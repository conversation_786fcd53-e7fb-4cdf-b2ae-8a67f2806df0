/// <reference types="multer" />
import { UploadsService } from './uploads.service';
export declare class UploadsController {
    private readonly uploadsService;
    constructor(uploadsService: UploadsService);
    uploadFiles(files: Array<Express.Multer.File>): Promise<import("./uploads.service").UploadedFile[]>;
    deleteFile(fileName: string): Promise<{
        message: string;
    }>;
    getFileUrl(fileName: string): Promise<{
        url: string;
    }>;
}
