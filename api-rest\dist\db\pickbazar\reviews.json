[{"id": 85, "order_id": null, "user_id": 4, "shop_id": 7, "product_id": 930, "variation_option_id": null, "comment": "Book is good but had to wait for a late delivery.", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-18T09:06:12.000000Z", "updated_at": "2022-03-18T09:06:12.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 930, "name": "Milan The Story of Love", "slug": "milan-the-story-of-love", "description": "The runes of Lyrical Ditties designedlyre-imagined the way poetry should sound\"By fitting to rhythmic arrangement a selection of the real language of men,\"<PERSON><PERSON> and his English coevals, similar as <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, wrote poetry that was meant to boil up from serious, reflective reflection over the commerce of humans with their terrain. Although numerous stress the notion of naturalness in Romantic poetry, the movement was still greatly concerned with the difficulty of composition and of rephrasing these feelings into lyrical form. Indeed, <PERSON><PERSON>, in his essay On Poesy or Art, sees art as “ the mediatress between, and jurist of nature and man”. Such an station reflects what might be called the dominant theme of English Romantic poetry the filtering of natural emotion through the mortal mind in order to produce meaning.", "type_id": 8, "price": 160, "shop_id": 7, "sale_price": 150, "language": "en", "min_price": 160, "max_price": 160, "sku": "+g+df5gda56f14dsa5f456sdf", "quantity": 500, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc", "height": null, "width": null, "length": null, "image": {"id": 1630, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1630/Romantic-Books.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1630/conversions/Romantic-Books-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-12-12T17:17:46.000000Z", "updated_at": "2022-01-02T08:50:19.000000Z", "author_id": 11, "manufacturer_id": 4, "is_digital": 1, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3, "total_reviews": 1, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 84, "order_id": null, "user_id": 4, "shop_id": 7, "product_id": 931, "variation_option_id": null, "comment": "For me its the book of the year.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T09:05:49.000000Z", "updated_at": "2022-03-18T09:05:49.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 931, "name": "Space Force First Chapter", "slug": "space-force-first-chapter", "description": "Science fabrication is a kidney of academic fabrication that generally deals with imaginative and futuristic generalities similar as advanced wisdom and technology, space disquisition, time trip, resemblant worlds, and extraterrestrial life.", "type_id": 8, "price": 200, "shop_id": 7, "sale_price": 180, "language": "en", "min_price": 200, "max_price": 200, "sku": "df5as+f5sda+f5s9+f5sda+5f9+sa5fsf+", "quantity": 500, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc", "height": null, "width": null, "length": null, "image": {"id": 1628, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1628/science-fiction-6.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1628/conversions/science-fiction-6-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-12-12T17:35:14.000000Z", "updated_at": "2022-01-02T08:44:46.000000Z", "author_id": 10, "manufacturer_id": 2, "is_digital": 1, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 83, "order_id": null, "user_id": 4, "shop_id": 7, "product_id": 932, "variation_option_id": null, "comment": "Too much fiction all over.. Over done every part.", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-18T09:05:31.000000Z", "updated_at": "2022-03-18T09:05:31.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 932, "name": "Space Force Second Chapter", "slug": "space-force-first-chapter-2", "description": "Science fabrication is a kidney of academic fabrication that generally deals with imaginative and futuristic generalities similar as advanced wisdom and technology, space disquisition, time trip, resemblant worlds, and extraterrestrial life.", "type_id": 8, "price": null, "shop_id": 7, "sale_price": null, "language": "en", "min_price": 150, "max_price": 152, "sku": null, "quantity": 1000, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc", "height": null, "width": null, "length": null, "image": {"id": 1629, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1629/science-fiction-7.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1629/conversions/science-fiction-7-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-12-12T17:35:14.000000Z", "updated_at": "2022-01-02T08:45:01.000000Z", "author_id": 10, "manufacturer_id": 2, "is_digital": 1, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 82, "order_id": null, "user_id": 4, "shop_id": 7, "product_id": 924, "variation_option_id": null, "comment": "Five star all the way. Love the story.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T09:05:08.000000Z", "updated_at": "2022-03-18T09:05:08.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 924, "name": "<PERSON><PERSON><PERSON>", "slug": "greddy-love", "description": "The runes of Lyrical Ditties designedlyre-imagined the way poetry should sound\"By fitting to rhythmic arrangement a selection of the real language of men,\"<PERSON><PERSON> and his English coevals, similar as <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, wrote poetry that was meant to boil up from serious, reflective reflection over the commerce of humans with their terrain. Although numerous stress the notion of naturalness in Romantic poetry, the movement was still greatly concerned with the difficulty of composition and of rephrasing these feelings into lyrical form. Indeed, <PERSON><PERSON>, in his essay On Poesy or Art, sees art as “ the mediatress between, and jurist of nature and man”. Such an station reflects what might be called the dominant theme of English Romantic poetry the filtering of natural emotion through the mortal mind in order to produce meaning.", "type_id": 8, "price": 180, "shop_id": 7, "sale_price": 150, "language": "en", "min_price": 180, "max_price": 180, "sku": "4g6g4d54fd6g54gd+++", "quantity": 500, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc", "height": null, "width": null, "length": null, "image": {"id": 1636, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1636/Romantic-Books-7.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1636/conversions/Romantic-Books-7-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-12-12T17:10:16.000000Z", "updated_at": "2022-01-02T08:51:51.000000Z", "author_id": 11, "manufacturer_id": 4, "is_digital": 1, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 81, "order_id": null, "user_id": 4, "shop_id": 1, "product_id": 416, "variation_option_id": null, "comment": "Okay with the price but wood is not of good quality", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-18T09:03:13.000000Z", "updated_at": "2022-03-18T09:03:13.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 416, "name": "Partex Coushoned Double Bed", "slug": "partex-coushoned-double-bed", "description": "A bed is a piece of furniture which is used as a place to sleep or relax.", "type_id": 6, "price": 270, "shop_id": 1, "sale_price": null, "language": "en", "min_price": 270, "max_price": 270, "sku": "2205", "quantity": 30, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "439", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/438/Partex.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/438/conversions/Partex-thumbnail.jpg"}, "video": null, "gallery": [{"id": "715", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/714/Bed-4.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/714/conversions/Bed-4-thumbnail.jpg"}, {"id": "716", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/715/Bed-5.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/715/conversions/Bed-5-thumbnail.jpg"}, {"id": "717", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/716/Bed-6.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/716/conversions/Bed-6-thumbnail.jpg"}, {"id": "784", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/783/Partex.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/783/conversions/Partex-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T07:53:46.000000Z", "updated_at": "2021-12-23T17:12:42.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3, "total_reviews": 1, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 80, "order_id": null, "user_id": 4, "shop_id": 1, "product_id": 414, "variation_option_id": null, "comment": "This is what you call a deluxe bed. luxury redefined.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T09:02:39.000000Z", "updated_at": "2022-03-18T09:02:39.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 414, "name": "Deluxe Mahagony Double Bed", "slug": "deluxe-mahagony-double-bed", "description": "A bed is a piece of furniture which is used as a place to sleep or relax.", "type_id": 6, "price": 300, "shop_id": 1, "sale_price": null, "language": "en", "min_price": 300, "max_price": 300, "sku": "2202", "quantity": 30, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "437", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/436/Mahogany.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/436/conversions/Mahogany-thumbnail.jpg"}, "video": null, "gallery": [{"id": "712", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/711/Bed-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/711/conversions/Bed-1-thumbnail.jpg"}, {"id": "713", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/712/Bed-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/712/conversions/Bed-2-thumbnail.jpg"}, {"id": "714", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/713/Bed-3.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/713/conversions/Bed-3-thumbnail.jpg"}, {"id": "781", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/780/Mahogany.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/780/conversions/Mahogany-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T07:51:38.000000Z", "updated_at": "2021-12-23T17:12:49.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 79, "order_id": null, "user_id": 4, "shop_id": 1, "product_id": 413, "variation_option_id": null, "comment": "Fantastic product.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T09:02:15.000000Z", "updated_at": "2022-03-18T09:02:15.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 413, "name": "Brown Hardwood Double Bed", "slug": "brown-hardwood-double-bed", "description": "A bed is a piece of furniture which is used as a place to sleep or relax.", "type_id": 6, "price": 250, "shop_id": 1, "sale_price": 220, "language": "en", "min_price": 250, "max_price": 250, "sku": "2201", "quantity": 30, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "436", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/435/Hardwoods.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/435/conversions/Hardwoods-thumbnail.jpg"}, "video": null, "gallery": [{"id": "709", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/708/Bed-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/708/conversions/Bed-1-thumbnail.jpg"}, {"id": "710", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/709/Bed-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/709/conversions/Bed-2-thumbnail.jpg"}, {"id": "711", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/710/Bed-3.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/710/conversions/Bed-3-thumbnail.jpg"}, {"id": "780", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/779/Hardwoods.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/779/conversions/Hardwoods-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T07:43:43.000000Z", "updated_at": "2021-12-23T17:12:54.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 78, "order_id": null, "user_id": 4, "shop_id": 1, "product_id": 412, "variation_option_id": null, "comment": "had to wait long time for delivery.", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-18T09:01:57.000000Z", "updated_at": "2022-03-18T09:01:57.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 412, "name": "Ash Double Bed", "slug": "ash-double-bed", "description": "A bed is a piece of furniture which is used as a place to sleep or relax.", "type_id": 6, "price": 250, "shop_id": 1, "sale_price": null, "language": "en", "min_price": 250, "max_price": 250, "sku": "2200", "quantity": 30, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "435", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/434/Ash.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/434/conversions/Ash-thumbnail.jpg"}, "video": null, "gallery": [{"id": "706", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/705/Bed-4.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/705/conversions/Bed-4-thumbnail.jpg"}, {"id": "707", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/706/Bed-5.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/706/conversions/Bed-5-thumbnail.jpg"}, {"id": "708", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/707/Bed-6.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/707/conversions/Bed-6-thumbnail.jpg"}, {"id": "779", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/778/Ash.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/778/conversions/Ash-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T07:42:44.000000Z", "updated_at": "2021-12-23T17:12:58.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 77, "order_id": null, "user_id": 4, "shop_id": 2, "product_id": 117, "variation_option_id": null, "comment": "Top notch product and really well tailored", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T09:00:46.000000Z", "updated_at": "2022-03-18T09:00:46.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 117, "name": "Solid Notch Lapel Single <PERSON><PERSON> Long Sleeve Blazer", "slug": "solid-notch-lapel-single-button-long-sleeve-blazer", "description": "Black bandhgala, has a mandarin collar, a full button placket, long sleeves, three pockets, double vented back hem, and has an attached lining", "type_id": 5, "price": null, "shop_id": 2, "sale_price": null, "language": "en", "min_price": 220, "max_price": 250, "sku": null, "quantity": 1000, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "119", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/119/Solid_Notch.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/119/conversions/Solid_Notch-thumbnail.jpg"}, "video": null, "gallery": [{"id": "695", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/694/Plain-Blazers-3.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/694/conversions/Plain-Blazers-3-thumbnail.jpg"}, {"id": "696", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/695/Plain-Blazers.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/695/conversions/Plain-Blazers-thumbnail.jpg"}, {"id": "775", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/774/Solid_Notch.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/774/conversions/Solid_Notch-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T18:07:17.000000Z", "updated_at": "2021-12-23T17:58:58.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 76, "order_id": null, "user_id": 4, "shop_id": 2, "product_id": 116, "variation_option_id": null, "comment": "Got the wrong size.", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-18T09:00:19.000000Z", "updated_at": "2022-03-18T09:00:19.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 116, "name": "Fold Over Collar Plain Blazers", "slug": "fold-over-collar-plain-blazers", "description": "Black bandhgala, has a mandarin collar, a full button placket, long sleeves, three pockets, double vented back hem, and has an attached lining", "type_id": 5, "price": null, "shop_id": 2, "sale_price": null, "language": "en", "min_price": 199, "max_price": 200, "sku": null, "quantity": 1000, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "117", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/117/Fold_over.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/117/conversions/Fold_over-thumbnail.jpg"}, "video": null, "gallery": [{"id": "692", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/691/Plain-Blazers-4.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/691/conversions/Plain-Blazers-4-thumbnail.jpg"}, {"id": "693", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/692/Plain-Blazers-5.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/692/conversions/Plain-Blazers-5-thumbnail.jpg"}, {"id": "774", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/773/Fold_over.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/773/conversions/Fold_over-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T18:04:53.000000Z", "updated_at": "2021-12-23T17:59:02.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3, "total_reviews": 1, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 75, "order_id": null, "user_id": 4, "shop_id": 2, "product_id": 113, "variation_option_id": null, "comment": "The cloth quality was not good though the finishing was good.", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-18T09:00:03.000000Z", "updated_at": "2022-03-18T09:00:03.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 113, "name": "Forever 21 Solid Bodycon Midi Dress", "slug": "forever-21-solid-bodycon-midi-dress", "description": "Grey solid woven bodycon dress, has a round neck, sleeveless, straight hem", "type_id": 5, "price": null, "shop_id": 2, "sale_price": null, "language": "en", "min_price": 100, "max_price": 120, "sku": null, "quantity": 1000, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "115", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/115/FOREVER_21.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/115/conversions/FOREVER_21-thumbnail.jpg"}, "video": null, "gallery": [{"id": "672", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/671/Striped-Dress.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/671/conversions/Striped-Dress-thumbnail.jpg"}, {"id": "768", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/767/FOREVER_21.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/767/conversions/FOREVER_21-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T18:01:23.000000Z", "updated_at": "2021-12-23T18:01:24.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3, "total_reviews": 1, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 74, "order_id": null, "user_id": 4, "shop_id": 2, "product_id": 111, "variation_option_id": null, "comment": "Finished to perfection. Cloth quality is good as well.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:59:30.000000Z", "updated_at": "2022-03-18T08:59:30.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 111, "name": "Mango Self Striped A Line Dress", "slug": "mango-self-striped-a-line-dress", "description": "Off-White self-striped knitted midi A-line dress, has a scoop neck, sleeveless, straight hem", "type_id": 5, "price": null, "shop_id": 2, "sale_price": null, "language": "en", "min_price": 70, "max_price": 81, "sku": null, "quantity": 1000, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "112", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/112/mango.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/112/conversions/mango-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:56:41.000000Z", "updated_at": "2021-12-23T18:01:30.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 73, "order_id": null, "user_id": 4, "shop_id": 2, "product_id": 110, "variation_option_id": null, "comment": "Gave me the wrong color. Wanted an exchange but could not get it.", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:59:04.000000Z", "updated_at": "2022-03-18T08:59:04.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 110, "name": "Magnetic Designs Women Printed Fit And Flare Dress", "slug": "magnetic-designs-women-printed-fit-and-flare-dress", "description": "Mauve printed knitted fit and flare dress, has a round neck, three-quarter sleeves, concealed zip closure,, flared hem", "type_id": 5, "price": null, "shop_id": 2, "sale_price": null, "language": "en", "min_price": 35, "max_price": 35, "sku": null, "quantity": 1000, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "111", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/111/magnetic.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/111/conversions/magnetic-thumbnail.jpg"}, "video": null, "gallery": [{"id": "668", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/667/Printed-Dress-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/667/conversions/Printed-Dress-2-thumbnail.jpg"}, {"id": "669", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/668/Printed-Dress.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/668/conversions/Printed-Dress-thumbnail.jpg"}, {"id": "767", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/766/magnetic.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/766/conversions/magnetic-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T17:55:02.000000Z", "updated_at": "2021-12-23T18:01:35.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 72, "order_id": null, "user_id": 4, "shop_id": 3, "product_id": 107, "variation_option_id": null, "comment": "This is the best silver purse you will ever buy.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:57:33.000000Z", "updated_at": "2022-03-18T08:57:33.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 107, "name": "Armani Silver Purse", "slug": "armani-silver-purse", "description": "The name <PERSON> has become synonymous with clean lines and Italian style. One of the most recognisable fashion houses in the world, the label has dressed some of the world’s most beautiful women.", "type_id": 4, "price": 120, "shop_id": 3, "sale_price": null, "language": "en", "min_price": 120, "max_price": 120, "sku": "2006", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "108", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/108/91PirQjxGjL._UL1500_.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/108/conversions/91PirQjxGjL._UL1500_-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:40:49.000000Z", "updated_at": "2021-12-23T18:01:44.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 71, "order_id": null, "user_id": 4, "shop_id": 3, "product_id": 106, "variation_option_id": null, "comment": "I love the red Gucci Purse.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:57:11.000000Z", "updated_at": "2022-03-18T08:57:11.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 106, "name": "<PERSON><PERSON>", "slug": "gucci-purse", "description": "Luxury Italian fashion house Gucci is renowned for its instantly recognisable bags and accessories, infusing its unique sense of quality and exquisite design into each piece. This pink logo print leather backpack from Gucci features top handles, a drawstring fastening, a pebbled leather texture, a removable zipped pouch and a vintage Gucci logo.", "type_id": 4, "price": 90, "shop_id": 3, "sale_price": null, "language": "en", "min_price": 90, "max_price": 90, "sku": "2005", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "107", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/107/gucci_purse.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/107/conversions/gucci_purse-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:23:18.000000Z", "updated_at": "2021-12-23T18:01:47.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 70, "order_id": null, "user_id": 4, "shop_id": 3, "product_id": 105, "variation_option_id": null, "comment": "Loved this one all the way. <PERSON>mooth<PERSON> finished.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:55:38.000000Z", "updated_at": "2022-03-18T08:55:38.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 105, "name": "Givenchy Mini Purse", "slug": "givenchy-mini-purse", "description": "Luxury Italian fashion house Gucci is renowned for its instantly recognisable bags and accessories, infusing its unique sense of quality and exquisite design into each piece. This pink logo print leather backpack from Gucci features top handles, a drawstring fastening, a pebbled leather texture, a removable zipped pouch and a vintage Gucci logo.", "type_id": 4, "price": 80, "shop_id": 3, "sale_price": 70, "language": "en", "min_price": 80, "max_price": 80, "sku": "2003", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "106", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/106/gucci_mini_purse.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/106/conversions/gucci_mini_purse-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:19:22.000000Z", "updated_at": "2021-12-23T18:01:50.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 69, "order_id": null, "user_id": 4, "shop_id": 3, "product_id": 104, "variation_option_id": null, "comment": "This is worth every penny.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:55:12.000000Z", "updated_at": "2022-03-18T08:55:12.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 104, "name": "Givenchy Purse", "slug": "givenchy-purse", "description": "Established in 1952, Givenchy's stance on contemporary elegance is perfectly captured through the brand’s premium accessory collections. Crafted from calf leather, this grey GV3 croc-effect shoulder bag from Givenchy features a chain top handle with logo charm, a detachable shoulder strap, a front flap closure, a metal logo plaque to the front, gold-tone hardware and suede panels.", "type_id": 4, "price": 75, "shop_id": 3, "sale_price": 60, "language": "en", "min_price": 75, "max_price": 75, "sku": "2002", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "105", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/105/givency_purse.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/105/conversions/givency_purse-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:18:11.000000Z", "updated_at": "2021-12-23T18:01:53.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 68, "order_id": null, "user_id": 4, "shop_id": 3, "product_id": 103, "variation_option_id": null, "comment": "Genuine product superfast delivery.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:52:06.000000Z", "updated_at": "2022-03-18T08:52:06.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 103, "name": "<PERSON><PERSON>", "slug": "armani-purse", "description": "Black logo embossed messenger bag from <PERSON> featuring an adjustable shoulder strap, a top zip fastening and a front zip pocket.", "type_id": 4, "price": 80, "shop_id": 3, "sale_price": 72, "language": "en", "min_price": 80, "max_price": 80, "sku": "2002", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "104", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/104/Armani_purse.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/104/conversions/Armani_purse-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:16:29.000000Z", "updated_at": "2021-12-23T18:01:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 67, "order_id": null, "user_id": 4, "shop_id": 3, "product_id": 102, "variation_option_id": null, "comment": "Don't buy this is the duplicate one", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:50:27.000000Z", "updated_at": "2022-03-18T08:50:27.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 102, "name": "<PERSON><PERSON>", "slug": "armani-leather-purse", "description": "The name <PERSON> has become synonymous with clean lines and Italian style. One of the most recognisable fashion houses in the world, the label has dressed some of the world’s most beautiful women.", "type_id": 4, "price": 50, "shop_id": 3, "sale_price": 40, "language": "en", "min_price": 50, "max_price": 50, "sku": "2001", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "103", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/103/Armani_leather_purse.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/103/conversions/Armani_leather_purse-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:12:48.000000Z", "updated_at": "2021-12-23T18:01:58.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 66, "order_id": null, "user_id": 5, "shop_id": 4, "product_id": 26, "variation_option_id": null, "comment": "Really good shades of foundation.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:41:42.000000Z", "updated_at": "2022-03-18T08:41:42.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 26, "name": "Ex1 Invisiwear Liquid Foundation", "slug": "ex1-invisiwear-liquid-foundation", "description": "A lightunit and luminous liquid base formulated with light diffusers to unify skin tone and effortlessly cover imperfections. Specially designed ‘true colour’ pigments work perfectly with your skins’ natural tones in an ultra-blendable formula.", "type_id": 3, "price": 22, "shop_id": 4, "sale_price": 18, "language": "en", "min_price": 22, "max_price": 22, "sku": "1006", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "27", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/27/EX1.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/27/conversions/EX1-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T06:33:28.000000Z", "updated_at": "2021-12-23T18:14:12.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 65, "order_id": null, "user_id": 5, "shop_id": 4, "product_id": 25, "variation_option_id": null, "comment": "the worst product you will ever buy.", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:41:20.000000Z", "updated_at": "2022-03-18T08:41:20.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 25, "name": "<PERSON><PERSON>ins Everlasting Compact Foundation", "slug": "clarins-everlasting-compact-foundation", "description": "A 15-hour matte finish your skin will feel good about! <PERSON><PERSON><PERSON>' long-wearing compact foundation evens out skin tone and minimizes the look of imperfections in seconds, delivering a shine-free, matte finish and hours of comfortable wear. Ultra-fine texture resists heat, humidity and perspiration for flawless coverage that lasts throughout the day.", "type_id": 3, "price": 54, "shop_id": 4, "sale_price": null, "language": "en", "min_price": 54, "max_price": 54, "sku": "1005", "quantity": 60, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "26", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/26/Clarins.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/26/conversions/Clar<PERSON>-thumbnail.jpg"}, "video": null, "gallery": [{"id": "656", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/655/Bour<PERSON>is-Little-Round-Pot-Blusher-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/655/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-1-thumbnail.jpg"}, {"id": "657", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/656/Bour<PERSON>is-Little-Round-Pot-Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/656/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-2-thumbnail.jpg"}, {"id": "660", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/659/Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/659/conversions/Blusher-2-thumbnail.jpg"}, {"id": "764", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/763/Clarins.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/763/conversions/Clar<PERSON>-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T06:31:30.000000Z", "updated_at": "2021-12-23T18:14:14.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 64, "order_id": null, "user_id": 5, "shop_id": 4, "product_id": 24, "variation_option_id": null, "comment": "Genuine product you can buy it with your eyes closed.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:40:55.000000Z", "updated_at": "2022-03-18T08:40:55.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 24, "name": "<PERSON>or Backstage Contour Palette", "slug": "dior-backstage-contour-palette", "description": "An easy-to-use, six-well contour kit with pigment-packed, blendable highlighter, bronzer, and blush powders. Use these versatile shades to create an effortlessly lifted neutral look or a warm, just-cruised-down-the-coast glow. It features three matte and two pearlescent powders to shape, bronze, and highlight. It also includes one matte blush to add a youthful flush of subtle color to any look. It is formulated Without: - Parabens- Phthalates This laid-back makeup palette makes it easy to add warmth and dimension to your look. Customize your signature Cali glow using six neutrals, including two of Smashbox's bestselling contour shades. This product is cruelty-free and formulated without parabens, phthalates, fragrance.", "type_id": 3, "price": 44, "shop_id": 4, "sale_price": null, "language": "en", "min_price": 44, "max_price": 44, "sku": "1004", "quantity": 45, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "25", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/25/DIOR.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/25/conversions/DIOR-thumbnail.jpg"}, "video": null, "gallery": [{"id": "647", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/646/Contour-Palette-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/646/conversions/Contour-Palette-1-thumbnail.jpg"}, {"id": "648", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/647/Contour-Palette-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/647/conversions/Contour-Palette-2-thumbnail.jpg"}, {"id": "649", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/648/Contour-Palette.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/648/conversions/Contour-Palette-thumbnail.jpg"}, {"id": "760", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/759/DIOR.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/759/conversions/DIOR-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T06:28:58.000000Z", "updated_at": "2021-12-23T18:14:17.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 63, "order_id": null, "user_id": 5, "shop_id": 4, "product_id": 23, "variation_option_id": null, "comment": "The cheap product was not worth this price. Definitely not recommending it.", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:40:32.000000Z", "updated_at": "2022-03-18T08:40:32.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 23, "name": "Smashbox The Cali Contour Palette", "slug": "smashbox-the-cali-contour-palette", "description": "An easy-to-use, six-well contour kit with pigment-packed, blendable highlighter, bronzer, and blush powders. Use these versatile shades to create an effortlessly lifted neutral look or a warm, just-cruised-down-the-coast glow. It features three matte and two pearlescent powders to shape, bronze, and highlight. It also includes one matte blush to add a youthful flush of subtle color to any look. It is formulated Without: - Parabens- Phthalates This laid-back makeup palette makes it easy to add warmth and dimension to your look. Customize your signature Cali glow using six neutrals, including two of Smashbox's bestselling contour shades. This product is cruelty-free and formulated without parabens, phthalates, fragrance.", "type_id": 3, "price": 42, "shop_id": 4, "sale_price": 38.59, "language": "en", "min_price": 42, "max_price": 42, "sku": "1003", "quantity": 30, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "24", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/24/Smashbox.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/24/conversions/Smashbox-thumbnail.jpg"}, "video": null, "gallery": [{"id": "644", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/643/Blusher-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/643/conversions/Blusher-1-thumbnail.jpg"}, {"id": "645", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/644/Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/644/conversions/Blusher-2-thumbnail.jpg"}, {"id": "646", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/645/Blusher.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/645/conversions/Blusher-thumbnail.jpg"}, {"id": "759", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/758/Smashbox.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/758/conversions/Smashbox-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T06:26:00.000000Z", "updated_at": "2021-12-23T18:14:20.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 62, "order_id": null, "user_id": 5, "shop_id": 4, "product_id": 22, "variation_option_id": null, "comment": "Decent product and delivery was fast as well", "rating": 4, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:39:57.000000Z", "updated_at": "2022-03-18T08:39:57.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 22, "name": "Cyo Crush On Blush Powder Blush", "slug": "cyo-crush-on-blush-powder-blush", "description": "<PERSON><PERSON><PERSON><PERSON> Little Round Pot Blusher has been keeping women beautiful for generations. Made in an exclusive baked technology process, its incredibly transparent & light texture formula is easy to apply and blends impeccably.", "type_id": 3, "price": 11, "shop_id": 4, "sale_price": 8, "language": "en", "min_price": 11, "max_price": 11, "sku": "1002", "quantity": 60, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "23", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/23/CYO.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/23/conversions/CYO-thumbnail.jpg"}, "video": null, "gallery": [{"id": "640", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/639/Bour<PERSON>is-Little-Round-Pot-Blusher-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/639/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-1-thumbnail.jpg"}, {"id": "641", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/640/Bour<PERSON>is-Little-Round-Pot-Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/640/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-2-thumbnail.jpg"}, {"id": "643", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/642/Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/642/conversions/Blusher-2-thumbnail.jpg"}, {"id": "758", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/757/CYO.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/757/conversions/CYO-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T06:23:13.000000Z", "updated_at": "2021-12-23T18:14:22.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4, "total_reviews": 1, "rating_count": [{"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 61, "order_id": null, "user_id": 5, "shop_id": 4, "product_id": 21, "variation_option_id": null, "comment": "Really good quality product at this price.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:32:42.000000Z", "updated_at": "2022-03-18T08:32:42.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 21, "name": "<PERSON><PERSON><PERSON><PERSON> Little Round Pot Blusher", "slug": "bourjois-little-round-pot-blusher", "description": "<PERSON><PERSON><PERSON><PERSON> Little Round Pot Blusher has been keeping women beautiful for generations. Made in an exclusive baked technology process, its incredibly transparent & light texture formula is easy to apply and blends impeccably.", "type_id": 3, "price": 9, "shop_id": 4, "sale_price": 8, "language": "en", "min_price": 9, "max_price": 9, "sku": "1001", "quantity": 49, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "22", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/22/Bourjois.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/22/conversions/Bo<PERSON><PERSON><PERSON>-thumbnail.jpg"}, "video": null, "gallery": [{"id": "637", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/636/Bour<PERSON>is-Little-Round-Pot-Blusher-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/636/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-1-thumbnail.jpg"}, {"id": "638", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/637/Bour<PERSON>is-Little-Round-Pot-Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/637/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-2-thumbnail.jpg"}, {"id": "639", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/638/Bour<PERSON>is-Little-Round-Pot-Blusher.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/638/conversions/<PERSON><PERSON><PERSON><PERSON>-<PERSON>-Round-Pot-<PERSON>sher-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T06:00:30.000000Z", "updated_at": "2021-12-23T18:14:25.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 60, "order_id": null, "user_id": 5, "shop_id": 5, "product_id": 503, "variation_option_id": null, "comment": "That's what you call a traditional English muffin.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:26:28.000000Z", "updated_at": "2022-03-18T08:26:28.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 503, "name": "Thomas Cinnamon Raisin English Muffins", "slug": "thomas-cinnamon-raisin-english-muffins", "description": "griddle and a cupcake-like quickbread that is chemically leavened and then baked in a mold.", "type_id": 2, "price": 1, "shop_id": 5, "sale_price": null, "language": "en", "min_price": 1, "max_price": 1, "sku": "3100", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "527", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/526/Muffin-5_jbx3ok.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/526/conversions/Muffin-5_jbx3ok-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-14T10:05:16.000000Z", "updated_at": "2021-12-23T17:02:49.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 59, "order_id": null, "user_id": 5, "shop_id": 5, "product_id": 504, "variation_option_id": null, "comment": "Really fresh and well-baked muffins", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:25:29.000000Z", "updated_at": "2022-03-18T08:25:29.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 504, "name": "Mini Zucchini & Carrot Muffin", "slug": "mini-zucchini-carrot-muffin", "description": "griddle and a cupcake-like quickbread that is chemically leavened and then baked in a mold.", "type_id": 2, "price": 1, "shop_id": 5, "sale_price": 0.75, "language": "en", "min_price": 1, "max_price": 1, "sku": "3101", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "528", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/527/Muffin-2_o6lhy8.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/527/conversions/Muffin-2_o6lhy8-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-14T10:06:24.000000Z", "updated_at": "2021-12-23T17:02:46.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 58, "order_id": null, "user_id": 5, "shop_id": 5, "product_id": 499, "variation_option_id": null, "comment": "Chocolate part was good but the raspberry within tasted aweful", "rating": 4, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:20:04.000000Z", "updated_at": "2022-03-18T08:20:04.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 499, "name": "Raspberry Cream Cheese Coffee Cake", "slug": "raspberry-cream-cheese-coffee-cake", "description": "Cake is a form of sweet food made from flour, sugar, and other ingredients, that is usually baked. In their oldest forms, cakes were modifications of bread, but cakes now cover a wide range of preparations", "type_id": 2, "price": 2.2, "shop_id": 5, "sale_price": null, "language": "en", "min_price": 2.2, "max_price": 2.2, "sku": "3091", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "523", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/522/Pice_Cake-5_dcbcn7.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/522/conversions/Pice_Cake-5_dcbcn7-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-14T09:57:57.000000Z", "updated_at": "2021-12-23T17:02:58.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4, "total_reviews": 1, "rating_count": [{"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 57, "order_id": null, "user_id": 5, "shop_id": 5, "product_id": 498, "variation_option_id": null, "comment": "Decent product but delivery was way too late.", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:19:19.000000Z", "updated_at": "2022-03-18T08:19:19.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 498, "name": "<PERSON> Cake Mix, Chocolate", "slug": "miss-jones-cake-mix-chocolate", "description": "Cake is a form of sweet food made from flour, sugar, and other ingredients, that is usually baked. In their oldest forms, cakes were modifications of bread, but cakes now cover a wide range of preparations", "type_id": 2, "price": 1.5, "shop_id": 5, "sale_price": null, "language": "en", "min_price": 1.5, "max_price": 1.5, "sku": "3090", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "522", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/521/Pice_Cake-4_ptz2r5.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/521/conversions/Pice_Cake-4_ptz2r5-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-14T09:56:43.000000Z", "updated_at": "2021-12-23T17:04:00.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3, "total_reviews": 1, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 56, "order_id": null, "user_id": 5, "shop_id": 5, "product_id": 493, "variation_option_id": null, "comment": "Good and tasty garlic breads.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:18:59.000000Z", "updated_at": "2022-03-18T08:18:59.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 493, "name": "<PERSON><PERSON><PERSON> Filled <PERSON><PERSON>read", "slug": "garlic-filled-pita-bread", "description": "Pita or pitta, is a family of yeast-leavened round flatbreads baked from wheat flour, common in the Mediterranean, Middle East, and neighboring areas", "type_id": 2, "price": 2, "shop_id": 5, "sale_price": 1.8, "language": "en", "min_price": 2, "max_price": 2, "sku": "3080", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "5 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "517", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/516/Pita_Bread-6_whabjs.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/516/conversions/Pita_Bread-6_whabjs-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-14T09:50:40.000000Z", "updated_at": "2021-12-23T17:04:20.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 55, "order_id": null, "user_id": 5, "shop_id": 5, "product_id": 492, "variation_option_id": null, "comment": "Fresh and soft bread.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:16:48.000000Z", "updated_at": "2022-03-18T08:16:48.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 492, "name": "Al Shams Pita Bread, 10 pcs", "slug": "al-shams-pita-bread-10-pcs", "description": "Pita or pitta, is a family of yeast-leavened round flatbreads baked from wheat flour, common in the Mediterranean, Middle East, and neighboring areas", "type_id": 2, "price": 2, "shop_id": 5, "sale_price": null, "language": "en", "min_price": 2, "max_price": 2, "sku": "3080", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "10 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "516", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/515/Pita_Bread-8_asbled.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/515/conversions/Pita_Bread-8_asbled-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-14T09:49:19.000000Z", "updated_at": "2021-12-23T17:04:22.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 54, "order_id": null, "user_id": 5, "shop_id": 5, "product_id": 473, "variation_option_id": null, "comment": "Worst green tea I have ever had.", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:14:58.000000Z", "updated_at": "2022-03-18T08:14:58.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 473, "name": "Freshly Brewed Organic Green Tea", "slug": "freshly-brewed-organic-green-tea", "description": "Coffee tea refers to herbal tea made from non-bean parts of the coffea (coffee plant), and may refer to: Coffee-leaf tea · Coffee cherry tea. Ground coffee, brewed", "type_id": 2, "price": 0.5, "shop_id": 5, "sale_price": null, "language": "en", "min_price": 0.5, "max_price": 0.5, "sku": "3007", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "496", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/495/Coffee_Tea_oo00oz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/495/conversions/Coffee_Tea_oo00oz-thumbnail.jpg"}, "video": null, "gallery": [{"id": "811", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/809/Coffee_Tea_oo00oz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/809/conversions/Coffee_Tea_oo00oz-thumbnail.jpg"}, {"id": "815", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/813/Coffee_Tea-1_rnxxwg.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/813/conversions/Coffee_Tea-1_rnxxwg-thumbnail.jpg"}, {"id": "816", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/814/Coffee_Tea_oo00oz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/814/conversions/Coffee_Tea_oo00oz-thumbnail.jpg"}, {"id": "817", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/815/Coffee_Tea-1_rnxxwg.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/815/conversions/Coffee_Tea-1_rnxxwg-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T09:20:08.000000Z", "updated_at": "2021-12-23T17:06:35.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 53, "order_id": null, "user_id": 5, "shop_id": 5, "product_id": 486, "variation_option_id": null, "comment": "That's what you call a signature cake. Really soft and tasty.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:14:34.000000Z", "updated_at": "2022-03-18T08:14:34.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 486, "name": "Signature Chocolate Cake Iced Fudge 7 oz", "slug": "signature-chocolate-cake-iced-fudge-7-oz", "description": "Cake is a form of sweet food made from flour, sugar, and other ingredients, that is usually baked. In their oldest forms, cakes were modifications of bread, but cakes now cover a wide range of preparations", "type_id": 2, "price": 11.75, "shop_id": 5, "sale_price": null, "language": "en", "min_price": 11.75, "max_price": 11.75, "sku": "3064", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "7oz", "height": null, "width": null, "length": null, "image": {"id": "509", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/508/Round_Cake-8_rjbmjc.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/508/conversions/Round_Cake-8_rjbmjc-thumbnail.jpg"}, "video": null, "gallery": [{"id": "847", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/845/Round_Cake-8_rjbmjc.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/845/conversions/Round_Cake-8_rjbmjc-thumbnail.jpg"}, {"id": "848", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/846/Round_Cake-5_hpfbrl.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/846/conversions/Round_Cake-5_hpfbrl-thumbnail.jpg"}, {"id": "849", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/847/Round_Cake_on61hh.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/847/conversions/Round_Cake_on61hh-thumbnail.jpg"}, {"id": "850", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/848/Round_Cake-3_pigscm.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/848/conversions/Round_Cake-3_pigscm-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T09:41:12.000000Z", "updated_at": "2021-12-23T17:04:42.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 52, "order_id": null, "user_id": 5, "shop_id": 5, "product_id": 469, "variation_option_id": null, "comment": "Delivery was fast but taste was not good.", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:14:06.000000Z", "updated_at": "2022-03-18T08:14:06.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 469, "name": "Kirkland Organic Lemonade, 150 ml", "slug": "kirkland-organic-lemonade-150-ml", "description": "Juice is a drink made from the extraction or pressing of the natural liquid contained in fruit and vegetables. It can also refer to liquids that are flavored with concentrate or other biological food sources, such as meat or seafood, such as clam juice..", "type_id": 2, "price": 1.5, "shop_id": 5, "sale_price": null, "language": "en", "min_price": 1.5, "max_price": 1.5, "sku": "3002", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "492", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/491/Juice4_1_xt54si.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/491/conversions/Juice4_1_xt54si-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-14T09:15:17.000000Z", "updated_at": "2021-12-23T17:06:49.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3, "total_reviews": 1, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 51, "order_id": null, "user_id": 5, "shop_id": 5, "product_id": 468, "variation_option_id": null, "comment": "Good product and fast delivery.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:13:50.000000Z", "updated_at": "2022-03-18T08:13:50.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 468, "name": "Wonderful Pomegranate Juice, 300 ml", "slug": "wonderful-pomegranate-juice-300-ml", "description": "Juice is a drink made from the extraction or pressing of the natural liquid contained in fruit and vegetables. It can also refer to liquids that are flavored with concentrate or other biological food sources, such as meat or seafood, such as clam juice", "type_id": 2, "price": 3, "shop_id": 5, "sale_price": 2.4, "language": "en", "min_price": 3, "max_price": 3, "sku": "3001", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "491", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/490/Juice5_bz8od4.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/490/conversions/Juice5_bz8od4-thumbnail.jpg"}, "video": null, "gallery": [{"id": "807", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/805/Juice5_bz8od4.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/805/conversions/Juice5_bz8od4-thumbnail.jpg"}, {"id": "808", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/806/Juice5_bz8od4.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/806/conversions/Juice5_bz8od4-thumbnail.jpg"}, {"id": "809", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/807/Juice5_bz8od4.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/807/conversions/Juice5_bz8od4-thumbnail.jpg"}, {"id": "810", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/808/Juice5_bz8od4.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/808/conversions/Juice5_bz8od4-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T09:14:15.000000Z", "updated_at": "2021-12-23T17:06:52.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 50, "order_id": null, "user_id": 5, "shop_id": 5, "product_id": 467, "variation_option_id": null, "comment": "Really tasty and yummy. Low suger.", "rating": 4, "photos": [], "deleted_at": null, "created_at": "2022-03-18T08:13:18.000000Z", "updated_at": "2022-03-18T08:13:18.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 467, "name": "Ritai Organic Orange Juice 500 ml", "slug": "ritai-organic-orange-juice-500-ml", "description": "Juice is a drink made from the extraction or pressing of the natural liquid contained in fruit and vegetables. It can also refer to liquids that are flavored with concentrate or other biological food sources, such as meat or seafood, such as clam juice", "type_id": 2, "price": 1.8, "shop_id": 5, "sale_price": null, "language": "en", "min_price": 1.8, "max_price": 1.8, "sku": "3000", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "490", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/489/Juice-5_eqrtuu.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/489/conversions/Juice-5_eqrtuu-thumbnail.jpg"}, "video": null, "gallery": [{"id": "605", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/604/Organic-orange-juice-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/604/conversions/Organic-orange-juice-1-thumbnail.jpg"}, {"id": "606", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/605/Organic-orange-juice.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/605/conversions/Organic-orange-juice-thumbnail.jpg"}, {"id": "795", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/793/Juice-5_eqrtuu.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/793/conversions/Juice-5_eqrtuu-thumbnail.jpg"}, {"id": "796", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/794/Juice-1_lx8xnf.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/794/conversions/Juice-1_lx8xnf-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T09:13:11.000000Z", "updated_at": "2021-12-23T17:06:55.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4, "total_reviews": 1, "rating_count": [{"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 49, "order_id": null, "user_id": 5, "shop_id": 6, "product_id": 10, "variation_option_id": null, "comment": "Had to wait 3 days for delivery.", "rating": 2, "photos": [], "deleted_at": null, "created_at": "2022-03-17T17:02:53.000000Z", "updated_at": "2022-03-17T17:02:53.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 10, "name": "French Green Beans", "slug": "french-green-beans", "description": "Green beans are the unripe, young fruit and protective pods of various cultivars of the common bean. Immature or young pods of the runner bean, yardlong bean, and hyacinth bean are used in a similar way.", "type_id": 1, "price": 1.5, "shop_id": 6, "sale_price": 1.2, "language": "en", "min_price": 1.5, "max_price": 1.5, "sku": "10", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "0.5lb", "height": null, "width": null, "length": null, "image": {"id": "11", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/11/FrenchGreenBeans.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/11/conversions/FrenchGreenBeans-thumbnail.jpg"}, "video": null, "gallery": [{"id": "602", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/601/French-Green-Beans-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/601/conversions/French-Green-Beans-1-thumbnail.jpg"}, {"id": "603", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/602/French-Green-Beans-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/602/conversions/French-Green-Beans-2-thumbnail.jpg"}, {"id": "604", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/603/French-Green-Beans.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/603/conversions/French-Green-Beans-thumbnail.jpg"}, {"id": "747", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/746/FrenchGreenBeans_azivow.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/746/conversions/FrenchGreenBeans_azivow-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:51:28.000000Z", "updated_at": "2021-12-23T18:15:40.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3.33, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 48, "order_id": null, "user_id": 5, "shop_id": 6, "product_id": 9, "variation_option_id": null, "comment": "Decent quality", "rating": 4, "photos": [], "deleted_at": null, "created_at": "2022-03-17T17:02:36.000000Z", "updated_at": "2022-03-17T17:02:36.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 9, "name": "Dates", "slug": "dates", "description": "Phoenix dactylifera, commonly known as date or date palm, is a flowering plant species in the palm family, Arecaceae, cultivated for its edible sweet fruit.", "type_id": 1, "price": 10, "shop_id": 6, "sale_price": 8, "language": "en", "min_price": 10, "max_price": 10, "sku": "9", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1.5lb", "height": null, "width": null, "length": null, "image": {"id": "10", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/10/Dates.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/10/conversions/Dates-thumbnail.jpg"}, "video": null, "gallery": [{"id": "599", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/598/Dates-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/598/conversions/Dates-1-thumbnail.jpg"}, {"id": "600", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/599/Dates-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/599/conversions/Dates-2-thumbnail.jpg"}, {"id": "601", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/600/Dates.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/600/conversions/Dates-thumbnail.jpg"}, {"id": "746", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/745/Dates_pq4oad.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/745/conversions/Dates_pq4oad-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:50:41.000000Z", "updated_at": "2021-12-23T18:15:43.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 47, "order_id": null, "user_id": 5, "shop_id": 6, "product_id": 8, "variation_option_id": null, "comment": "good product.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T17:02:24.000000Z", "updated_at": "2022-03-17T17:02:24.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 8, "name": "<PERSON><PERSON><PERSON>ber", "slug": "cucumber", "description": "Cucumber is a widely cultivated plant in the gourd family, Cucurbitaceae. It is a creeping vine that bears cucumiform fruits that are used as vegetables. There are three main varieties of cucumber: slicing, pickling, and seedless. Within these varieties, several cultivars have been created.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "8", "quantity": 25, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "2.5lb", "height": null, "width": null, "length": null, "image": {"id": "8", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/8/Cucumber.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/8/conversions/Cucumber-thumbnail.jpg"}, "video": null, "gallery": [{"id": "596", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/595/Cucumber-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/595/conversions/Cucumber-1-thumbnail.jpg"}, {"id": "597", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/596/Cucumber-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/596/conversions/Cucumber-2-thumbnail.jpg"}, {"id": "598", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/597/Cucumber.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/597/conversions/Cucumber-thumbnail.jpg"}, {"id": "745", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/744/Cucumber_w6hlxr.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/744/conversions/Cucumber_w6hlxr-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:49:18.000000Z", "updated_at": "2021-12-23T18:15:46.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 46, "order_id": null, "user_id": 5, "shop_id": 6, "product_id": 7, "variation_option_id": null, "comment": "Good fresh quality produce.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T17:02:11.000000Z", "updated_at": "2022-03-17T17:02:11.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 7, "name": "Sweet Corn", "slug": "sweet-corn", "description": "Maize, also known as corn, is a cereal grain first domesticated by indigenous peoples in southern Mexico about 10,000 years ago. The leafy stalk of the plant produces pollen inflorescences and separate ovuliferous inflorescences called ears that yield kernels or seeds, which are fruits.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 4, "language": "en", "min_price": 5, "max_price": 5, "sku": "7", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "7", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/7/Corn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/7/conversions/Corn-thumbnail.jpg"}, "video": null, "gallery": [{"id": "593", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/592/Sweet-Corn-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/592/conversions/Sweet-Corn-1-thumbnail.jpg"}, {"id": "594", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/593/Sweet-Corn-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/593/conversions/Sweet-Corn-2-thumbnail.jpg"}, {"id": "595", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/594/Sweet-Corn.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/594/conversions/Sweet-Corn-thumbnail.jpg"}, {"id": "744", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/743/Corn_dlrtbv.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/743/conversions/Corn_dlrtbv-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:48:20.000000Z", "updated_at": "2021-12-23T18:15:49.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 45, "order_id": null, "user_id": 5, "shop_id": 6, "product_id": 6, "variation_option_id": null, "comment": "wanted the sweet ones got the sour ones and delivery was late as well.", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-17T17:01:56.000000Z", "updated_at": "2022-03-17T17:01:56.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 6, "name": "Clementines", "slug": "clementines", "description": "clementine is a tangor, a citrus fruit hybrid between a willowleaf mandarin orange and a sweet orange, named for its late 19th-century discoverer. The exterior is a deep orange colour with a smooth, glossy appearance.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": 2.5, "language": "en", "min_price": 3, "max_price": 3, "sku": "6", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "6", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/6/clementines.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/6/conversions/clementines-thumbnail.jpg"}, "video": null, "gallery": [{"id": "590", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/589/Clementines-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/589/conversions/Clementines-1-thumbnail.jpg"}, {"id": "591", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/590/Clementines.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/590/conversions/Clementines-thumbnail.jpg"}, {"id": "592", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/591/Clementines-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/591/conversions/Clementines-2-thumbnail.jpg"}, {"id": "743", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/742/clementines_h74qrp.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/742/conversions/clementines_h74qrp-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:45:18.000000Z", "updated_at": "2021-12-23T18:15:52.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 2, "total_reviews": 3, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 44, "order_id": null, "user_id": 5, "shop_id": 6, "product_id": 5, "variation_option_id": null, "comment": "Don't buy this product. Not good at all", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-17T17:01:36.000000Z", "updated_at": "2022-03-17T17:01:36.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 5, "name": "Celery Stick", "slug": "celery-stick", "description": "celery stick - celery stalks cut into small sticks. crudites - raw vegetables cut into bite-sized strips and served with a dip. celery - stalks eaten raw or cooked or used as seasoning.", "type_id": 1, "price": 6, "shop_id": 6, "sale_price": 5, "language": "en", "min_price": 6, "max_price": 6, "sku": "5", "quantity": 18, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "5", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/5/CelerySticks.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/5/conversions/CelerySticks-thumbnail.jpg"}, "video": null, "gallery": [{"id": "585", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/584/Celery-Stick-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/584/conversions/Celery-Stick-1-thumbnail.jpg"}, {"id": "586", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/585/Celery-Stick-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/585/conversions/Celery-Stick-2-thumbnail.jpg"}, {"id": "587", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/586/Celery-Stick.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/586/conversions/Celery-Stick-thumbnail.jpg"}, {"id": "742", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/741/CelerySticks_ulljfz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/741/conversions/CelerySticks_ulljfz-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:44:09.000000Z", "updated_at": "2021-12-23T18:15:54.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1.67, "total_reviews": 3, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 1, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 43, "order_id": null, "user_id": 5, "shop_id": 6, "product_id": 4, "variation_option_id": null, "comment": "Absolute best quality you will ever get.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T17:01:17.000000Z", "updated_at": "2022-03-17T17:01:17.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 4, "name": "Brussels Sprout", "slug": "brussels-sprout", "description": "The Brussels sprout is a member of the Gemmifera Group of cabbages, grown for its edible buds. The leaf vegetables are typically 1.5–4.0 cm in diameter and look like miniature cabbages. The Brussels sprout has long been popular in Brussels, Belgium, and may have gained its name there.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 3, "language": "en", "min_price": 5, "max_price": 5, "sku": "4", "quantity": 17, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "4", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/BrusselsSprouts.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/conversions/BrusselsSprouts-thumbnail.jpg"}, "video": null, "gallery": [{"id": "582", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/Brussels-Sprout-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/conversions/Brussels-Sprout-1-thumbnail.jpg"}, {"id": "583", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/Brussels-Sprout-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/conversions/Brussels-Sprout-2-thumbnail.jpg"}, {"id": "584", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/Brussels-Sprout.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/conversions/Brussels-Sprout-thumbnail.jpg"}, {"id": "741", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/BrusselsSprouts_adwhet.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/conversions/BrusselsSprouts_adwhet-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:42:32.000000Z", "updated_at": "2021-12-23T18:15:57.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 42, "order_id": null, "user_id": 5, "shop_id": 6, "product_id": 3, "variation_option_id": null, "comment": "Really tasty berries.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T17:01:00.000000Z", "updated_at": "2022-03-17T17:01:00.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 3, "name": "Blueberries", "slug": "blueberries", "description": "Blueberries are perennial flowering plants with blue or purple berries. They are classified in the section Cyanococcus within the genus Vaccinium. Vaccinium also includes cranberries, bilberries, huckleberries and Madeira blueberries. Commercial blueberries—both wild and cultivated —are all native to North America.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 3, "max_price": 3, "sku": "3", "quantity": 30, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "3", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/blueberries.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/conversions/blueberries-thumbnail.jpg"}, "video": null, "gallery": [{"id": "580", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/Bluberries-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/conversions/Bluberries-2-thumbnail.jpg"}, {"id": "581", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/Bluberries.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/conversions/Bluberries-thumbnail.jpg"}, {"id": "740", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/blueberries_relyfn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/conversions/blueberries_relyfn-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:40:00.000000Z", "updated_at": "2021-12-23T18:16:00.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 41, "order_id": null, "user_id": 5, "shop_id": 6, "product_id": 2, "variation_option_id": null, "comment": "Decent product.", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-17T17:00:46.000000Z", "updated_at": "2022-03-17T17:00:46.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 2, "name": "<PERSON>", "slug": "baby-spinach", "description": "Spinach (Spinacia oleracea) is a leafy green flowering plant native to central and western Asia. It is of the order Caryophyllales, family Amaranthaceae, subfamily Chenopodioideae. Its leaves are a common edible vegetable consumed either fresh.", "type_id": 1, "price": 0.6, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 0.6, "max_price": 0.6, "sku": "2", "quantity": 10, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "2lb", "height": null, "width": null, "length": null, "image": {"id": "2", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/BabySpinach.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/conversions/BabySpinach-thumbnail.jpg"}, "video": null, "gallery": [{"id": "576", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/baby-spinach-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/conversions/baby-spinach-1-thumbnail.jpg"}, {"id": "577", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/baby-spinach-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/conversions/baby-spinach-2-thumbnail.jpg"}, {"id": "578", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/baby-spinach.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/conversions/baby-spinach-thumbnail.jpg"}, {"id": "738", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/BabySpinach_xronqz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/conversions/BabySpinach_xronqz-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:26:13.000000Z", "updated_at": "2021-12-23T18:16:03.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3.33, "total_reviews": 3, "rating_count": [{"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 3, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 40, "order_id": null, "user_id": 5, "shop_id": 6, "product_id": 1, "variation_option_id": null, "comment": "Excellent and tasty apples.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T16:41:37.000000Z", "updated_at": "2022-03-17T16:41:37.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 1, "name": "Apples", "slug": "apples", "description": "An apple is a sweet, edible fruit produced by an apple tree (Malus domestica). Apple trees are ... The skin of ripe apples is generally red, yellow, green, pink, or russetted, though many bi- or tri-colored cultivars may be found.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.6, "language": "en", "min_price": 2, "max_price": 2, "sku": "1", "quantity": 18, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "1", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/Apples.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/conversions/Apples-thumbnail.jpg"}, "video": null, "gallery": [{"id": "573", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/apple-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/conversions/apple-1-thumbnail.jpg"}, {"id": "574", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/conversions/apple-2-thumbnail.jpg"}, {"id": "575", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/apple.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/conversions/apple-thumbnail.jpg"}, {"id": "737", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/conversions/apple-2-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:24:53.000000Z", "updated_at": "2021-12-23T18:16:06.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 5, "name": "customer3", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T16:25:39.000000Z", "updated_at": "2022-03-17T16:25:39.000000Z", "is_active": 1, "shop_id": null}}, {"id": 39, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 19, "variation_option_id": null, "comment": "Simple well balanced magnificent product.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:51:44.000000Z", "updated_at": "2022-03-17T15:51:44.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 19, "name": "Mix Vegetable Platter", "slug": "mix-vegetable-platter", "description": "Spinach (Spinacia oleracea) is a leafy green flowering plant native to central and western Asia. It is of the order Caryophyllales, family Amaranthaceae, subfamily Chenopodioideae. Its leaves are a common edible vegetable consumed either fresh.", "type_id": 1, "price": 4, "shop_id": 6, "sale_price": 3.2, "language": "en", "min_price": 4, "max_price": 4, "sku": "19", "quantity": 100, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "0.5lb", "height": null, "width": null, "length": null, "image": {"id": "20", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/20/VeggiePlatter.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/20/conversions/VeggiePlatter-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T11:00:12.000000Z", "updated_at": "2021-12-23T18:14:30.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 38, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 18, "variation_option_id": null, "comment": "Don't buy the worst product available here.", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:51:19.000000Z", "updated_at": "2022-03-17T15:51:19.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 18, "name": "<PERSON><PERSON>berry", "slug": "strawberry", "description": "The garden strawberry is a widely grown hybrid species of the genus Fragaria, collectively known as the strawberries, which are cultivated worldwide for their fruit. The fruit is widely appreciated for its characteristic aroma, bright red color, juicy texture, and sweetness.", "type_id": 1, "price": 10, "shop_id": 6, "sale_price": 8, "language": "en", "min_price": 10, "max_price": 10, "sku": "17", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "19", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/19/strawberry.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/19/conversions/strawberry-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:58:54.000000Z", "updated_at": "2021-12-23T18:14:33.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1, "total_reviews": 2, "rating_count": [{"rating": 1, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 37, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 17, "variation_option_id": null, "comment": "Quality product.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:50:49.000000Z", "updated_at": "2022-03-17T15:50:49.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 17, "name": "Cherry", "slug": "cherry", "description": "A cherry is the fruit of many plants of the genus Prunus, and is a fleshy drupe. Commercial cherries are obtained from cultivars of several species, such as the sweet Prunus avium and the sour Prunus cerasus", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.8, "language": "en", "min_price": 2, "max_price": 2, "sku": "16", "quantity": 15, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "18", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/18/RedCherries.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/18/conversions/RedCherries-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:58:02.000000Z", "updated_at": "2021-12-23T18:14:36.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 36, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 16, "variation_option_id": null, "comment": "Simple one word to describe Excellent", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:50:36.000000Z", "updated_at": "2022-03-17T15:50:36.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 16, "name": "Peeled <PERSON>", "slug": "peeled-baby-carrot", "description": "The carrot is a root vegetable, usually orange in colour, though purple, black, red, white, and yellow cultivars exist. They are a domesticated form of the wild carrot, Daucus carota, native to Europe and Southwestern Asia. The plant probably originated in Persia and was originally cultivated for its leaves and seeds.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": 2.2, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "16", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "17", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/17/Peeled-Carrots.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/17/conversions/Peeled-Carrots-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:56:44.000000Z", "updated_at": "2021-12-23T18:14:38.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 35, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 15, "variation_option_id": null, "comment": "Local and half-rotten product.", "rating": 2, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:50:06.000000Z", "updated_at": "2022-03-17T15:50:06.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 15, "name": "<PERSON><PERSON>s", "slug": "pears", "description": "The pear tree and shrub are a species of genus Pyrus, in the family Rosaceae, bearing the pomaceous fruit of the same name. Several species of pear are valued for their edible fruit and juices while others are cultivated as trees.", "type_id": 1, "price": 4, "shop_id": 6, "sale_price": 3.5, "language": "en", "min_price": 4, "max_price": 4, "sku": "15", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "16", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/16/pears.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/16/conversions/pears-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:55:58.000000Z", "updated_at": "2021-12-23T18:15:24.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 2.5, "total_reviews": 2, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 34, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 14, "variation_option_id": null, "comment": "though product was good but only got the yellow peppers instead of mixed colored ones.", "rating": 4, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:49:36.000000Z", "updated_at": "2022-03-17T15:49:36.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 14, "name": "Pepper", "slug": "pepper", "description": "Black pepper is a flowering vine in the family Piperaceae, cultivated for its fruit, known as a peppercorn, which is usually dried and used as a spice and seasoning. When fresh and fully mature, it is about 5 mm in diameter and dark red, and contains a single seed, like all drupes", "type_id": 1, "price": 6, "shop_id": 6, "sale_price": 5, "language": "en", "min_price": 6, "max_price": 6, "sku": "14", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "15", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/15/MiniPeppers.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/15/conversions/MiniPeppers-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:55:14.000000Z", "updated_at": "2021-12-23T18:15:28.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 33, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 13, "variation_option_id": null, "comment": "Really sweet and tasty mangoes.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:48:47.000000Z", "updated_at": "2022-03-17T15:48:47.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 13, "name": "Mango", "slug": "mango", "description": "A mango is a juicy stone fruit produced from numerous species of tropical trees belonging to the flowering plant genus Mangifera, cultivated mostly for their edible fruit. Most of these species are found in nature as wild mangoes. The genus belongs to the cashew family Anacardiaceae.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "13", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "14", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/14/Mangoes.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/14/conversions/Mangoes-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:54:24.000000Z", "updated_at": "2021-12-23T18:15:31.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 32, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 12, "variation_option_id": null, "comment": "Really juicy limes", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:47:47.000000Z", "updated_at": "2022-03-17T15:47:47.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 12, "name": "Lime", "slug": "lime", "description": "The lemon/lime, Citrus limon <PERSON>beck, is a species of small evergreen tree in the flowering plant family Rutaceae, native to South Asia, primarily North eastern India.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.5, "language": "en", "min_price": 2, "max_price": 2, "sku": "12", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "4pc(s)", "height": null, "width": null, "length": null, "image": {"id": "13", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/13/GreenLimes.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/13/conversions/GreenLimes-thumbnail.jpg"}, "video": null, "gallery": [{"id": "570", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/569/lime-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/569/conversions/lime-1-thumbnail.jpg"}, {"id": "572", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/571/lime.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/571/conversions/lime-thumbnail.jpg"}, {"id": "754", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/753/lime-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/753/conversions/lime-2-thumbnail.jpg"}, {"id": "755", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/754/GreenLimes_jrodle.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/754/conversions/GreenLimes_jrodle-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:53:23.000000Z", "updated_at": "2021-12-23T18:15:33.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 31, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 11, "variation_option_id": null, "comment": "Was properly sliced and cleaned but packaging can be improved.", "rating": 4, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:47:32.000000Z", "updated_at": "2022-03-17T15:47:32.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 11, "name": "Green Beans", "slug": "green-beans", "description": "Green beans are the unripe, young fruit and protective pods of various cultivars of the common bean. Immature or young pods of the runner bean, yardlong bean, and hyacinth bean are used in a similar way.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 4, "language": "en", "min_price": 5, "max_price": 5, "sku": "11", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "12", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/12/GreenBeans.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/12/conversions/GreenBeans-thumbnail.jpg"}, "video": null, "gallery": [{"id": "748", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/747/FrenchGreenBeans_azivow.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/747/conversions/FrenchGreenBeans_azivow-thumbnail.jpg"}, {"id": "749", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/748/French-Green-Beans-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/748/conversions/French-Green-Beans-1-thumbnail.jpg"}, {"id": "750", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/749/French-Green-Beans-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/749/conversions/French-Green-Beans-2-thumbnail.jpg"}, {"id": "751", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/750/French-Green-Beans.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/750/conversions/French-Green-Beans-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:52:16.000000Z", "updated_at": "2021-12-23T18:15:37.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 30, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 10, "variation_option_id": null, "comment": "Not cleaned properly", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:47:05.000000Z", "updated_at": "2022-03-17T15:47:05.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 10, "name": "French Green Beans", "slug": "french-green-beans", "description": "Green beans are the unripe, young fruit and protective pods of various cultivars of the common bean. Immature or young pods of the runner bean, yardlong bean, and hyacinth bean are used in a similar way.", "type_id": 1, "price": 1.5, "shop_id": 6, "sale_price": 1.2, "language": "en", "min_price": 1.5, "max_price": 1.5, "sku": "10", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "0.5lb", "height": null, "width": null, "length": null, "image": {"id": "11", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/11/FrenchGreenBeans.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/11/conversions/FrenchGreenBeans-thumbnail.jpg"}, "video": null, "gallery": [{"id": "602", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/601/French-Green-Beans-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/601/conversions/French-Green-Beans-1-thumbnail.jpg"}, {"id": "603", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/602/French-Green-Beans-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/602/conversions/French-Green-Beans-2-thumbnail.jpg"}, {"id": "604", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/603/French-Green-Beans.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/603/conversions/French-Green-Beans-thumbnail.jpg"}, {"id": "747", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/746/FrenchGreenBeans_azivow.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/746/conversions/FrenchGreenBeans_azivow-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:51:28.000000Z", "updated_at": "2021-12-23T18:15:40.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3.33, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 29, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 9, "variation_option_id": null, "comment": "Dates were quality though the brand was different.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:46:36.000000Z", "updated_at": "2022-03-17T15:46:36.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 9, "name": "Dates", "slug": "dates", "description": "Phoenix dactylifera, commonly known as date or date palm, is a flowering plant species in the palm family, Arecaceae, cultivated for its edible sweet fruit.", "type_id": 1, "price": 10, "shop_id": 6, "sale_price": 8, "language": "en", "min_price": 10, "max_price": 10, "sku": "9", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1.5lb", "height": null, "width": null, "length": null, "image": {"id": "10", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/10/Dates.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/10/conversions/Dates-thumbnail.jpg"}, "video": null, "gallery": [{"id": "599", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/598/Dates-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/598/conversions/Dates-1-thumbnail.jpg"}, {"id": "600", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/599/Dates-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/599/conversions/Dates-2-thumbnail.jpg"}, {"id": "601", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/600/Dates.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/600/conversions/Dates-thumbnail.jpg"}, {"id": "746", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/745/Dates_pq4oad.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/745/conversions/Dates_pq4oad-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:50:41.000000Z", "updated_at": "2021-12-23T18:15:43.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 28, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 8, "variation_option_id": null, "comment": "quality product", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:45:26.000000Z", "updated_at": "2022-03-17T15:45:26.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 8, "name": "<PERSON><PERSON><PERSON>ber", "slug": "cucumber", "description": "Cucumber is a widely cultivated plant in the gourd family, Cucurbitaceae. It is a creeping vine that bears cucumiform fruits that are used as vegetables. There are three main varieties of cucumber: slicing, pickling, and seedless. Within these varieties, several cultivars have been created.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "8", "quantity": 25, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "2.5lb", "height": null, "width": null, "length": null, "image": {"id": "8", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/8/Cucumber.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/8/conversions/Cucumber-thumbnail.jpg"}, "video": null, "gallery": [{"id": "596", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/595/Cucumber-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/595/conversions/Cucumber-1-thumbnail.jpg"}, {"id": "597", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/596/Cucumber-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/596/conversions/Cucumber-2-thumbnail.jpg"}, {"id": "598", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/597/Cucumber.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/597/conversions/Cucumber-thumbnail.jpg"}, {"id": "745", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/744/Cucumber_w6hlxr.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/744/conversions/Cucumber_w6hlxr-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:49:18.000000Z", "updated_at": "2021-12-23T18:15:46.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 27, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 7, "variation_option_id": null, "comment": "Good product.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:31:36.000000Z", "updated_at": "2022-03-17T15:31:36.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 7, "name": "Sweet Corn", "slug": "sweet-corn", "description": "Maize, also known as corn, is a cereal grain first domesticated by indigenous peoples in southern Mexico about 10,000 years ago. The leafy stalk of the plant produces pollen inflorescences and separate ovuliferous inflorescences called ears that yield kernels or seeds, which are fruits.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 4, "language": "en", "min_price": 5, "max_price": 5, "sku": "7", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "7", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/7/Corn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/7/conversions/Corn-thumbnail.jpg"}, "video": null, "gallery": [{"id": "593", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/592/Sweet-Corn-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/592/conversions/Sweet-Corn-1-thumbnail.jpg"}, {"id": "594", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/593/Sweet-Corn-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/593/conversions/Sweet-Corn-2-thumbnail.jpg"}, {"id": "595", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/594/Sweet-Corn.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/594/conversions/Sweet-Corn-thumbnail.jpg"}, {"id": "744", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/743/Corn_dlrtbv.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/743/conversions/Corn_dlrtbv-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:48:20.000000Z", "updated_at": "2021-12-23T18:15:49.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 26, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 6, "variation_option_id": null, "comment": "I was looking for sweet ones but it turned out to be sour ones.", "rating": 2, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:26:58.000000Z", "updated_at": "2022-03-17T15:26:58.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 6, "name": "Clementines", "slug": "clementines", "description": "clementine is a tangor, a citrus fruit hybrid between a willowleaf mandarin orange and a sweet orange, named for its late 19th-century discoverer. The exterior is a deep orange colour with a smooth, glossy appearance.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": 2.5, "language": "en", "min_price": 3, "max_price": 3, "sku": "6", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "6", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/6/clementines.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/6/conversions/clementines-thumbnail.jpg"}, "video": null, "gallery": [{"id": "590", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/589/Clementines-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/589/conversions/Clementines-1-thumbnail.jpg"}, {"id": "591", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/590/Clementines.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/590/conversions/Clementines-thumbnail.jpg"}, {"id": "592", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/591/Clementines-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/591/conversions/Clementines-2-thumbnail.jpg"}, {"id": "743", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/742/clementines_h74qrp.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/742/conversions/clementines_h74qrp-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:45:18.000000Z", "updated_at": "2021-12-23T18:15:52.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 2, "total_reviews": 3, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 25, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 5, "variation_option_id": null, "comment": "Ordered 3 days but have yet to get the product. Worst customer service.", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:26:28.000000Z", "updated_at": "2022-03-17T15:26:28.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 5, "name": "Celery Stick", "slug": "celery-stick", "description": "celery stick - celery stalks cut into small sticks. crudites - raw vegetables cut into bite-sized strips and served with a dip. celery - stalks eaten raw or cooked or used as seasoning.", "type_id": 1, "price": 6, "shop_id": 6, "sale_price": 5, "language": "en", "min_price": 6, "max_price": 6, "sku": "5", "quantity": 18, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "5", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/5/CelerySticks.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/5/conversions/CelerySticks-thumbnail.jpg"}, "video": null, "gallery": [{"id": "585", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/584/Celery-Stick-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/584/conversions/Celery-Stick-1-thumbnail.jpg"}, {"id": "586", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/585/Celery-Stick-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/585/conversions/Celery-Stick-2-thumbnail.jpg"}, {"id": "587", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/586/Celery-Stick.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/586/conversions/Celery-Stick-thumbnail.jpg"}, {"id": "742", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/741/CelerySticks_ulljfz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/741/conversions/CelerySticks_ulljfz-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:44:09.000000Z", "updated_at": "2021-12-23T18:15:54.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1.67, "total_reviews": 3, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 1, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 24, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 4, "variation_option_id": null, "comment": "Absolutely marvelous and fresh sprouts available in the country.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:25:19.000000Z", "updated_at": "2022-03-17T15:25:19.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 4, "name": "Brussels Sprout", "slug": "brussels-sprout", "description": "The Brussels sprout is a member of the Gemmifera Group of cabbages, grown for its edible buds. The leaf vegetables are typically 1.5–4.0 cm in diameter and look like miniature cabbages. The Brussels sprout has long been popular in Brussels, Belgium, and may have gained its name there.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 3, "language": "en", "min_price": 5, "max_price": 5, "sku": "4", "quantity": 17, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "4", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/BrusselsSprouts.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/conversions/BrusselsSprouts-thumbnail.jpg"}, "video": null, "gallery": [{"id": "582", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/Brussels-Sprout-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/conversions/Brussels-Sprout-1-thumbnail.jpg"}, {"id": "583", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/Brussels-Sprout-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/conversions/Brussels-Sprout-2-thumbnail.jpg"}, {"id": "584", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/Brussels-Sprout.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/conversions/Brussels-Sprout-thumbnail.jpg"}, {"id": "741", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/BrusselsSprouts_adwhet.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/conversions/BrusselsSprouts_adwhet-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:42:32.000000Z", "updated_at": "2021-12-23T18:15:57.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 23, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 3, "variation_option_id": null, "comment": "Really tasty product.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:24:40.000000Z", "updated_at": "2022-03-17T15:24:40.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 3, "name": "Blueberries", "slug": "blueberries", "description": "Blueberries are perennial flowering plants with blue or purple berries. They are classified in the section Cyanococcus within the genus Vaccinium. Vaccinium also includes cranberries, bilberries, huckleberries and Madeira blueberries. Commercial blueberries—both wild and cultivated —are all native to North America.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 3, "max_price": 3, "sku": "3", "quantity": 30, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "3", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/blueberries.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/conversions/blueberries-thumbnail.jpg"}, "video": null, "gallery": [{"id": "580", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/Bluberries-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/conversions/Bluberries-2-thumbnail.jpg"}, {"id": "581", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/Bluberries.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/conversions/Bluberries-thumbnail.jpg"}, {"id": "740", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/blueberries_relyfn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/conversions/blueberries_relyfn-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:40:00.000000Z", "updated_at": "2021-12-23T18:16:00.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 22, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 2, "variation_option_id": null, "comment": "Fresh product but it was not cleaned properly.", "rating": 4, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:24:24.000000Z", "updated_at": "2022-03-17T15:24:24.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 2, "name": "<PERSON>", "slug": "baby-spinach", "description": "Spinach (Spinacia oleracea) is a leafy green flowering plant native to central and western Asia. It is of the order Caryophyllales, family Amaranthaceae, subfamily Chenopodioideae. Its leaves are a common edible vegetable consumed either fresh.", "type_id": 1, "price": 0.6, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 0.6, "max_price": 0.6, "sku": "2", "quantity": 10, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "2lb", "height": null, "width": null, "length": null, "image": {"id": "2", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/BabySpinach.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/conversions/BabySpinach-thumbnail.jpg"}, "video": null, "gallery": [{"id": "576", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/baby-spinach-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/conversions/baby-spinach-1-thumbnail.jpg"}, {"id": "577", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/baby-spinach-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/conversions/baby-spinach-2-thumbnail.jpg"}, {"id": "578", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/baby-spinach.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/conversions/baby-spinach-thumbnail.jpg"}, {"id": "738", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/BabySpinach_xronqz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/conversions/BabySpinach_xronqz-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:26:13.000000Z", "updated_at": "2021-12-23T18:16:03.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3.33, "total_reviews": 3, "rating_count": [{"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 3, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 21, "order_id": null, "user_id": 4, "shop_id": 6, "product_id": 1, "variation_option_id": null, "comment": "Good quality and fresh apples.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T15:24:03.000000Z", "updated_at": "2022-03-17T15:24:03.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 1, "name": "Apples", "slug": "apples", "description": "An apple is a sweet, edible fruit produced by an apple tree (Malus domestica). Apple trees are ... The skin of ripe apples is generally red, yellow, green, pink, or russetted, though many bi- or tri-colored cultivars may be found.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.6, "language": "en", "min_price": 2, "max_price": 2, "sku": "1", "quantity": 18, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "1", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/Apples.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/conversions/Apples-thumbnail.jpg"}, "video": null, "gallery": [{"id": "573", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/apple-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/conversions/apple-1-thumbnail.jpg"}, {"id": "574", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/conversions/apple-2-thumbnail.jpg"}, {"id": "575", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/apple.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/conversions/apple-thumbnail.jpg"}, {"id": "737", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/conversions/apple-2-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:24:53.000000Z", "updated_at": "2021-12-23T18:16:06.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null}}, {"id": 20, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 20, "variation_option_id": null, "comment": "Was really juicy lemons.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T14:13:47.000000Z", "updated_at": "2022-03-17T14:13:47.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 20, "name": "Lemon", "slug": "lemon", "description": "The lemon/lime, Citrus limon <PERSON>beck, is a species of small evergreen tree in the flowering plant family Rutaceae, native to South Asia, primarily North eastern India.", "type_id": 1, "price": 1.5, "shop_id": 6, "sale_price": 1.2, "language": "en", "min_price": 1.5, "max_price": 1.5, "sku": "20", "quantity": 60, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "4pc(s)", "height": null, "width": null, "length": null, "image": {"id": "21", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/21/Yellow-Limes.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/21/conversions/Yellow-Limes-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T11:01:10.000000Z", "updated_at": "2021-12-23T18:14:28.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 19, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 19, "variation_option_id": null, "comment": "Nicely balanced and mixed of variety of vegetables.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T14:13:27.000000Z", "updated_at": "2022-03-17T14:13:27.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 19, "name": "Mix Vegetable Platter", "slug": "mix-vegetable-platter", "description": "Spinach (Spinacia oleracea) is a leafy green flowering plant native to central and western Asia. It is of the order Caryophyllales, family Amaranthaceae, subfamily Chenopodioideae. Its leaves are a common edible vegetable consumed either fresh.", "type_id": 1, "price": 4, "shop_id": 6, "sale_price": 3.2, "language": "en", "min_price": 4, "max_price": 4, "sku": "19", "quantity": 100, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "0.5lb", "height": null, "width": null, "length": null, "image": {"id": "20", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/20/VeggiePlatter.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/20/conversions/VeggiePlatter-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T11:00:12.000000Z", "updated_at": "2021-12-23T18:14:30.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 18, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 18, "variation_option_id": null, "comment": "One of the worst strawberries I have ever had. Mostly written and really sour.", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-17T14:13:03.000000Z", "updated_at": "2022-03-17T14:13:03.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 18, "name": "<PERSON><PERSON>berry", "slug": "strawberry", "description": "The garden strawberry is a widely grown hybrid species of the genus Fragaria, collectively known as the strawberries, which are cultivated worldwide for their fruit. The fruit is widely appreciated for its characteristic aroma, bright red color, juicy texture, and sweetness.", "type_id": 1, "price": 10, "shop_id": 6, "sale_price": 8, "language": "en", "min_price": 10, "max_price": 10, "sku": "17", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "19", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/19/strawberry.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/19/conversions/strawberry-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:58:54.000000Z", "updated_at": "2021-12-23T18:14:33.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1, "total_reviews": 2, "rating_count": [{"rating": 1, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 17, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 17, "variation_option_id": null, "comment": "Good quality product", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T14:12:28.000000Z", "updated_at": "2022-03-17T14:12:28.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 17, "name": "Cherry", "slug": "cherry", "description": "A cherry is the fruit of many plants of the genus Prunus, and is a fleshy drupe. Commercial cherries are obtained from cultivars of several species, such as the sweet Prunus avium and the sour Prunus cerasus", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.8, "language": "en", "min_price": 2, "max_price": 2, "sku": "16", "quantity": 15, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "18", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/18/RedCherries.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/18/conversions/RedCherries-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:58:02.000000Z", "updated_at": "2021-12-23T18:14:36.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 16, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 16, "variation_option_id": null, "comment": "Nicely peeled and chopped and packed.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T14:12:07.000000Z", "updated_at": "2022-03-17T14:12:07.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 16, "name": "Peeled <PERSON>", "slug": "peeled-baby-carrot", "description": "The carrot is a root vegetable, usually orange in colour, though purple, black, red, white, and yellow cultivars exist. They are a domesticated form of the wild carrot, Daucus carota, native to Europe and Southwestern Asia. The plant probably originated in Persia and was originally cultivated for its leaves and seeds.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": 2.2, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "16", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "17", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/17/Peeled-Carrots.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/17/conversions/Peeled-Carrots-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:56:44.000000Z", "updated_at": "2021-12-23T18:14:38.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 15, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 15, "variation_option_id": null, "comment": "<PERSON><PERSON> said these are imported but what I got was local pears.", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-17T14:01:29.000000Z", "updated_at": "2022-03-17T14:01:29.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 15, "name": "<PERSON><PERSON>s", "slug": "pears", "description": "The pear tree and shrub are a species of genus Pyrus, in the family Rosaceae, bearing the pomaceous fruit of the same name. Several species of pear are valued for their edible fruit and juices while others are cultivated as trees.", "type_id": 1, "price": 4, "shop_id": 6, "sale_price": 3.5, "language": "en", "min_price": 4, "max_price": 4, "sku": "15", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "16", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/16/pears.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/16/conversions/pears-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:55:58.000000Z", "updated_at": "2021-12-23T18:15:24.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 2.5, "total_reviews": 2, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 14, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 14, "variation_option_id": null, "comment": "Good quality product.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T14:00:53.000000Z", "updated_at": "2022-03-17T14:00:53.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 14, "name": "Pepper", "slug": "pepper", "description": "Black pepper is a flowering vine in the family Piperaceae, cultivated for its fruit, known as a peppercorn, which is usually dried and used as a spice and seasoning. When fresh and fully mature, it is about 5 mm in diameter and dark red, and contains a single seed, like all drupes", "type_id": 1, "price": 6, "shop_id": 6, "sale_price": 5, "language": "en", "min_price": 6, "max_price": 6, "sku": "14", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "15", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/15/MiniPeppers.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/15/conversions/MiniPeppers-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:55:14.000000Z", "updated_at": "2021-12-23T18:15:28.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 13, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 13, "variation_option_id": null, "comment": "Though the mangoes were tasty, the seller said these are <PERSON><PERSON><PERSON> but what I got is a different breed", "rating": 4, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:53:32.000000Z", "updated_at": "2022-03-17T13:53:32.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 13, "name": "Mango", "slug": "mango", "description": "A mango is a juicy stone fruit produced from numerous species of tropical trees belonging to the flowering plant genus Mangifera, cultivated mostly for their edible fruit. Most of these species are found in nature as wild mangoes. The genus belongs to the cashew family Anacardiaceae.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "13", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "14", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/14/Mangoes.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/14/conversions/Mangoes-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:54:24.000000Z", "updated_at": "2021-12-23T18:15:31.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 12, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 12, "variation_option_id": null, "comment": "Limes were juicy", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:52:39.000000Z", "updated_at": "2022-03-17T13:52:39.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 12, "name": "Lime", "slug": "lime", "description": "The lemon/lime, Citrus limon <PERSON>beck, is a species of small evergreen tree in the flowering plant family Rutaceae, native to South Asia, primarily North eastern India.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.5, "language": "en", "min_price": 2, "max_price": 2, "sku": "12", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "4pc(s)", "height": null, "width": null, "length": null, "image": {"id": "13", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/13/GreenLimes.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/13/conversions/GreenLimes-thumbnail.jpg"}, "video": null, "gallery": [{"id": "570", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/569/lime-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/569/conversions/lime-1-thumbnail.jpg"}, {"id": "572", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/571/lime.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/571/conversions/lime-thumbnail.jpg"}, {"id": "754", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/753/lime-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/753/conversions/lime-2-thumbnail.jpg"}, {"id": "755", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/754/GreenLimes_jrodle.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/754/conversions/GreenLimes_jrodle-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:53:23.000000Z", "updated_at": "2021-12-23T18:15:33.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 11, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 11, "variation_option_id": null, "comment": "Absolutely fresh produce.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:52:25.000000Z", "updated_at": "2022-03-17T13:52:25.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 11, "name": "Green Beans", "slug": "green-beans", "description": "Green beans are the unripe, young fruit and protective pods of various cultivars of the common bean. Immature or young pods of the runner bean, yardlong bean, and hyacinth bean are used in a similar way.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 4, "language": "en", "min_price": 5, "max_price": 5, "sku": "11", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "12", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/12/GreenBeans.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/12/conversions/GreenBeans-thumbnail.jpg"}, "video": null, "gallery": [{"id": "748", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/747/FrenchGreenBeans_azivow.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/747/conversions/FrenchGreenBeans_azivow-thumbnail.jpg"}, {"id": "749", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/748/French-Green-Beans-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/748/conversions/French-Green-Beans-1-thumbnail.jpg"}, {"id": "750", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/749/French-Green-Beans-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/749/conversions/French-Green-Beans-2-thumbnail.jpg"}, {"id": "751", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/750/French-Green-Beans.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/750/conversions/French-Green-Beans-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:52:16.000000Z", "updated_at": "2021-12-23T18:15:37.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 10, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 10, "variation_option_id": null, "comment": "Quality product.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:52:12.000000Z", "updated_at": "2022-03-17T13:52:12.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 10, "name": "French Green Beans", "slug": "french-green-beans", "description": "Green beans are the unripe, young fruit and protective pods of various cultivars of the common bean. Immature or young pods of the runner bean, yardlong bean, and hyacinth bean are used in a similar way.", "type_id": 1, "price": 1.5, "shop_id": 6, "sale_price": 1.2, "language": "en", "min_price": 1.5, "max_price": 1.5, "sku": "10", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "0.5lb", "height": null, "width": null, "length": null, "image": {"id": "11", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/11/FrenchGreenBeans.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/11/conversions/FrenchGreenBeans-thumbnail.jpg"}, "video": null, "gallery": [{"id": "602", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/601/French-Green-Beans-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/601/conversions/French-Green-Beans-1-thumbnail.jpg"}, {"id": "603", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/602/French-Green-Beans-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/602/conversions/French-Green-Beans-2-thumbnail.jpg"}, {"id": "604", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/603/French-Green-Beans.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/603/conversions/French-Green-Beans-thumbnail.jpg"}, {"id": "747", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/746/FrenchGreenBeans_azivow.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/746/conversions/FrenchGreenBeans_azivow-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:51:28.000000Z", "updated_at": "2021-12-23T18:15:40.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3.33, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 9, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 9, "variation_option_id": null, "comment": "the seller said these are Ajwah dates but sent me a different brand of dates.", "rating": 2, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:48:55.000000Z", "updated_at": "2022-03-17T13:48:55.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 9, "name": "Dates", "slug": "dates", "description": "Phoenix dactylifera, commonly known as date or date palm, is a flowering plant species in the palm family, Arecaceae, cultivated for its edible sweet fruit.", "type_id": 1, "price": 10, "shop_id": 6, "sale_price": 8, "language": "en", "min_price": 10, "max_price": 10, "sku": "9", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1.5lb", "height": null, "width": null, "length": null, "image": {"id": "10", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/10/Dates.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/10/conversions/Dates-thumbnail.jpg"}, "video": null, "gallery": [{"id": "599", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/598/Dates-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/598/conversions/Dates-1-thumbnail.jpg"}, {"id": "600", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/599/Dates-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/599/conversions/Dates-2-thumbnail.jpg"}, {"id": "601", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/600/Dates.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/600/conversions/Dates-thumbnail.jpg"}, {"id": "746", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/745/Dates_pq4oad.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/745/conversions/Dates_pq4oad-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:50:41.000000Z", "updated_at": "2021-12-23T18:15:43.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 8, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 8, "variation_option_id": null, "comment": "was fresh and properly cleaned.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:48:21.000000Z", "updated_at": "2022-03-17T13:48:21.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 8, "name": "<PERSON><PERSON><PERSON>ber", "slug": "cucumber", "description": "Cucumber is a widely cultivated plant in the gourd family, Cucurbitaceae. It is a creeping vine that bears cucumiform fruits that are used as vegetables. There are three main varieties of cucumber: slicing, pickling, and seedless. Within these varieties, several cultivars have been created.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "8", "quantity": 25, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "2.5lb", "height": null, "width": null, "length": null, "image": {"id": "8", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/8/Cucumber.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/8/conversions/Cucumber-thumbnail.jpg"}, "video": null, "gallery": [{"id": "596", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/595/Cucumber-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/595/conversions/Cucumber-1-thumbnail.jpg"}, {"id": "597", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/596/Cucumber-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/596/conversions/Cucumber-2-thumbnail.jpg"}, {"id": "598", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/597/Cucumber.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/597/conversions/Cucumber-thumbnail.jpg"}, {"id": "745", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/744/Cucumber_w6hlxr.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/744/conversions/Cucumber_w6hlxr-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:49:18.000000Z", "updated_at": "2021-12-23T18:15:46.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 7, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 7, "variation_option_id": null, "comment": "quality product.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:48:05.000000Z", "updated_at": "2022-03-17T13:48:05.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 7, "name": "Sweet Corn", "slug": "sweet-corn", "description": "Maize, also known as corn, is a cereal grain first domesticated by indigenous peoples in southern Mexico about 10,000 years ago. The leafy stalk of the plant produces pollen inflorescences and separate ovuliferous inflorescences called ears that yield kernels or seeds, which are fruits.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 4, "language": "en", "min_price": 5, "max_price": 5, "sku": "7", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "7", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/7/Corn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/7/conversions/Corn-thumbnail.jpg"}, "video": null, "gallery": [{"id": "593", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/592/Sweet-Corn-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/592/conversions/Sweet-Corn-1-thumbnail.jpg"}, {"id": "594", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/593/Sweet-Corn-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/593/conversions/Sweet-Corn-2-thumbnail.jpg"}, {"id": "595", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/594/Sweet-Corn.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/594/conversions/Sweet-Corn-thumbnail.jpg"}, {"id": "744", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/743/Corn_dlrtbv.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/743/conversions/Corn_dlrtbv-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:48:20.000000Z", "updated_at": "2021-12-23T18:15:49.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 6, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 6, "variation_option_id": null, "comment": "The seller said these are a sweet lot which is a lie as these are sour lot.", "rating": 1, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:47:48.000000Z", "updated_at": "2022-03-17T13:47:48.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 6, "name": "Clementines", "slug": "clementines", "description": "clementine is a tangor, a citrus fruit hybrid between a willowleaf mandarin orange and a sweet orange, named for its late 19th-century discoverer. The exterior is a deep orange colour with a smooth, glossy appearance.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": 2.5, "language": "en", "min_price": 3, "max_price": 3, "sku": "6", "quantity": 50, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "6", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/6/clementines.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/6/conversions/clementines-thumbnail.jpg"}, "video": null, "gallery": [{"id": "590", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/589/Clementines-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/589/conversions/Clementines-1-thumbnail.jpg"}, {"id": "591", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/590/Clementines.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/590/conversions/Clementines-thumbnail.jpg"}, {"id": "592", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/591/Clementines-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/591/conversions/Clementines-2-thumbnail.jpg"}, {"id": "743", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/742/clementines_h74qrp.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/742/conversions/clementines_h74qrp-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:45:18.000000Z", "updated_at": "2021-12-23T18:15:52.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 2, "total_reviews": 3, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 5, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 5, "variation_option_id": null, "comment": "was sliced and fresh but was not cleaned properly.", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:46:57.000000Z", "updated_at": "2022-03-17T13:46:57.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 5, "name": "Celery Stick", "slug": "celery-stick", "description": "celery stick - celery stalks cut into small sticks. crudites - raw vegetables cut into bite-sized strips and served with a dip. celery - stalks eaten raw or cooked or used as seasoning.", "type_id": 1, "price": 6, "shop_id": 6, "sale_price": 5, "language": "en", "min_price": 6, "max_price": 6, "sku": "5", "quantity": 18, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "5", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/5/CelerySticks.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/5/conversions/CelerySticks-thumbnail.jpg"}, "video": null, "gallery": [{"id": "585", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/584/Celery-Stick-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/584/conversions/Celery-Stick-1-thumbnail.jpg"}, {"id": "586", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/585/Celery-Stick-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/585/conversions/Celery-Stick-2-thumbnail.jpg"}, {"id": "587", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/586/Celery-Stick.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/586/conversions/Celery-Stick-thumbnail.jpg"}, {"id": "742", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/741/CelerySticks_ulljfz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/741/conversions/CelerySticks_ulljfz-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:44:09.000000Z", "updated_at": "2021-12-23T18:15:54.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 1.67, "total_reviews": 3, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 1, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 4, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 4, "variation_option_id": null, "comment": "absolutely best sprouts in town.", "rating": 5, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:46:30.000000Z", "updated_at": "2022-03-17T13:46:30.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 4, "name": "Brussels Sprout", "slug": "brussels-sprout", "description": "The Brussels sprout is a member of the Gemmifera Group of cabbages, grown for its edible buds. The leaf vegetables are typically 1.5–4.0 cm in diameter and look like miniature cabbages. The Brussels sprout has long been popular in Brussels, Belgium, and may have gained its name there.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 3, "language": "en", "min_price": 5, "max_price": 5, "sku": "4", "quantity": 17, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "4", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/BrusselsSprouts.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/conversions/BrusselsSprouts-thumbnail.jpg"}, "video": null, "gallery": [{"id": "582", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/Brussels-Sprout-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/conversions/Brussels-Sprout-1-thumbnail.jpg"}, {"id": "583", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/Brussels-Sprout-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/conversions/Brussels-Sprout-2-thumbnail.jpg"}, {"id": "584", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/Brussels-Sprout.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/conversions/Brussels-Sprout-thumbnail.jpg"}, {"id": "741", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/BrusselsSprouts_adwhet.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/conversions/BrusselsSprouts_adwhet-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:42:32.000000Z", "updated_at": "2021-12-23T18:15:57.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 3, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 3, "variation_option_id": null, "comment": "Blueberries were good but delivery was not on time.", "rating": 4, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:46:02.000000Z", "updated_at": "2022-03-17T13:46:02.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 3, "name": "Blueberries", "slug": "blueberries", "description": "Blueberries are perennial flowering plants with blue or purple berries. They are classified in the section Cyanococcus within the genus Vaccinium. Vaccinium also includes cranberries, bilberries, huckleberries and Madeira blueberries. Commercial blueberries—both wild and cultivated —are all native to North America.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 3, "max_price": 3, "sku": "3", "quantity": 30, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "3", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/blueberries.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/conversions/blueberries-thumbnail.jpg"}, "video": null, "gallery": [{"id": "580", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/Bluberries-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/conversions/Bluberries-2-thumbnail.jpg"}, {"id": "581", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/Bluberries.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/conversions/Bluberries-thumbnail.jpg"}, {"id": "740", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/blueberries_relyfn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/conversions/blueberries_relyfn-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:40:00.000000Z", "updated_at": "2021-12-23T18:16:00.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 2, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 2, "variation_option_id": null, "comment": "It's not fully fresh. Was probably a few days old already.", "rating": 3, "photos": [], "deleted_at": null, "created_at": "2022-03-17T13:45:24.000000Z", "updated_at": "2022-03-17T13:45:24.000000Z", "positive_feedbacks_count": 112, "negative_feedbacks_count": 13, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 2, "name": "<PERSON>", "slug": "baby-spinach", "description": "Spinach (Spinacia oleracea) is a leafy green flowering plant native to central and western Asia. It is of the order Caryophyllales, family Amaranthaceae, subfamily Chenopodioideae. Its leaves are a common edible vegetable consumed either fresh.", "type_id": 1, "price": 0.6, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 0.6, "max_price": 0.6, "sku": "2", "quantity": 10, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "2lb", "height": null, "width": null, "length": null, "image": {"id": "2", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/BabySpinach.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/conversions/BabySpinach-thumbnail.jpg"}, "video": null, "gallery": [{"id": "576", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/baby-spinach-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/conversions/baby-spinach-1-thumbnail.jpg"}, {"id": "577", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/baby-spinach-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/conversions/baby-spinach-2-thumbnail.jpg"}, {"id": "578", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/baby-spinach.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/conversions/baby-spinach-thumbnail.jpg"}, {"id": "738", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/BabySpinach_xronqz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/conversions/BabySpinach_xronqz-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:26:13.000000Z", "updated_at": "2021-12-23T18:16:03.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 3.33, "total_reviews": 3, "rating_count": [{"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 3, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}, {"id": 1, "order_id": null, "user_id": 2, "shop_id": 6, "product_id": 1, "variation_option_id": null, "comment": "Good and yummy", "rating": 4, "photos": [{"id": 1689, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1689/peakpx.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1689/conversions/peakpx-thumbnail.jpg"}], "deleted_at": null, "created_at": "2022-03-17T08:17:44.000000Z", "updated_at": "2022-03-17T16:09:51.000000Z", "positive_feedbacks_count": 5, "negative_feedbacks_count": 2, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 1, "name": "Apples", "slug": "apples", "description": "An apple is a sweet, edible fruit produced by an apple tree (Malus domestica). Apple trees are ... The skin of ripe apples is generally red, yellow, green, pink, or russetted, though many bi- or tri-colored cultivars may be found.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.6, "language": "en", "min_price": 2, "max_price": 2, "sku": "1", "quantity": 18, "in_stock": 1, "is_taxable": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "1", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/Apples.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/conversions/Apples-thumbnail.jpg"}, "video": null, "gallery": [{"id": "573", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/apple-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/conversions/apple-1-thumbnail.jpg"}, {"id": "574", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/conversions/apple-2-thumbnail.jpg"}, {"id": "575", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/apple.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/conversions/apple-thumbnail.jpg"}, {"id": "737", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/conversions/apple-2-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:24:53.000000Z", "updated_at": "2021-12-23T18:16:06.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "blocked_dates": [], "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null}}]