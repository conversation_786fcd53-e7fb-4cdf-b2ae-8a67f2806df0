"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_cart_cart-counter-icon-button_tsx"],{

/***/ "./src/components/cart/cart-counter-icon-button.tsx":
/*!**********************************************************!*\
  !*** ./src/components/cart/cart-counter-icon-button.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_icons_cart_outlined__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/icons/cart-outlined */ \"./src/components/icons/cart-outlined.tsx\");\n/* harmony import */ var _store_drawer_atom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/drawer-atom */ \"./src/store/drawer-atom.ts\");\n/* harmony import */ var _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/quick-cart/cart.context */ \"./src/store/quick-cart/cart.context.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jotai */ \"./node_modules/jotai/esm/index.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tailwind-merge */ \"./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CartCounterIconButton = (param)=>{\n    let { className, ...rest } = param;\n    _s();\n    const { totalUniqueItems } = (0,_store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_3__.useCart)();\n    const [_, setDisplayCart] = (0,jotai__WEBPACK_IMPORTED_MODULE_5__.useAtom)(_store_drawer_atom__WEBPACK_IMPORTED_MODULE_2__.drawerAtom);\n    function handleCartSidebar() {\n        setDisplayCart({\n            display: true,\n            view: \"cart\"\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_6__.twMerge)(classnames__WEBPACK_IMPORTED_MODULE_4___default()(\"hidden product-cart lg:flex relative\", className)),\n        onClick: handleCartSidebar,\n        ...rest,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_cart_outlined__WEBPACK_IMPORTED_MODULE_1__.CartOutlinedIcon, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-icon-button.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            totalUniqueItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"min-w-[20px] h-5 flex items-center justify-center rounded-full bg-primary-accent text-primary-text text-[10px] absolute ltr:-right-1/2 rtl:-left-1/2 -top-1/2\",\n                children: totalUniqueItems\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-icon-button.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-icon-button.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CartCounterIconButton, \"kpasgz+kIeeBXucDOqkK25fgj9E=\", false, function() {\n    return [\n        _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_3__.useCart,\n        jotai__WEBPACK_IMPORTED_MODULE_5__.useAtom\n    ];\n});\n_c = CartCounterIconButton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CartCounterIconButton);\nvar _c;\n$RefreshReg$(_c, \"CartCounterIconButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/cart/cart-counter-icon-button.tsx\n"));

/***/ }),

/***/ "./src/components/icons/cart-outlined.tsx":
/*!************************************************!*\
  !*** ./src/components/icons/cart-outlined.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartOutlinedIcon: function() { return /* binding */ CartOutlinedIcon; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CartOutlinedIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 17.6 19.6\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            \"data-name\": \"Path 12\",\n            d: \"M12.8 8.8v-4a4 4 0 00-8 0v4m-3-2h14l1 12H.8z\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: \"1.6\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-outlined.tsx\",\n            lineNumber: 3,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-outlined.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n_c = CartOutlinedIcon;\nvar _c;\n$RefreshReg$(_c, \"CartOutlinedIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9jYXJ0LW91dGxpbmVkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU8sTUFBTUEsbUJBQXNELENBQUNDLHNCQUNsRSw4REFBQ0M7UUFBSUMsT0FBTTtRQUE2QkMsU0FBUTtRQUFpQixHQUFHSCxLQUFLO2tCQUN2RSw0RUFBQ0k7WUFDQ0MsYUFBVTtZQUNWQyxHQUFFO1lBQ0ZDLE1BQUs7WUFDTEMsUUFBTztZQUNQQyxlQUFjO1lBQ2RDLGdCQUFlO1lBQ2ZDLGFBQVk7Ozs7Ozs7Ozs7a0JBR2hCO0tBWldaIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2ljb25zL2NhcnQtb3V0bGluZWQudHN4P2E5NmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IENhcnRPdXRsaW5lZEljb246IFJlYWN0LkZDPFJlYWN0LlNWR0F0dHJpYnV0ZXM8e30+PiA9IChwcm9wcykgPT4gKFxuICA8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiB2aWV3Qm94PVwiMCAwIDE3LjYgMTkuNlwiIHsuLi5wcm9wc30+XG4gICAgPHBhdGhcbiAgICAgIGRhdGEtbmFtZT1cIlBhdGggMTJcIlxuICAgICAgZD1cIk0xMi44IDguOHYtNGE0IDQgMCAwMC04IDB2NG0tMy0yaDE0bDEgMTJILjh6XCJcbiAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICBzdHJva2VXaWR0aD1cIjEuNlwiXG4gICAgLz5cbiAgPC9zdmc+XG4pO1xuIl0sIm5hbWVzIjpbIkNhcnRPdXRsaW5lZEljb24iLCJwcm9wcyIsInN2ZyIsInhtbG5zIiwidmlld0JveCIsInBhdGgiLCJkYXRhLW5hbWUiLCJkIiwiZmlsbCIsInN0cm9rZSIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/icons/cart-outlined.tsx\n"));

/***/ })

}]);