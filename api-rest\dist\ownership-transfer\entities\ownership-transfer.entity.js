"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TodayTotalOrderByStatus = exports.OwnershipTransfer = void 0;
const openapi = require("@nestjs/swagger");
const core_entity_1 = require("../../common/entities/core.entity");
class OwnershipTransfer extends core_entity_1.CoreEntity {
    static _OPENAPI_METADATA_FACTORY() {
        return { created_by: { required: true, type: () => String }, deleted_at: { required: true, type: () => String }, transaction_identifier: { required: true, type: () => String }, previous_owner: { required: true, type: () => require("../../users/entities/user.entity").User }, current_owner: { required: true, type: () => require("../../users/entities/user.entity").User }, message: { required: false, type: () => String }, status: { required: true, type: () => String }, shop: { required: true, type: () => require("../../shops/entities/shop.entity").Shop }, refund_info: { required: false, type: () => [require("../../refunds/entities/refund.entity").Refund] }, withdrawal_info: { required: false, type: () => [require("../../withdraws/entities/withdraw.entity").Withdraw] }, order_info: { required: true, type: () => require("./ownership-transfer.entity").TodayTotalOrderByStatus }, balance_info: { required: true, type: () => require("../../shops/entities/shop.entity").Balance }, name: { required: true, type: () => String } };
    }
}
exports.OwnershipTransfer = OwnershipTransfer;
class TodayTotalOrderByStatus {
    static _OPENAPI_METADATA_FACTORY() {
        return { pending: { required: true, type: () => Number }, processing: { required: true, type: () => Number }, complete: { required: true, type: () => Number }, cancelled: { required: true, type: () => Number }, refunded: { required: true, type: () => Number }, failed: { required: true, type: () => Number }, localFacility: { required: true, type: () => Number }, outForDelivery: { required: true, type: () => Number } };
    }
}
exports.TodayTotalOrderByStatus = TodayTotalOrderByStatus;
//# sourceMappingURL=ownership-transfer.entity.js.map