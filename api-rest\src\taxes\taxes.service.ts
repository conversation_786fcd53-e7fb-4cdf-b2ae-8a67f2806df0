import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreateTaxDto } from './dto/create-tax.dto';
import { UpdateTaxDto } from './dto/update-tax.dto';
import { Tax } from './entities/tax.entity';

@Injectable()
export class TaxesService {
  constructor(
    @InjectModel(Tax)
    private taxModel: typeof Tax,
  ) {}

  async create(createTaxDto: CreateTaxDto): Promise<Tax> {
    return this.taxModel.create(createTaxDto as any);
  }

  async findAll(): Promise<Tax[]> {
    return this.taxModel.findAll();
  }

  async findOne(id: number): Promise<Tax | null> {
    return this.taxModel.findByPk(id);
  }

  async update(id: number, updateTaxDto: UpdateTaxDto): Promise<Tax | null> {
    await this.taxModel.update(updateTaxDto as any, { where: { id } });
    return this.taxModel.findByPk(id);
  }

  remove(id: number) {
    return `This action removes a #${id} tax`;
  }
}
