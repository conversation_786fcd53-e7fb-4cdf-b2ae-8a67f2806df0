"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/my-shops",{

/***/ "./src/components/dashboard/owner.tsx":
/*!********************************************!*\
  !*** ./src/components/dashboard/owner.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_widgets_column_chart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/widgets/column-chart */ \"./src/components/widgets/column-chart.tsx\");\n/* harmony import */ var _components_widgets_sticker_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/widgets/sticker-card */ \"./src/components/widgets/sticker-card.tsx\");\n/* harmony import */ var _data_dashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/dashboard */ \"./src/data/dashboard.ts\");\n/* harmony import */ var _utils_auth_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/auth-utils */ \"./src/utils/auth-utils.ts\");\n/* harmony import */ var _utils_use_price__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/use-price */ \"./src/utils/use-price.ts\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _components_icons_summary_earning__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/icons/summary/earning */ \"./src/components/icons/summary/earning.tsx\");\n/* harmony import */ var _components_icons_summary_shopping__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/summary/shopping */ \"./src/components/icons/summary/shopping.tsx\");\n/* harmony import */ var _components_icons_summary_checklist__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/icons/summary/checklist */ \"./src/components/icons/summary/checklist.tsx\");\n/* harmony import */ var _components_icons_summary_basket__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/icons/summary/basket */ \"./src/components/icons/summary/basket.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _components_common_page_heading__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/common/page-heading */ \"./src/components/common/page-heading.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ShopList = next_dynamic__WEBPACK_IMPORTED_MODULE_9___default()(()=>__webpack_require__.e(/*! import() */ \"src_components_dashboard_shops_shops_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/dashboard/shops/shops */ \"./src/components/dashboard/shops/shops.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\dashboard\\\\owner.tsx -> \" + \"@/components/dashboard/shops/shops\"\n        ]\n    }\n});\n_c = ShopList;\nconst Message = next_dynamic__WEBPACK_IMPORTED_MODULE_9___default()(_c1 = ()=>__webpack_require__.e(/*! import() */ \"src_components_dashboard_shops_message_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/dashboard/shops/message */ \"./src/components/dashboard/shops/message.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\dashboard\\\\owner.tsx -> \" + \"@/components/dashboard/shops/message\"\n        ]\n    }\n});\n_c2 = Message;\nconst StoreNotices = next_dynamic__WEBPACK_IMPORTED_MODULE_9___default()(_c3 = ()=>__webpack_require__.e(/*! import() */ \"src_components_dashboard_shops_store-notices_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/dashboard/shops/store-notices */ \"./src/components/dashboard/shops/store-notices.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\dashboard\\\\owner.tsx -> \" + \"@/components/dashboard/shops/store-notices\"\n        ]\n    }\n});\n_c4 = StoreNotices;\nconst OrderStatusWidget = next_dynamic__WEBPACK_IMPORTED_MODULE_9___default()(()=>__webpack_require__.e(/*! import() */ \"src_components_dashboard_widgets_box_widget-order-by-status_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/dashboard/widgets/box/widget-order-by-status */ \"./src/components/dashboard/widgets/box/widget-order-by-status.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\dashboard\\\\owner.tsx -> \" + \"@/components/dashboard/widgets/box/widget-order-by-status\"\n        ]\n    }\n});\n_c5 = OrderStatusWidget;\nconst ProductCountByCategory = next_dynamic__WEBPACK_IMPORTED_MODULE_9___default()(()=>__webpack_require__.e(/*! import() */ \"src_components_dashboard_widgets_table_widget-product-count-by-category_tsx-_32bc0\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/dashboard/widgets/table/widget-product-count-by-category */ \"./src/components/dashboard/widgets/table/widget-product-count-by-category.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\dashboard\\\\owner.tsx -> \" + \"@/components/dashboard/widgets/table/widget-product-count-by-category\"\n        ]\n    }\n});\n_c6 = ProductCountByCategory;\nconst TopRatedProducts = next_dynamic__WEBPACK_IMPORTED_MODULE_9___default()(()=>__webpack_require__.e(/*! import() */ \"src_components_dashboard_widgets_box_widget-top-rate-product_tsx-_47910\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/dashboard/widgets/box/widget-top-rate-product */ \"./src/components/dashboard/widgets/box/widget-top-rate-product.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\dashboard\\\\owner.tsx -> \" + \"@/components/dashboard/widgets/box/widget-top-rate-product\"\n        ]\n    }\n});\n_c7 = TopRatedProducts;\nconst MAP_PAGE_LIST = {\n    ShopList: ShopList,\n    Message: Message,\n    StoreNotices: StoreNotices\n};\nconst OwnerShopLayout = ()=>{\n    var _data_totalYearSaleByMonth;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)();\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { permissions } = (0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_5__.getAuthCredentials)();\n    const { data, isLoading: loading } = (0,_data_dashboard__WEBPACK_IMPORTED_MODULE_4__.useAnalyticsQuery)();\n    const [activeTimeFrame, setActiveTimeFrame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [orderDataRange, setOrderDataRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(data === null || data === void 0 ? void 0 : data.todayTotalOrderByStatus);\n    const { data: productByCategory, isLoading: productByCategoryLoading, error: productByCategoryError } = (0,_data_dashboard__WEBPACK_IMPORTED_MODULE_4__.useProductByCategoryQuery)({\n        limit: 10,\n        language: locale\n    });\n    const { data: topRatedProducts, isLoading: topRatedProductsLoading, error: topRatedProductsError } = (0,_data_dashboard__WEBPACK_IMPORTED_MODULE_4__.useTopRatedProductsQuery)({\n        limit: 10,\n        language: locale\n    });\n    const { price: total_revenue } = (0,_utils_use_price__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(data && {\n        amount: data === null || data === void 0 ? void 0 : data.totalRevenue\n    });\n    const { price: total_refund } = (0,_utils_use_price__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(data && {\n        amount: data === null || data === void 0 ? void 0 : data.totalRefunds\n    });\n    const { price: todays_revenue } = (0,_utils_use_price__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(data && {\n        amount: data === null || data === void 0 ? void 0 : data.todaysRevenue\n    });\n    const { query } = router;\n    const classNames = {\n        basic: \"lg:text-[1.375rem] font-semibold border-b-2 border-solid border-transparent lg:pb-5 pb-3 -mb-0.5\",\n        selected: \"text-accent hover:text-accent-hover border-current\",\n        normal: \"hover:text-black/80\"\n    };\n    let salesByYear = Array.from({\n        length: 12\n    }, (_)=>0);\n    if (!!(data === null || data === void 0 ? void 0 : (_data_totalYearSaleByMonth = data.totalYearSaleByMonth) === null || _data_totalYearSaleByMonth === void 0 ? void 0 : _data_totalYearSaleByMonth.length)) {\n        salesByYear = data.totalYearSaleByMonth.map((item)=>item.total.toFixed(2));\n    }\n    const timeFrame = [\n        {\n            name: t(\"text-today\"),\n            day: 1\n        },\n        {\n            name: t(\"text-weekly\"),\n            day: 7\n        },\n        {\n            name: t(\"text-monthly\"),\n            day: 30\n        },\n        {\n            name: t(\"text-yearly\"),\n            day: 365\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        switch(activeTimeFrame){\n            case 1:\n                setOrderDataRange(data === null || data === void 0 ? void 0 : data.todayTotalOrderByStatus);\n                break;\n            case 7:\n                setOrderDataRange(data === null || data === void 0 ? void 0 : data.weeklyTotalOrderByStatus);\n                break;\n            case 30:\n                setOrderDataRange(data === null || data === void 0 ? void 0 : data.todayTotalOrderByStatus);\n                break;\n            case 365:\n                setOrderDataRange(data === null || data === void 0 ? void 0 : data.yearlyTotalOrderByStatus);\n                break;\n            default:\n                setOrderDataRange(orderDataRange);\n                break;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8 rounded-lg bg-light p-5 md:p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-7 flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_page_heading__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            title: t(\"text-summary\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid w-full grid-cols-1 gap-5 sm:grid-cols-2 xl:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_sticker_card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                titleTransKey: \"sticker-card-title-rev\",\n                                // subtitleTransKey=\"sticker-card-subtitle-rev\"\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_earning__WEBPACK_IMPORTED_MODULE_11__.EaringIcon, {\n                                    className: \"h-8 w-8\"\n                                }, void 0, false, void 0, void 0),\n                                color: \"#047857\",\n                                price: total_revenue\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_sticker_card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                titleTransKey: \"sticker-card-title-today-refunds\",\n                                // subtitleTransKey=\"sticker-card-subtitle-order\"\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_shopping__WEBPACK_IMPORTED_MODULE_12__.ShoppingIcon, {\n                                    className: \"h-8 w-8\"\n                                }, void 0, false, void 0, void 0),\n                                color: \"#865DFF\",\n                                price: total_refund\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_sticker_card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                titleTransKey: \"sticker-card-title-total-shops\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_basket__WEBPACK_IMPORTED_MODULE_14__.BasketIcon, {\n                                    className: \"h-8 w-8\"\n                                }, void 0, false, void 0, void 0),\n                                color: \"#E157A0\",\n                                price: data === null || data === void 0 ? void 0 : data.totalShops\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_sticker_card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                titleTransKey: \"sticker-card-title-today-rev\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_checklist__WEBPACK_IMPORTED_MODULE_13__.ChecklistIcon, {\n                                    className: \"h-8 w-8\"\n                                }, void 0, false, void 0, void 0),\n                                color: \"#D74EFF\",\n                                price: todays_revenue\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8 rounded-lg bg-light p-5 md:p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-5 items-center justify-between sm:flex md:mb-7\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_page_heading__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                title: t(\"text-order-status\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3.5 inline-flex rounded-full bg-gray-100/80 p-1.5 sm:mt-0\",\n                                children: timeFrame ? timeFrame.map((time)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(\"!focus:ring-0  relative z-10 !h-7 rounded-full !px-2.5 text-sm font-medium text-gray-500\", time.day === activeTimeFrame ? \"text-accent\" : \"\"),\n                                                type: \"button\",\n                                                onClick: ()=>setActiveTimeFrame(time.day),\n                                                variant: \"custom\",\n                                                children: time.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            time.day === activeTimeFrame ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                                                className: \"absolute bottom-0 left-0 right-0 z-0 h-full rounded-3xl bg-accent/10\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 23\n                                            }, undefined) : null\n                                        ]\n                                    }, time.day, true, {\n                                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, undefined)) : null\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OrderStatusWidget, {\n                        order: orderDataRange,\n                        timeFrame: activeTimeFrame,\n                        allowedStatus: [\n                            \"pending\",\n                            \"processing\",\n                            \"complete\",\n                            \"cancel\"\n                        ]\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, undefined),\n            (0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_5__.hasAccess)(_utils_auth_utils__WEBPACK_IMPORTED_MODULE_5__.adminAndOwnerOnly, permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8 flex w-full flex-wrap md:flex-nowrap\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_column_chart__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    widgetTitle: t(\"common:sale-history\"),\n                    colors: [\n                        \"#6073D4\"\n                    ],\n                    series: salesByYear,\n                    categories: [\n                        t(\"common:january\"),\n                        t(\"common:february\"),\n                        t(\"common:march\"),\n                        t(\"common:april\"),\n                        t(\"common:may\"),\n                        t(\"common:june\"),\n                        t(\"common:july\"),\n                        t(\"common:august\"),\n                        t(\"common:september\"),\n                        t(\"common:october\"),\n                        t(\"common:november\"),\n                        t(\"common:december\")\n                    ]\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-8 xl:grid-cols-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TopRatedProducts, {\n                        products: topRatedProducts,\n                        title: \"text-most-rated-products\",\n                        className: \"xl:col-span-5 2xl:me-20\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCountByCategory, {\n                        products: productByCategory,\n                        title: \"text-most-category-products\",\n                        className: \"xl:col-span-7 2xl:ltr:-ml-20 2xl:rtl:-mr-20\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(OwnerShopLayout, \"QkvUzNgyzwEyBkeo4gCSMevdDko=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _data_dashboard__WEBPACK_IMPORTED_MODULE_4__.useAnalyticsQuery,\n        _data_dashboard__WEBPACK_IMPORTED_MODULE_4__.useProductByCategoryQuery,\n        _data_dashboard__WEBPACK_IMPORTED_MODULE_4__.useTopRatedProductsQuery,\n        _utils_use_price__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _utils_use_price__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _utils_use_price__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c8 = OwnerShopLayout;\nconst OwnerDashboard = ()=>{\n    const { permissions } = (0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_5__.getAuthCredentials)();\n    let permission = (0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_5__.hasAccess)(_utils_auth_utils__WEBPACK_IMPORTED_MODULE_5__.adminOnly, permissions);\n    return permission ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShopList, {}, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n        lineNumber: 247,\n        columnNumber: 23\n    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OwnerShopLayout, {}, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\owner.tsx\",\n        lineNumber: 247,\n        columnNumber: 38\n    }, undefined);\n};\n_c9 = OwnerDashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (OwnerDashboard);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"ShopList\");\n$RefreshReg$(_c1, \"Message$dynamic\");\n$RefreshReg$(_c2, \"Message\");\n$RefreshReg$(_c3, \"StoreNotices$dynamic\");\n$RefreshReg$(_c4, \"StoreNotices\");\n$RefreshReg$(_c5, \"OrderStatusWidget\");\n$RefreshReg$(_c6, \"ProductCountByCategory\");\n$RefreshReg$(_c7, \"TopRatedProducts\");\n$RefreshReg$(_c8, \"OwnerShopLayout\");\n$RefreshReg$(_c9, \"OwnerDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvb3duZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ2dCO0FBQ0E7QUFLbEM7QUFNRTtBQUNhO0FBQ2I7QUFDa0I7QUFDWDtBQUNLO0FBQ3dCO0FBQ0c7QUFDRTtBQUNOO0FBQ25CO0FBQ0w7QUFDb0I7QUFDM0QsTUFBTXVCLFdBQVdULG1EQUFPQSxDQUFDLElBQU0sNk5BQU87Ozs7Ozs7S0FBaENTO0FBQ04sTUFBTUMsVUFBVVYsbURBQU9BLE9BQUMsSUFBTSxtT0FBTzs7Ozs7Ozs7QUFDckMsTUFBTVcsZUFBZVgsbURBQU9BLE9BQzFCLElBQU0scVBBQU87Ozs7Ozs7O0FBRWYsTUFBTVksb0JBQW9CWixtREFBT0EsQ0FDL0IsSUFBTSxrU0FBTzs7Ozs7OztNQURUWTtBQUdOLE1BQU1DLHlCQUF5QmIsbURBQU9BLENBQ3BDLElBQ0UsNlVBQ0U7Ozs7Ozs7TUFIQWE7QUFPTixNQUFNQyxtQkFBbUJkLG1EQUFPQSxDQUM5QixJQUFNLDRTQUFPOzs7Ozs7O01BRFRjO0FBSU4sTUFBTUMsZ0JBQXFDO0lBQ3pDTixVQUFVQTtJQUNWQyxTQUFTQTtJQUNUQyxjQUFjQTtBQUNoQjtBQUVBLE1BQU1LLGtCQUFrQjtRQWdEaEJDOztJQS9DTixNQUFNLEVBQUVDLENBQUMsRUFBRSxHQUFHbkIsNERBQWNBO0lBQzVCLE1BQU0sRUFBRW9CLE1BQU0sRUFBRSxHQUFHbEIsdURBQVNBO0lBQzVCLE1BQU1tQixTQUFTbkIsdURBQVNBO0lBQ3hCLE1BQU0sRUFBRW9CLFdBQVcsRUFBRSxHQUFHMUIscUVBQWtCQTtJQUMxQyxNQUFNLEVBQUVzQixJQUFJLEVBQUVLLFdBQVdDLE9BQU8sRUFBRSxHQUFHakMsa0VBQWlCQTtJQUN0RCxNQUFNLENBQUNrQyxpQkFBaUJDLG1CQUFtQixHQUFHdEMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDdUMsZ0JBQWdCQyxrQkFBa0IsR0FBR3hDLCtDQUFRQSxDQUNsRDhCLGlCQUFBQSwyQkFBQUEsS0FBTVcsdUJBQXVCO0lBRy9CLE1BQU0sRUFDSlgsTUFBTVksaUJBQWlCLEVBQ3ZCUCxXQUFXUSx3QkFBd0IsRUFDbkNDLE9BQU9DLHNCQUFzQixFQUM5QixHQUFHekMsMEVBQXlCQSxDQUFDO1FBQUUwQyxPQUFPO1FBQUlDLFVBQVVmO0lBQU87SUFFNUQsTUFBTSxFQUNKRixNQUFNa0IsZ0JBQWdCLEVBQ3RCYixXQUFXYyx1QkFBdUIsRUFDbENMLE9BQU9NLHFCQUFxQixFQUM3QixHQUFHN0MseUVBQXdCQSxDQUFDO1FBQUV5QyxPQUFPO1FBQUlDLFVBQVVmO0lBQU87SUFFM0QsTUFBTSxFQUFFbUIsT0FBT0MsYUFBYSxFQUFFLEdBQUcxQyw0REFBUUEsQ0FDdkNvQixRQUFRO1FBQ051QixNQUFNLEVBQUV2QixpQkFBQUEsMkJBQUFBLEtBQU13QixZQUFZO0lBQzVCO0lBRUYsTUFBTSxFQUFFSCxPQUFPSSxZQUFZLEVBQUUsR0FBRzdDLDREQUFRQSxDQUN0Q29CLFFBQVE7UUFDTnVCLE1BQU0sRUFBRXZCLGlCQUFBQSwyQkFBQUEsS0FBTTBCLFlBQVk7SUFDNUI7SUFHRixNQUFNLEVBQUVMLE9BQU9NLGNBQWMsRUFBRSxHQUFHL0MsNERBQVFBLENBQ3hDb0IsUUFBUTtRQUNOdUIsTUFBTSxFQUFFdkIsaUJBQUFBLDJCQUFBQSxLQUFNNEIsYUFBYTtJQUM3QjtJQUVGLE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUcxQjtJQUVsQixNQUFNMkIsYUFBYTtRQUNqQkMsT0FDRTtRQUNGQyxVQUFVO1FBQ1ZDLFFBQVE7SUFDVjtJQUNBLElBQUlDLGNBQXdCQyxNQUFNQyxJQUFJLENBQUM7UUFBRUMsUUFBUTtJQUFHLEdBQUcsQ0FBQ0MsSUFBTTtJQUM5RCxJQUFJLENBQUMsRUFBQ3RDLGlCQUFBQSw0QkFBQUEsNkJBQUFBLEtBQU11QyxvQkFBb0IsY0FBMUJ2QyxpREFBQUEsMkJBQTRCcUMsTUFBTSxHQUFFO1FBQ3hDSCxjQUFjbEMsS0FBS3VDLG9CQUFvQixDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsT0FDM0NBLEtBQUtDLEtBQUssQ0FBQ0MsT0FBTyxDQUFDO0lBRXZCO0lBRUEsTUFBTUMsWUFBWTtRQUNoQjtZQUFFQyxNQUFNNUMsRUFBRTtZQUFlNkMsS0FBSztRQUFFO1FBQ2hDO1lBQUVELE1BQU01QyxFQUFFO1lBQWdCNkMsS0FBSztRQUFFO1FBQ2pDO1lBQUVELE1BQU01QyxFQUFFO1lBQWlCNkMsS0FBSztRQUFHO1FBQ25DO1lBQUVELE1BQU01QyxFQUFFO1lBQWdCNkMsS0FBSztRQUFJO0tBQ3BDO0lBRUQ3RSxnREFBU0EsQ0FBQztRQUNSLE9BQVFzQztZQUNOLEtBQUs7Z0JBQ0hHLGtCQUFrQlYsaUJBQUFBLDJCQUFBQSxLQUFNVyx1QkFBdUI7Z0JBQy9DO1lBQ0YsS0FBSztnQkFDSEQsa0JBQWtCVixpQkFBQUEsMkJBQUFBLEtBQU0rQyx3QkFBd0I7Z0JBQ2hEO1lBQ0YsS0FBSztnQkFDSHJDLGtCQUFrQlYsaUJBQUFBLDJCQUFBQSxLQUFNVyx1QkFBdUI7Z0JBQy9DO1lBQ0YsS0FBSztnQkFDSEQsa0JBQWtCVixpQkFBQUEsMkJBQUFBLEtBQU1nRCx3QkFBd0I7Z0JBQ2hEO1lBRUY7Z0JBQ0V0QyxrQkFBa0JEO2dCQUNsQjtRQUNKO0lBQ0Y7SUFFQSxxQkFDRTs7MEJBQ0UsOERBQUN3QztnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDM0Qsd0VBQVdBOzRCQUFDNEQsT0FBT2xELEVBQUU7Ozs7Ozs7Ozs7O2tDQUV4Qiw4REFBQ2dEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQzlFLHdFQUFXQTtnQ0FDVmdGLGVBQWM7Z0NBQ2QsK0NBQStDO2dDQUMvQ0Msb0JBQU0sOERBQUNwRSwwRUFBVUE7b0NBQUNpRSxXQUFVOztnQ0FDNUJJLE9BQU07Z0NBQ05qQyxPQUFPQzs7Ozs7OzBDQUVULDhEQUFDbEQsd0VBQVdBO2dDQUNWZ0YsZUFBYztnQ0FDZCxpREFBaUQ7Z0NBQ2pEQyxvQkFBTSw4REFBQ25FLDZFQUFZQTtvQ0FBQ2dFLFdBQVU7O2dDQUM5QkksT0FBTTtnQ0FDTmpDLE9BQU9JOzs7Ozs7MENBRVQsOERBQUNyRCx3RUFBV0E7Z0NBQ1ZnRixlQUFjO2dDQUNkQyxvQkFBTSw4REFBQ2pFLHlFQUFVQTtvQ0FBQzhELFdBQVU7O2dDQUM1QkksT0FBTTtnQ0FDTmpDLEtBQUssRUFBRXJCLGlCQUFBQSwyQkFBQUEsS0FBTXVELFVBQVU7Ozs7OzswQ0FFekIsOERBQUNuRix3RUFBV0E7Z0NBQ1ZnRixlQUFjO2dDQUNkQyxvQkFBTSw4REFBQ2xFLCtFQUFhQTtvQ0FBQytELFdBQVU7O2dDQUMvQkksT0FBTTtnQ0FDTmpDLE9BQU9NOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS2IsOERBQUNzQjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQzNELHdFQUFXQTtnQ0FBQzRELE9BQU9sRCxFQUFFOzs7Ozs7MENBQ3RCLDhEQUFDZ0Q7Z0NBQUlDLFdBQVU7MENBQ1pOLFlBQ0dBLFVBQVVKLEdBQUcsQ0FBQyxDQUFDZ0IscUJBQ2IsOERBQUNQO3dDQUFtQkMsV0FBVTs7MERBQzVCLDhEQUFDN0QsOERBQU1BO2dEQUNMNkQsV0FBV3JFLGlEQUFFQSxDQUNYLDRGQUNBMkUsS0FBS1YsR0FBRyxLQUFLdkMsa0JBQWtCLGdCQUFnQjtnREFFakRrRCxNQUFLO2dEQUNMQyxTQUFTLElBQU1sRCxtQkFBbUJnRCxLQUFLVixHQUFHO2dEQUMxQ2EsU0FBUTswREFFUEgsS0FBS1gsSUFBSTs7Ozs7OzRDQUVYVyxLQUFLVixHQUFHLEtBQUt2QyxnQ0FDWiw4REFBQ2pCLGtEQUFNQSxDQUFDMkQsR0FBRztnREFBQ0MsV0FBVTs7Ozs7NERBQ3BCOzt1Q0FkSU0sS0FBS1YsR0FBRzs7OztxREFpQnBCOzs7Ozs7Ozs7Ozs7a0NBR1IsOERBQUNuRDt3QkFDQ2lFLE9BQU9uRDt3QkFDUG1DLFdBQVdyQzt3QkFDWHNELGVBQWU7NEJBQUM7NEJBQVc7NEJBQWM7NEJBQVk7eUJBQVM7Ozs7Ozs7Ozs7OztZQUlqRWxGLDREQUFTQSxDQUFDRixnRUFBaUJBLEVBQUUyQiw4QkFDNUIsOERBQUM2QztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQy9FLHdFQUFXQTtvQkFDVjJGLGFBQWE3RCxFQUFFO29CQUNmOEQsUUFBUTt3QkFBQztxQkFBVTtvQkFDbkJDLFFBQVE5QjtvQkFDUitCLFlBQVk7d0JBQ1ZoRSxFQUFFO3dCQUNGQSxFQUFFO3dCQUNGQSxFQUFFO3dCQUNGQSxFQUFFO3dCQUNGQSxFQUFFO3dCQUNGQSxFQUFFO3dCQUNGQSxFQUFFO3dCQUNGQSxFQUFFO3dCQUNGQSxFQUFFO3dCQUNGQSxFQUFFO3dCQUNGQSxFQUFFO3dCQUNGQSxFQUFFO3FCQUNIOzs7Ozs7Ozs7OzswQkFLUCw4REFBQ2dEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ3JEO3dCQUNDcUUsVUFBVWhEO3dCQUNWaUMsT0FBTzt3QkFDUEQsV0FBVTs7Ozs7O2tDQUVaLDhEQUFDdEQ7d0JBQ0NzRSxVQUFVdEQ7d0JBQ1Z1QyxPQUFPO3dCQUNQRCxXQUFVOzs7Ozs7Ozs7Ozs7OztBQUtwQjtHQTdMTW5EOztRQUNVakIsd0RBQWNBO1FBQ1RFLG1EQUFTQTtRQUNiQSxtREFBU0E7UUFFYVgsOERBQWlCQTtRQVVsREMsc0VBQXlCQTtRQU16QkMscUVBQXdCQTtRQUVLSyx3REFBUUE7UUFLVEEsd0RBQVFBO1FBTU5BLHdEQUFRQTs7O01BbEN0Q21CO0FBK0xOLE1BQU1vRSxpQkFBaUI7SUFDckIsTUFBTSxFQUFFL0QsV0FBVyxFQUFFLEdBQUcxQixxRUFBa0JBO0lBQzFDLElBQUkwRixhQUFhekYsNERBQVNBLENBQUNILHdEQUFTQSxFQUFFNEI7SUFFdEMsT0FBT2dFLDJCQUFhLDhEQUFDNUU7Ozs7a0NBQWMsOERBQUNPOzs7OztBQUN0QztNQUxNb0U7QUFPTiwrREFBZUEsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvb3duZXIudHN4P2Q3MmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IENvbHVtbkNoYXJ0IGZyb20gJ0AvY29tcG9uZW50cy93aWRnZXRzL2NvbHVtbi1jaGFydCc7XHJcbmltcG9ydCBTdGlja2VyQ2FyZCBmcm9tICdAL2NvbXBvbmVudHMvd2lkZ2V0cy9zdGlja2VyLWNhcmQnO1xyXG5pbXBvcnQge1xyXG4gIHVzZUFuYWx5dGljc1F1ZXJ5LFxyXG4gIHVzZVByb2R1Y3RCeUNhdGVnb3J5UXVlcnksXHJcbiAgdXNlVG9wUmF0ZWRQcm9kdWN0c1F1ZXJ5LFxyXG59IGZyb20gJ0AvZGF0YS9kYXNoYm9hcmQnO1xyXG5pbXBvcnQge1xyXG4gIGFkbWluT25seSxcclxuICBhZG1pbkFuZE93bmVyT25seSxcclxuICBnZXRBdXRoQ3JlZGVudGlhbHMsXHJcbiAgaGFzQWNjZXNzLFxyXG59IGZyb20gJ0AvdXRpbHMvYXV0aC11dGlscyc7XHJcbmltcG9ydCB1c2VQcmljZSBmcm9tICdAL3V0aWxzL3VzZS1wcmljZSc7XHJcbmltcG9ydCBjbiBmcm9tICdjbGFzc25hbWVzJztcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICduZXh0LWkxOG5leHQnO1xyXG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XHJcbmltcG9ydCB7IEVhcmluZ0ljb24gfSBmcm9tICdAL2NvbXBvbmVudHMvaWNvbnMvc3VtbWFyeS9lYXJuaW5nJztcclxuaW1wb3J0IHsgU2hvcHBpbmdJY29uIH0gZnJvbSAnQC9jb21wb25lbnRzL2ljb25zL3N1bW1hcnkvc2hvcHBpbmcnO1xyXG5pbXBvcnQgeyBDaGVja2xpc3RJY29uIH0gZnJvbSAnQC9jb21wb25lbnRzL2ljb25zL3N1bW1hcnkvY2hlY2tsaXN0JztcclxuaW1wb3J0IHsgQmFza2V0SWNvbiB9IGZyb20gJ0AvY29tcG9uZW50cy9pY29ucy9zdW1tYXJ5L2Jhc2tldCc7XHJcbmltcG9ydCBCdXR0b24gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XHJcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xyXG5pbXBvcnQgUGFnZUhlYWRpbmcgZnJvbSAnQC9jb21wb25lbnRzL2NvbW1vbi9wYWdlLWhlYWRpbmcnO1xyXG5jb25zdCBTaG9wTGlzdCA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL3Nob3BzL3Nob3BzJykpO1xyXG5jb25zdCBNZXNzYWdlID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvc2hvcHMvbWVzc2FnZScpKTtcclxuY29uc3QgU3RvcmVOb3RpY2VzID0gZHluYW1pYyhcclxuICAoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvc2hvcHMvc3RvcmUtbm90aWNlcycpLFxyXG4pO1xyXG5jb25zdCBPcmRlclN0YXR1c1dpZGdldCA9IGR5bmFtaWMoXHJcbiAgKCkgPT4gaW1wb3J0KCdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL3dpZGdldHMvYm94L3dpZGdldC1vcmRlci1ieS1zdGF0dXMnKSxcclxuKTtcclxuY29uc3QgUHJvZHVjdENvdW50QnlDYXRlZ29yeSA9IGR5bmFtaWMoXHJcbiAgKCkgPT5cclxuICAgIGltcG9ydChcclxuICAgICAgJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvd2lkZ2V0cy90YWJsZS93aWRnZXQtcHJvZHVjdC1jb3VudC1ieS1jYXRlZ29yeSdcclxuICAgICksXHJcbik7XHJcblxyXG5jb25zdCBUb3BSYXRlZFByb2R1Y3RzID0gZHluYW1pYyhcclxuICAoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvd2lkZ2V0cy9ib3gvd2lkZ2V0LXRvcC1yYXRlLXByb2R1Y3QnKSxcclxuKTtcclxuXHJcbmNvbnN0IE1BUF9QQUdFX0xJU1Q6IFJlY29yZDxzdHJpbmcsIGFueT4gPSB7XHJcbiAgU2hvcExpc3Q6IFNob3BMaXN0LFxyXG4gIE1lc3NhZ2U6IE1lc3NhZ2UsXHJcbiAgU3RvcmVOb3RpY2VzOiBTdG9yZU5vdGljZXMsXHJcbn07XHJcblxyXG5jb25zdCBPd25lclNob3BMYXlvdXQgPSAoKSA9PiB7XHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG4gIGNvbnN0IHsgbG9jYWxlIH0gPSB1c2VSb3V0ZXIoKTtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuICBjb25zdCB7IHBlcm1pc3Npb25zIH0gPSBnZXRBdXRoQ3JlZGVudGlhbHMoKTtcclxuICBjb25zdCB7IGRhdGEsIGlzTG9hZGluZzogbG9hZGluZyB9ID0gdXNlQW5hbHl0aWNzUXVlcnkoKTtcclxuICBjb25zdCBbYWN0aXZlVGltZUZyYW1lLCBzZXRBY3RpdmVUaW1lRnJhbWVdID0gdXNlU3RhdGUoMSk7XHJcbiAgY29uc3QgW29yZGVyRGF0YVJhbmdlLCBzZXRPcmRlckRhdGFSYW5nZV0gPSB1c2VTdGF0ZShcclxuICAgIGRhdGE/LnRvZGF5VG90YWxPcmRlckJ5U3RhdHVzLFxyXG4gICk7XHJcblxyXG4gIGNvbnN0IHtcclxuICAgIGRhdGE6IHByb2R1Y3RCeUNhdGVnb3J5LFxyXG4gICAgaXNMb2FkaW5nOiBwcm9kdWN0QnlDYXRlZ29yeUxvYWRpbmcsXHJcbiAgICBlcnJvcjogcHJvZHVjdEJ5Q2F0ZWdvcnlFcnJvcixcclxuICB9ID0gdXNlUHJvZHVjdEJ5Q2F0ZWdvcnlRdWVyeSh7IGxpbWl0OiAxMCwgbGFuZ3VhZ2U6IGxvY2FsZSB9KTtcclxuXHJcbiAgY29uc3Qge1xyXG4gICAgZGF0YTogdG9wUmF0ZWRQcm9kdWN0cyxcclxuICAgIGlzTG9hZGluZzogdG9wUmF0ZWRQcm9kdWN0c0xvYWRpbmcsXHJcbiAgICBlcnJvcjogdG9wUmF0ZWRQcm9kdWN0c0Vycm9yLFxyXG4gIH0gPSB1c2VUb3BSYXRlZFByb2R1Y3RzUXVlcnkoeyBsaW1pdDogMTAsIGxhbmd1YWdlOiBsb2NhbGUgfSk7XHJcblxyXG4gIGNvbnN0IHsgcHJpY2U6IHRvdGFsX3JldmVudWUgfSA9IHVzZVByaWNlKFxyXG4gICAgZGF0YSAmJiB7XHJcbiAgICAgIGFtb3VudDogZGF0YT8udG90YWxSZXZlbnVlISxcclxuICAgIH0sXHJcbiAgKTtcclxuICBjb25zdCB7IHByaWNlOiB0b3RhbF9yZWZ1bmQgfSA9IHVzZVByaWNlKFxyXG4gICAgZGF0YSAmJiB7XHJcbiAgICAgIGFtb3VudDogZGF0YT8udG90YWxSZWZ1bmRzISxcclxuICAgIH0sXHJcbiAgKTtcclxuXHJcbiAgY29uc3QgeyBwcmljZTogdG9kYXlzX3JldmVudWUgfSA9IHVzZVByaWNlKFxyXG4gICAgZGF0YSAmJiB7XHJcbiAgICAgIGFtb3VudDogZGF0YT8udG9kYXlzUmV2ZW51ZSEsXHJcbiAgICB9LFxyXG4gICk7XHJcbiAgY29uc3QgeyBxdWVyeSB9ID0gcm91dGVyO1xyXG5cclxuICBjb25zdCBjbGFzc05hbWVzID0ge1xyXG4gICAgYmFzaWM6XHJcbiAgICAgICdsZzp0ZXh0LVsxLjM3NXJlbV0gZm9udC1zZW1pYm9sZCBib3JkZXItYi0yIGJvcmRlci1zb2xpZCBib3JkZXItdHJhbnNwYXJlbnQgbGc6cGItNSBwYi0zIC1tYi0wLjUnLFxyXG4gICAgc2VsZWN0ZWQ6ICd0ZXh0LWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1ob3ZlciBib3JkZXItY3VycmVudCcsXHJcbiAgICBub3JtYWw6ICdob3Zlcjp0ZXh0LWJsYWNrLzgwJyxcclxuICB9O1xyXG4gIGxldCBzYWxlc0J5WWVhcjogbnVtYmVyW10gPSBBcnJheS5mcm9tKHsgbGVuZ3RoOiAxMiB9LCAoXykgPT4gMCk7XHJcbiAgaWYgKCEhZGF0YT8udG90YWxZZWFyU2FsZUJ5TW9udGg/Lmxlbmd0aCkge1xyXG4gICAgc2FsZXNCeVllYXIgPSBkYXRhLnRvdGFsWWVhclNhbGVCeU1vbnRoLm1hcCgoaXRlbTogYW55KSA9PlxyXG4gICAgICBpdGVtLnRvdGFsLnRvRml4ZWQoMiksXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgdGltZUZyYW1lID0gW1xyXG4gICAgeyBuYW1lOiB0KCd0ZXh0LXRvZGF5JyksIGRheTogMSB9LFxyXG4gICAgeyBuYW1lOiB0KCd0ZXh0LXdlZWtseScpLCBkYXk6IDcgfSxcclxuICAgIHsgbmFtZTogdCgndGV4dC1tb250aGx5JyksIGRheTogMzAgfSxcclxuICAgIHsgbmFtZTogdCgndGV4dC15ZWFybHknKSwgZGF5OiAzNjUgfSxcclxuICBdO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgc3dpdGNoIChhY3RpdmVUaW1lRnJhbWUpIHtcclxuICAgICAgY2FzZSAxOlxyXG4gICAgICAgIHNldE9yZGVyRGF0YVJhbmdlKGRhdGE/LnRvZGF5VG90YWxPcmRlckJ5U3RhdHVzKTtcclxuICAgICAgICBicmVhaztcclxuICAgICAgY2FzZSA3OlxyXG4gICAgICAgIHNldE9yZGVyRGF0YVJhbmdlKGRhdGE/LndlZWtseVRvdGFsT3JkZXJCeVN0YXR1cyk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgMzA6XHJcbiAgICAgICAgc2V0T3JkZXJEYXRhUmFuZ2UoZGF0YT8udG9kYXlUb3RhbE9yZGVyQnlTdGF0dXMpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBjYXNlIDM2NTpcclxuICAgICAgICBzZXRPcmRlckRhdGFSYW5nZShkYXRhPy55ZWFybHlUb3RhbE9yZGVyQnlTdGF0dXMpO1xyXG4gICAgICAgIGJyZWFrO1xyXG5cclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICBzZXRPcmRlckRhdGFSYW5nZShvcmRlckRhdGFSYW5nZSk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICB9XHJcbiAgfSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTggcm91bmRlZC1sZyBiZy1saWdodCBwLTUgbWQ6cC04XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi03IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgPFBhZ2VIZWFkaW5nIHRpdGxlPXt0KCd0ZXh0LXN1bW1hcnknKX0gLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgdy1mdWxsIGdyaWQtY29scy0xIGdhcC01IHNtOmdyaWQtY29scy0yIHhsOmdyaWQtY29scy00XCI+XHJcbiAgICAgICAgICA8U3RpY2tlckNhcmRcclxuICAgICAgICAgICAgdGl0bGVUcmFuc0tleT1cInN0aWNrZXItY2FyZC10aXRsZS1yZXZcIlxyXG4gICAgICAgICAgICAvLyBzdWJ0aXRsZVRyYW5zS2V5PVwic3RpY2tlci1jYXJkLXN1YnRpdGxlLXJldlwiXHJcbiAgICAgICAgICAgIGljb249ezxFYXJpbmdJY29uIGNsYXNzTmFtZT1cImgtOCB3LThcIiAvPn1cclxuICAgICAgICAgICAgY29sb3I9XCIjMDQ3ODU3XCJcclxuICAgICAgICAgICAgcHJpY2U9e3RvdGFsX3JldmVudWV9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPFN0aWNrZXJDYXJkXHJcbiAgICAgICAgICAgIHRpdGxlVHJhbnNLZXk9XCJzdGlja2VyLWNhcmQtdGl0bGUtdG9kYXktcmVmdW5kc1wiXHJcbiAgICAgICAgICAgIC8vIHN1YnRpdGxlVHJhbnNLZXk9XCJzdGlja2VyLWNhcmQtc3VidGl0bGUtb3JkZXJcIlxyXG4gICAgICAgICAgICBpY29uPXs8U2hvcHBpbmdJY29uIGNsYXNzTmFtZT1cImgtOCB3LThcIiAvPn1cclxuICAgICAgICAgICAgY29sb3I9XCIjODY1REZGXCJcclxuICAgICAgICAgICAgcHJpY2U9e3RvdGFsX3JlZnVuZH1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8U3RpY2tlckNhcmRcclxuICAgICAgICAgICAgdGl0bGVUcmFuc0tleT1cInN0aWNrZXItY2FyZC10aXRsZS10b3RhbC1zaG9wc1wiXHJcbiAgICAgICAgICAgIGljb249ezxCYXNrZXRJY29uIGNsYXNzTmFtZT1cImgtOCB3LThcIiAvPn1cclxuICAgICAgICAgICAgY29sb3I9XCIjRTE1N0EwXCJcclxuICAgICAgICAgICAgcHJpY2U9e2RhdGE/LnRvdGFsU2hvcHN9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPFN0aWNrZXJDYXJkXHJcbiAgICAgICAgICAgIHRpdGxlVHJhbnNLZXk9XCJzdGlja2VyLWNhcmQtdGl0bGUtdG9kYXktcmV2XCJcclxuICAgICAgICAgICAgaWNvbj17PENoZWNrbGlzdEljb24gY2xhc3NOYW1lPVwiaC04IHctOFwiIC8+fVxyXG4gICAgICAgICAgICBjb2xvcj1cIiNENzRFRkZcIlxyXG4gICAgICAgICAgICBwcmljZT17dG9kYXlzX3JldmVudWV9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOCByb3VuZGVkLWxnIGJnLWxpZ2h0IHAtNSBtZDpwLThcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTUgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBzbTpmbGV4IG1kOm1iLTdcIj5cclxuICAgICAgICAgIDxQYWdlSGVhZGluZyB0aXRsZT17dCgndGV4dC1vcmRlci1zdGF0dXMnKX0gLz5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMy41IGlubGluZS1mbGV4IHJvdW5kZWQtZnVsbCBiZy1ncmF5LTEwMC84MCBwLTEuNSBzbTptdC0wXCI+XHJcbiAgICAgICAgICAgIHt0aW1lRnJhbWVcclxuICAgICAgICAgICAgICA/IHRpbWVGcmFtZS5tYXAoKHRpbWUpID0+IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e3RpbWUuZGF5fSBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICchZm9jdXM6cmluZy0wICByZWxhdGl2ZSB6LTEwICFoLTcgcm91bmRlZC1mdWxsICFweC0yLjUgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgdGltZS5kYXkgPT09IGFjdGl2ZVRpbWVGcmFtZSA/ICd0ZXh0LWFjY2VudCcgOiAnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRpbWVGcmFtZSh0aW1lLmRheSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiY3VzdG9tXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7dGltZS5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIHt0aW1lLmRheSA9PT0gYWN0aXZlVGltZUZyYW1lID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTAgbGVmdC0wIHJpZ2h0LTAgei0wIGgtZnVsbCByb3VuZGVkLTN4bCBiZy1hY2NlbnQvMTBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICkgOiBudWxsfVxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICkpXHJcbiAgICAgICAgICAgICAgOiBudWxsfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPE9yZGVyU3RhdHVzV2lkZ2V0XHJcbiAgICAgICAgICBvcmRlcj17b3JkZXJEYXRhUmFuZ2V9XHJcbiAgICAgICAgICB0aW1lRnJhbWU9e2FjdGl2ZVRpbWVGcmFtZX1cclxuICAgICAgICAgIGFsbG93ZWRTdGF0dXM9e1sncGVuZGluZycsICdwcm9jZXNzaW5nJywgJ2NvbXBsZXRlJywgJ2NhbmNlbCddfVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAge2hhc0FjY2VzcyhhZG1pbkFuZE93bmVyT25seSwgcGVybWlzc2lvbnMpICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTggZmxleCB3LWZ1bGwgZmxleC13cmFwIG1kOmZsZXgtbm93cmFwXCI+XHJcbiAgICAgICAgICA8Q29sdW1uQ2hhcnRcclxuICAgICAgICAgICAgd2lkZ2V0VGl0bGU9e3QoJ2NvbW1vbjpzYWxlLWhpc3RvcnknKX1cclxuICAgICAgICAgICAgY29sb3JzPXtbJyM2MDczRDQnXX1cclxuICAgICAgICAgICAgc2VyaWVzPXtzYWxlc0J5WWVhcn1cclxuICAgICAgICAgICAgY2F0ZWdvcmllcz17W1xyXG4gICAgICAgICAgICAgIHQoJ2NvbW1vbjpqYW51YXJ5JyksXHJcbiAgICAgICAgICAgICAgdCgnY29tbW9uOmZlYnJ1YXJ5JyksXHJcbiAgICAgICAgICAgICAgdCgnY29tbW9uOm1hcmNoJyksXHJcbiAgICAgICAgICAgICAgdCgnY29tbW9uOmFwcmlsJyksXHJcbiAgICAgICAgICAgICAgdCgnY29tbW9uOm1heScpLFxyXG4gICAgICAgICAgICAgIHQoJ2NvbW1vbjpqdW5lJyksXHJcbiAgICAgICAgICAgICAgdCgnY29tbW9uOmp1bHknKSxcclxuICAgICAgICAgICAgICB0KCdjb21tb246YXVndXN0JyksXHJcbiAgICAgICAgICAgICAgdCgnY29tbW9uOnNlcHRlbWJlcicpLFxyXG4gICAgICAgICAgICAgIHQoJ2NvbW1vbjpvY3RvYmVyJyksXHJcbiAgICAgICAgICAgICAgdCgnY29tbW9uOm5vdmVtYmVyJyksXHJcbiAgICAgICAgICAgICAgdCgnY29tbW9uOmRlY2VtYmVyJyksXHJcbiAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC04IHhsOmdyaWQtY29scy0xMlwiPlxyXG4gICAgICAgIDxUb3BSYXRlZFByb2R1Y3RzXHJcbiAgICAgICAgICBwcm9kdWN0cz17dG9wUmF0ZWRQcm9kdWN0c31cclxuICAgICAgICAgIHRpdGxlPXsndGV4dC1tb3N0LXJhdGVkLXByb2R1Y3RzJ31cclxuICAgICAgICAgIGNsYXNzTmFtZT1cInhsOmNvbC1zcGFuLTUgMnhsOm1lLTIwXCJcclxuICAgICAgICAvPlxyXG4gICAgICAgIDxQcm9kdWN0Q291bnRCeUNhdGVnb3J5XHJcbiAgICAgICAgICBwcm9kdWN0cz17cHJvZHVjdEJ5Q2F0ZWdvcnl9XHJcbiAgICAgICAgICB0aXRsZT17J3RleHQtbW9zdC1jYXRlZ29yeS1wcm9kdWN0cyd9XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJ4bDpjb2wtc3Bhbi03IDJ4bDpsdHI6LW1sLTIwIDJ4bDpydGw6LW1yLTIwXCJcclxuICAgICAgICAvPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvPlxyXG4gICk7XHJcbn07XHJcblxyXG5jb25zdCBPd25lckRhc2hib2FyZCA9ICgpID0+IHtcclxuICBjb25zdCB7IHBlcm1pc3Npb25zIH0gPSBnZXRBdXRoQ3JlZGVudGlhbHMoKTtcclxuICBsZXQgcGVybWlzc2lvbiA9IGhhc0FjY2VzcyhhZG1pbk9ubHksIHBlcm1pc3Npb25zKTtcclxuXHJcbiAgcmV0dXJuIHBlcm1pc3Npb24gPyA8U2hvcExpc3QgLz4gOiA8T3duZXJTaG9wTGF5b3V0IC8+O1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgT3duZXJEYXNoYm9hcmQ7XHJcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIkNvbHVtbkNoYXJ0IiwiU3RpY2tlckNhcmQiLCJ1c2VBbmFseXRpY3NRdWVyeSIsInVzZVByb2R1Y3RCeUNhdGVnb3J5UXVlcnkiLCJ1c2VUb3BSYXRlZFByb2R1Y3RzUXVlcnkiLCJhZG1pbk9ubHkiLCJhZG1pbkFuZE93bmVyT25seSIsImdldEF1dGhDcmVkZW50aWFscyIsImhhc0FjY2VzcyIsInVzZVByaWNlIiwiY24iLCJ1c2VUcmFuc2xhdGlvbiIsImR5bmFtaWMiLCJ1c2VSb3V0ZXIiLCJFYXJpbmdJY29uIiwiU2hvcHBpbmdJY29uIiwiQ2hlY2tsaXN0SWNvbiIsIkJhc2tldEljb24iLCJCdXR0b24iLCJtb3Rpb24iLCJQYWdlSGVhZGluZyIsIlNob3BMaXN0IiwiTWVzc2FnZSIsIlN0b3JlTm90aWNlcyIsIk9yZGVyU3RhdHVzV2lkZ2V0IiwiUHJvZHVjdENvdW50QnlDYXRlZ29yeSIsIlRvcFJhdGVkUHJvZHVjdHMiLCJNQVBfUEFHRV9MSVNUIiwiT3duZXJTaG9wTGF5b3V0IiwiZGF0YSIsInQiLCJsb2NhbGUiLCJyb3V0ZXIiLCJwZXJtaXNzaW9ucyIsImlzTG9hZGluZyIsImxvYWRpbmciLCJhY3RpdmVUaW1lRnJhbWUiLCJzZXRBY3RpdmVUaW1lRnJhbWUiLCJvcmRlckRhdGFSYW5nZSIsInNldE9yZGVyRGF0YVJhbmdlIiwidG9kYXlUb3RhbE9yZGVyQnlTdGF0dXMiLCJwcm9kdWN0QnlDYXRlZ29yeSIsInByb2R1Y3RCeUNhdGVnb3J5TG9hZGluZyIsImVycm9yIiwicHJvZHVjdEJ5Q2F0ZWdvcnlFcnJvciIsImxpbWl0IiwibGFuZ3VhZ2UiLCJ0b3BSYXRlZFByb2R1Y3RzIiwidG9wUmF0ZWRQcm9kdWN0c0xvYWRpbmciLCJ0b3BSYXRlZFByb2R1Y3RzRXJyb3IiLCJwcmljZSIsInRvdGFsX3JldmVudWUiLCJhbW91bnQiLCJ0b3RhbFJldmVudWUiLCJ0b3RhbF9yZWZ1bmQiLCJ0b3RhbFJlZnVuZHMiLCJ0b2RheXNfcmV2ZW51ZSIsInRvZGF5c1JldmVudWUiLCJxdWVyeSIsImNsYXNzTmFtZXMiLCJiYXNpYyIsInNlbGVjdGVkIiwibm9ybWFsIiwic2FsZXNCeVllYXIiLCJBcnJheSIsImZyb20iLCJsZW5ndGgiLCJfIiwidG90YWxZZWFyU2FsZUJ5TW9udGgiLCJtYXAiLCJpdGVtIiwidG90YWwiLCJ0b0ZpeGVkIiwidGltZUZyYW1lIiwibmFtZSIsImRheSIsIndlZWtseVRvdGFsT3JkZXJCeVN0YXR1cyIsInllYXJseVRvdGFsT3JkZXJCeVN0YXR1cyIsImRpdiIsImNsYXNzTmFtZSIsInRpdGxlIiwidGl0bGVUcmFuc0tleSIsImljb24iLCJjb2xvciIsInRvdGFsU2hvcHMiLCJ0aW1lIiwidHlwZSIsIm9uQ2xpY2siLCJ2YXJpYW50Iiwib3JkZXIiLCJhbGxvd2VkU3RhdHVzIiwid2lkZ2V0VGl0bGUiLCJjb2xvcnMiLCJzZXJpZXMiLCJjYXRlZ29yaWVzIiwicHJvZHVjdHMiLCJPd25lckRhc2hib2FyZCIsInBlcm1pc3Npb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/dashboard/owner.tsx\n"));

/***/ })

});