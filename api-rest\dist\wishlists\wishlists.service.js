"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WishlistsService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const paginate_1 = require("../common/pagination/paginate");
const wishlist_entity_1 = require("./entities/wishlist.entity");
const product_entity_1 = require("../products/entities/product.entity");
let WishlistsService = class WishlistsService {
    constructor(wishlistModel, productModel) {
        this.wishlistModel = wishlistModel;
        this.productModel = productModel;
    }
    async findAllWishlists({ limit, page, search }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const { count, rows: data } = await this.wishlistModel.findAndCountAll({
            limit,
            offset,
        });
        const url = `/wishlists?with=shop&orderBy=created_at&sortedBy=desc`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
    async findWishlist(id) {
        return this.wishlistModel.findByPk(id);
    }
    async create(createWishlistDto) {
        return this.wishlistModel.create(createWishlistDto);
    }
    async update(id, updateWishlistDto) {
        await this.wishlistModel.update(updateWishlistDto, {
            where: { id },
        });
        return this.wishlistModel.findByPk(id);
    }
    async delete(id) {
        return this.wishlistModel.destroy({ where: { id } });
    }
    async isInWishlist(product_id) {
        const product = await this.productModel.findByPk(product_id);
        return (product === null || product === void 0 ? void 0 : product.in_wishlist) || false;
    }
    async toggle({ product_id }) {
        const product = await this.productModel.findByPk(product_id);
        if (product) {
            const newWishlistStatus = !product.in_wishlist;
            await this.productModel.update({ in_wishlist: newWishlistStatus }, { where: { id: product_id } });
            return newWishlistStatus;
        }
        return false;
    }
};
WishlistsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(wishlist_entity_1.Wishlist)),
    __param(1, (0, sequelize_1.InjectModel)(product_entity_1.Product)),
    __metadata("design:paramtypes", [Object, Object])
], WishlistsService);
exports.WishlistsService = WishlistsService;
//# sourceMappingURL=wishlists.service.js.map