{"version": 3, "file": "wishlist.entity.js", "sourceRoot": "", "sources": ["../../../src/wishlists/entities/wishlist.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,+DAO8B;AAC9B,2EAA+D;AAC/D,kEAAsD;AAMtD,IAAa,QAAQ,GAArB,MAAa,QAAS,SAAQ,4BAAK;;;;CA2BlC,CAAA;AArBC;IALC,IAAA,6BAAM,EAAC;QACN,IAAI,EAAE,+BAAQ,CAAC,OAAO;QACtB,aAAa,EAAE,IAAI;QACnB,UAAU,EAAE,IAAI;KACjB,CAAC;;oCACS;AAGX;IADC,IAAA,gCAAS,EAAC,GAAG,EAAE,CAAC,wBAAO,CAAC;8BAChB,wBAAO;yCAAC;AAOjB;IALC,IAAA,iCAAU,EAAC,GAAG,EAAE,CAAC,wBAAO,CAAC;IACzB,IAAA,6BAAM,EAAC;QACN,IAAI,EAAE,+BAAQ,CAAC,MAAM;QACrB,SAAS,EAAE,KAAK;KACjB,CAAC;;4CACiB;AAGnB;IADC,IAAA,gCAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,CAAC;8BAChB,kBAAI;sCAAC;AAOX;IALC,IAAA,iCAAU,EAAC,GAAG,EAAE,CAAC,kBAAI,CAAC;IACtB,IAAA,6BAAM,EAAC;QACN,IAAI,EAAE,+BAAQ,CAAC,MAAM;QACrB,SAAS,EAAE,KAAK;KACjB,CAAC;;yCACc;AA1BL,QAAQ;IAJpB,IAAA,4BAAK,EAAC;QACL,SAAS,EAAE,WAAW;QACtB,UAAU,EAAE,IAAI;KACjB,CAAC;GACW,QAAQ,CA2BpB;AA3BY,4BAAQ"}