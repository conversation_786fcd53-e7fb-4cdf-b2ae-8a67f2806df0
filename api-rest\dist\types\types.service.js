"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypesService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const type_entity_1 = require("./entities/type.entity");
let TypesService = class TypesService {
    constructor(typeModel) {
        this.typeModel = typeModel;
    }
    async getTypes({ text, search }) {
        let data = await this.typeModel.findAll();
        return data;
    }
    async getTypeBySlug(slug) {
        return this.typeModel.findOne({ where: { slug } });
    }
    async create(createTypeDto) {
        return this.typeModel.create(createTypeDto);
    }
    findAll() {
        return `This action returns all types`;
    }
    findOne(id) {
        return `This action returns a #${id} type`;
    }
    async update(id, updateTypeDto) {
        await this.typeModel.update(updateTypeDto, { where: { id } });
        return this.typeModel.findByPk(id);
    }
    remove(id) {
        return `This action removes a #${id} type`;
    }
};
TypesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(type_entity_1.Type)),
    __metadata("design:paramtypes", [Object])
], TypesService);
exports.TypesService = TypesService;
//# sourceMappingURL=types.service.js.map