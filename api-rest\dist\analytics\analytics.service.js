"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const analytics_entity_1 = require("./entities/analytics.entity");
const category_wise_product_entity_1 = require("./entities/category-wise-product.entity");
const product_entity_1 = require("../products/entities/product.entity");
const top_rate_product_entity_1 = require("./entities/top-rate-product.entity");
let AnalyticsService = class AnalyticsService {
    constructor(analyticsModel, categoryWiseProductModel, productModel, topRateProductModel) {
        this.analyticsModel = analyticsModel;
        this.categoryWiseProductModel = categoryWiseProductModel;
        this.productModel = productModel;
        this.topRateProductModel = topRateProductModel;
    }
    async findAll() {
        return this.analyticsModel.findAll({ include: { all: true } });
    }
    async findAllCategoryWiseProduct() {
        return this.categoryWiseProductModel.findAll();
    }
    async findAllLowStockProducts() {
        return this.productModel.findAll({
            where: {
                quantity: { lt: 10 },
            },
        });
    }
    async findAllTopRateProduct() {
        return this.topRateProductModel.findAll({
            order: [['actual_rating', 'DESC']],
        });
    }
};
AnalyticsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(analytics_entity_1.Analytics)),
    __param(1, (0, sequelize_1.InjectModel)(category_wise_product_entity_1.CategoryWiseProduct)),
    __param(2, (0, sequelize_1.InjectModel)(product_entity_1.Product)),
    __param(3, (0, sequelize_1.InjectModel)(top_rate_product_entity_1.TopRateProduct)),
    __metadata("design:paramtypes", [Object, Object, Object, Object])
], AnalyticsService);
exports.AnalyticsService = AnalyticsService;
//# sourceMappingURL=analytics.service.js.map