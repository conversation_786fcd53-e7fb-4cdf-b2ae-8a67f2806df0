#!/usr/bin/env node

/**
 * oneKart E-Commerce Development Startup Script (Node.js)
 * Cross-platform script that works on Windows, macOS, and Linux
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    purple: '\x1b[35m'
};

// Global process references
let apiProcess, adminProcess, shopProcess;

// Utility functions
function printStatus(message) {
    console.log(`${colors.blue}[oneKart]${colors.reset} ${message}`);
}

function printSuccess(message) {
    console.log(`${colors.green}[SUCCESS]${colors.reset} ${message}`);
}

function printWarning(message) {
    console.log(`${colors.yellow}[WARNING]${colors.reset} ${message}`);
}

function printError(message) {
    console.log(`${colors.red}[ERROR]${colors.reset} ${message}`);
}

function printHeader() {
    console.log(`${colors.purple}================================${colors.reset}`);
    console.log(`${colors.purple}  oneKart E-Commerce Platform${colors.reset}`);
    console.log(`${colors.purple}     Development Mode${colors.reset}`);
    console.log(`${colors.purple}================================${colors.reset}`);
}

// Check if a command exists
function commandExists(command) {
    return new Promise((resolve) => {
        exec(`${command} --version`, (error) => {
            resolve(!error);
        });
    });
}

// Check if port is in use
function isPortInUse(port) {
    return new Promise((resolve) => {
        const server = require('net').createServer();
        server.listen(port, () => {
            server.once('close', () => resolve(false));
            server.close();
        });
        server.on('error', () => resolve(true));
    });
}

// Kill process on port (cross-platform)
function killProcessOnPort(port) {
    return new Promise((resolve) => {
        const isWindows = process.platform === 'win32';
        const command = isWindows 
            ? `netstat -ano | findstr :${port}`
            : `lsof -ti :${port}`;
        
        exec(command, (error, stdout) => {
            if (error || !stdout) {
                resolve();
                return;
            }
            
            if (isWindows) {
                const lines = stdout.split('\n');
                const pids = lines
                    .map(line => line.trim().split(/\s+/).pop())
                    .filter(pid => pid && !isNaN(pid));
                
                pids.forEach(pid => {
                    exec(`taskkill /PID ${pid} /F`, () => {});
                });
            } else {
                const pids = stdout.trim().split('\n');
                pids.forEach(pid => {
                    if (pid) exec(`kill -9 ${pid}`, () => {});
                });
            }
            
            setTimeout(resolve, 2000);
        });
    });
}

// Wait for service to be ready
function waitForService(url, serviceName, maxAttempts = 30) {
    return new Promise((resolve) => {
        printStatus(`Waiting for ${serviceName} to be ready...`);
        
        let attempts = 0;
        const checkService = () => {
            attempts++;
            
            const request = http.get(url, (res) => {
                if (res.statusCode === 200) {
                    printSuccess(`${serviceName} is ready!`);
                    resolve(true);
                } else {
                    retryCheck();
                }
            });
            
            request.on('error', retryCheck);
            request.setTimeout(5000, () => {
                request.destroy();
                retryCheck();
            });
        };
        
        const retryCheck = () => {
            if (attempts >= maxAttempts) {
                printError(`${serviceName} failed to start within expected time`);
                resolve(false);
                return;
            }
            
            process.stdout.write('.');
            setTimeout(checkService, 2000);
        };
        
        checkService();
    });
}

// Check prerequisites
async function checkPrerequisites() {
    printStatus('Checking prerequisites...');
    
    // Check Node.js version
    const nodeVersion = process.version.substring(1).split('.')[0];
    if (parseInt(nodeVersion) < 18) {
        printError(`Node.js version 18 or higher is required. Current version: ${process.version}`);
        process.exit(1);
    }
    
    // Check npm
    if (!(await commandExists('npm'))) {
        printError('npm is not installed.');
        process.exit(1);
    }
    
    printSuccess('Prerequisites check passed');
}

// Install dependencies
async function installDependencies() {
    printStatus('Installing dependencies...');
    
    const installInDirectory = (dir, command) => {
        return new Promise((resolve, reject) => {
            printStatus(`Installing ${dir} dependencies...`);
            const child = spawn(command.split(' ')[0], command.split(' ').slice(1), {
                cwd: path.join(process.cwd(), dir),
                stdio: 'inherit',
                shell: true
            });
            
            child.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`Failed to install ${dir} dependencies`));
                }
            });
        });
    };
    
    try {
        await installInDirectory('api-rest', 'npm install --legacy-peer-deps');
        await installInDirectory('admin-rest', 'npm install');
        await installInDirectory('shop', 'npm install');
        printSuccess('Dependencies installed successfully');
    } catch (error) {
        printError(error.message);
        process.exit(1);
    }
}

// Start a service
function startService(name, directory, command, port) {
    return new Promise(async (resolve) => {
        printStatus(`Starting ${name}...`);
        await killProcessOnPort(port);
        
        const child = spawn(command.split(' ')[0], command.split(' ').slice(1), {
            cwd: path.join(process.cwd(), directory),
            stdio: 'pipe',
            shell: true,
            detached: false
        });
        
        printStatus(`${name} starting with PID: ${child.pid}`);
        
        // Log output to files
        const logFile = fs.createWriteStream(`${name.toLowerCase().replace(' ', '-')}.log`);
        child.stdout.pipe(logFile);
        child.stderr.pipe(logFile);
        
        child.on('error', (error) => {
            printError(`Failed to start ${name}: ${error.message}`);
        });
        
        resolve(child);
    });
}

// Start all services
async function startServices() {
    // Start API server
    apiProcess = await startService('API Server', 'api-rest', 'npm run start:dev', 9000);
    await waitForService('http://localhost:9000/api', 'API Server');
    printSuccess('API server started on http://localhost:9000');
    
    // Start Admin dashboard
    adminProcess = await startService('Admin Dashboard', 'admin-rest', 'npm run dev', 3002);
    await waitForService('http://localhost:3002', 'Admin Dashboard');
    printSuccess('Admin dashboard started on http://localhost:3002');
    
    // Start Shop frontend
    shopProcess = await startService('Shop Frontend', 'shop', 'npx next dev -p 3005', 3005);
    await waitForService('http://localhost:3005', 'Shop Frontend');
    printSuccess('Shop frontend started on http://localhost:3005');
}

// Show running services
function showServices() {
    console.log('');
    printHeader();
    console.log('');
    printSuccess('🚀 oneKart E-Commerce Platform is running in development mode!');
    console.log('');
    console.log(`${colors.blue}📊 Admin Dashboard:${colors.reset} http://localhost:3002`);
    console.log(`${colors.blue}🛍️  Shop Frontend:${colors.reset}   http://localhost:3005`);
    console.log(`${colors.blue}🔧 API Server:${colors.reset}      http://localhost:9000/api`);
    console.log('');
    console.log(`${colors.yellow}💡 Features:${colors.reset}`);
    console.log('   • Purple Nebula Dark Theme');
    console.log('   • oneKart Branding');
    console.log('   • Hot Reload Enabled');
    console.log('   • Development Tools Active');
    console.log('');
    console.log(`${colors.green}Press Ctrl+C to stop all services${colors.reset}`);
    console.log('');
}

// Cleanup function
function cleanup() {
    printStatus('Shutting down services...');
    
    if (apiProcess && !apiProcess.killed) {
        apiProcess.kill('SIGTERM');
    }
    if (adminProcess && !adminProcess.killed) {
        adminProcess.kill('SIGTERM');
    }
    if (shopProcess && !shopProcess.killed) {
        shopProcess.kill('SIGTERM');
    }
    
    // Clean up log files
    ['api-server.log', 'admin-dashboard.log', 'shop-frontend.log'].forEach(file => {
        if (fs.existsSync(file)) {
            fs.unlinkSync(file);
        }
    });
    
    printSuccess('All services stopped');
    process.exit(0);
}

// Main execution
async function main() {
    const args = process.argv.slice(2);
    const shouldInstall = args.includes('--install') || args.includes('-i');
    const showHelp = args.includes('--help') || args.includes('-h');
    
    if (showHelp) {
        console.log('oneKart Development Server - Node.js Script');
        console.log('');
        console.log('Usage: node start-dev.js [OPTIONS]');
        console.log('');
        console.log('Options:');
        console.log('  --install, -i    Install dependencies before starting');
        console.log('  --help, -h       Show this help message');
        console.log('');
        console.log('Examples:');
        console.log('  node start-dev.js                # Start with existing dependencies');
        console.log('  node start-dev.js --install      # Install dependencies and start');
        console.log('');
        return;
    }
    
    printHeader();
    
    // Set up signal handlers
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
    
    try {
        await checkPrerequisites();
        
        if (shouldInstall) {
            await installDependencies();
        }
        
        await startServices();
        showServices();
        
        // Keep the script running
        process.stdin.resume();
        
    } catch (error) {
        printError(`Failed to start services: ${error.message}`);
        process.exit(1);
    }
}

// Run the script
main().catch(console.error);
