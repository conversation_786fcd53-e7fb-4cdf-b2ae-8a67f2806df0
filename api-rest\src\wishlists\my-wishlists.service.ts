import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import Fuse from 'fuse.js';
import { paginate } from 'src/common/pagination/paginate';
import { Wishlist } from './entities/wishlist.entity';
import { GetWishlistDto } from './dto/get-wishlists.dto';
import { CreateWishlistDto } from './dto/create-wishlists.dto';
import { UpdateWishlistDto } from './dto/update-wishlists.dto';
import { Product } from '../products/entities/product.entity';

@Injectable()
export class MyWishlistService {
  constructor(
    @InjectModel(Wishlist)
    private wishlistModel: typeof Wishlist,
    @InjectModel(Product)
    private productModel: typeof Product,
  ) {}

  async findAMyWishlists({ limit, page, search }: GetWishlistDto) {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const offset = (page - 1) * limit;

    // TODO: Implement proper wishlist query with products
    const { count, rows: data } = await this.productModel.findAndCountAll({
      limit: 6, // Mock: return 6 products as wishlist items
      offset: 1,
    });

    const url = `/my-wishlists?with=shop&orderBy=created_at&sortedBy=desc`;
    return {
      data,
      ...paginate(count, page, limit, data.length, url),
    };
  }

  async findAMyWishlist(id: number): Promise<Wishlist | null> {
    return this.wishlistModel.findByPk(id);
  }

  async create(createWishlistDto: CreateWishlistDto): Promise<Wishlist> {
    return this.wishlistModel.create(createWishlistDto as any);
  }

  async update(
    id: number,
    updateWishlistDto: UpdateWishlistDto,
  ): Promise<Wishlist | null> {
    await this.wishlistModel.update(updateWishlistDto as any, {
      where: { id },
    });
    return this.wishlistModel.findByPk(id);
  }

  async delete(id: number): Promise<number> {
    return this.wishlistModel.destroy({ where: { id } });
  }
}
