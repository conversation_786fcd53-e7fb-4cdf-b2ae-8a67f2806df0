"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_ownership-transfer_ownership-transfer-delete-view_tsx"],{

/***/ "./src/components/ownership-transfer/ownership-transfer-delete-view.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/ownership-transfer/ownership-transfer-delete-view.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_ownership_transfer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/ownership-transfer */ \"./src/data/ownership-transfer.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst OwnershipTransferDeleteView = ()=>{\n    _s();\n    const { mutate: deleteOwnershipTransfer, isLoading: loading } = (0,_data_ownership_transfer__WEBPACK_IMPORTED_MODULE_3__.useDeleteOwnerTransferMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteOwnershipTransfer({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\ownership-transfer\\\\ownership-transfer-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OwnershipTransferDeleteView, \"u8ZLZLmbaIjvgqGEsBN186nIedQ=\", false, function() {\n    return [\n        _data_ownership_transfer__WEBPACK_IMPORTED_MODULE_3__.useDeleteOwnerTransferMutation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction\n    ];\n});\n_c = OwnershipTransferDeleteView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (OwnershipTransferDeleteView);\nvar _c;\n$RefreshReg$(_c, \"OwnershipTransferDeleteView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ownership-transfer/ownership-transfer-delete-view.tsx\n"));

/***/ }),

/***/ "./src/data/client/ownership-transfer.ts":
/*!***********************************************!*\
  !*** ./src/data/client/ownership-transfer.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ownershipTransferClient: function() { return /* binding */ ownershipTransferClient; }\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\n\n\n\nconst ownershipTransferClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER),\n    all: function() {\n        let { transaction_identifier, shop_id, ...params } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params\n        });\n    },\n    get (param) {\n        let { transaction_identifier, language, shop_id, request_view_type } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(\"\".concat(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, \"/\").concat(transaction_identifier), {\n            language,\n            shop_id,\n            transaction_identifier,\n            request_view_type\n        });\n    },\n    paginated: (param)=>{\n        let { transaction_identifier, shop_id, ...params } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                transaction_identifier\n            })\n        });\n    },\n    approve: (variables)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, variables);\n    },\n    disapprove: (variables)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, variables);\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/ownership-transfer.ts\n"));

/***/ }),

/***/ "./src/data/ownership-transfer.ts":
/*!****************************************!*\
  !*** ./src/data/ownership-transfer.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApproveOwnerTransferMutation: function() { return /* binding */ useApproveOwnerTransferMutation; },\n/* harmony export */   useCreateOwnerTransferMutation: function() { return /* binding */ useCreateOwnerTransferMutation; },\n/* harmony export */   useDeleteOwnerTransferMutation: function() { return /* binding */ useDeleteOwnerTransferMutation; },\n/* harmony export */   useDisApproveOwnerTransferMutation: function() { return /* binding */ useDisApproveOwnerTransferMutation; },\n/* harmony export */   useOwnerShipTransferLoadMoreQuery: function() { return /* binding */ useOwnerShipTransferLoadMoreQuery; },\n/* harmony export */   useOwnerShipTransferQuery: function() { return /* binding */ useOwnerShipTransferQuery; },\n/* harmony export */   useOwnerShipTransfersQuery: function() { return /* binding */ useOwnerShipTransfersQuery; },\n/* harmony export */   useUpdateOwnerTransferMutation: function() { return /* binding */ useUpdateOwnerTransferMutation; }\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\n/* harmony import */ var _data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/data/client/ownership-transfer */ \"./src/data/client/ownership-transfer.ts\");\n\n\n\n\n\n\n\n\n\n// Read Single Ownership transfer request\nconst useOwnerShipTransferQuery = (param)=>{\n    let { transaction_identifier, language, shop_id, request_view_type } = param;\n    const { data, error, isLoading, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER,\n        {\n            transaction_identifier,\n            language,\n            shop_id\n        }\n    ], ()=>_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.get({\n            transaction_identifier,\n            language,\n            shop_id,\n            request_view_type\n        }));\n    return {\n        ownershipTransfer: data,\n        error,\n        loading: isLoading,\n        refetch\n    };\n};\n// Read All Ownership transfer request\nconst useOwnerShipTransfersQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER,\n        options\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.paginated(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        keepPreviousData: true\n    });\n    var _data_data;\n    return {\n        ownershipTransfer: (_data_data = data === null || data === void 0 ? void 0 : data.data) !== null && _data_data !== void 0 ? _data_data : [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n// Read All Ownership transfer request paginated\nconst useOwnerShipTransferLoadMoreQuery = (options, config)=>{\n    const { data, error, isLoading, isFetchingNextPage, hasNextPage, fetchNextPage } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useInfiniteQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER,\n        options\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.all(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        ...config,\n        getNextPageParam: (param)=>{\n            let { current_page, last_page } = param;\n            return last_page > current_page && {\n                page: current_page + 1\n            };\n        }\n    });\n    function handleLoadMore() {\n        fetchNextPage();\n    }\n    var _data_pages_flatMap;\n    return {\n        ownershipTransfer: (_data_pages_flatMap = data === null || data === void 0 ? void 0 : data.pages.flatMap((page)=>page === null || page === void 0 ? void 0 : page.data)) !== null && _data_pages_flatMap !== void 0 ? _data_pages_flatMap : [],\n        paginatorInfo: Array.isArray(data === null || data === void 0 ? void 0 : data.pages) ? data === null || data === void 0 ? void 0 : data.pages[data.pages.length - 1] : null,\n        error,\n        hasNextPage,\n        loading: isLoading,\n        isLoadingMore: isFetchingNextPage,\n        loadMore: handleLoadMore\n    };\n};\n// Create Ownership transfer request\nconst useCreateOwnerTransferMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? \"/\".concat(router.query.shop).concat(_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list) : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\n// Update Ownership transfer request\nconst useUpdateOwnerTransferMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? \"/\".concat(router.query.shop).concat(_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list) : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list;\n            await router.push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\n// Delete Ownership transfer request\nconst useDeleteOwnerTransferMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\n// approve Ownership transfer request\nconst useApproveOwnerTransferMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.approve, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        }\n    });\n};\n// disapprove Ownership transfer request\nconst useDisApproveOwnerTransferMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.disapprove, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        }\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/ownership-transfer.ts\n"));

/***/ })

}]);