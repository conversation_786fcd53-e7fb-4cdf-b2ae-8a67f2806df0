"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_faqs_faq-delete-view_tsx"],{

/***/ "./src/components/faqs/faq-delete-view.tsx":
/*!*************************************************!*\
  !*** ./src/components/faqs/faq-delete-view.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_faqs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/faqs */ \"./src/data/faqs.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst FaqsDeleteView = ()=>{\n    _s();\n    const { mutate: deleteFaq, isLoading: loading } = (0,_data_faqs__WEBPACK_IMPORTED_MODULE_3__.useDeleteFaqsMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteFaq({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\faqs\\\\faq-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FaqsDeleteView, \"QH3GhQT2ZZo1qDEA2hMh0iDe30Y=\", false, function() {\n    return [\n        _data_faqs__WEBPACK_IMPORTED_MODULE_3__.useDeleteFaqsMutation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction\n    ];\n});\n_c = FaqsDeleteView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FaqsDeleteView);\nvar _c;\n$RefreshReg$(_c, \"FaqsDeleteView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/faqs/faq-delete-view.tsx\n"));

/***/ }),

/***/ "./src/data/client/faqs.ts":
/*!*********************************!*\
  !*** ./src/data/client/faqs.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   faqsClient: function() { return /* binding */ faqsClient; }\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\n\n\n\nconst faqsClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS),\n    all: function() {\n        let { faq_title, shop_id, ...params } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                faq_title,\n                shop_id\n            })\n        });\n    },\n    get (param) {\n        let { id, language } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(\"\".concat(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS, \"/\").concat(id), {\n            language\n        });\n    },\n    paginated: (param)=>{\n        let { faq_title, shop_id, ...params } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                faq_title,\n                shop_id\n            })\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/faqs.ts\n"));

/***/ }),

/***/ "./src/data/faqs.ts":
/*!**************************!*\
  !*** ./src/data/faqs.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateFaqsMutation: function() { return /* binding */ useCreateFaqsMutation; },\n/* harmony export */   useDeleteFaqsMutation: function() { return /* binding */ useDeleteFaqsMutation; },\n/* harmony export */   useFaqQuery: function() { return /* binding */ useFaqQuery; },\n/* harmony export */   useFaqsLoadMoreQuery: function() { return /* binding */ useFaqsLoadMoreQuery; },\n/* harmony export */   useFaqsQuery: function() { return /* binding */ useFaqsQuery; },\n/* harmony export */   useUpdateFaqsMutation: function() { return /* binding */ useUpdateFaqsMutation; }\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\n/* harmony import */ var _data_client_faqs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/data/client/faqs */ \"./src/data/client/faqs.ts\");\n\n\n\n\n\n\n\n\n\n// Read Single FAQ\nconst useFaqQuery = (param)=>{\n    let { id, language } = param;\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS,\n        {\n            id,\n            language\n        }\n    ], ()=>_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.get({\n            id,\n            language\n        }));\n    return {\n        faqs: data,\n        error,\n        loading: isLoading\n    };\n};\n// Read All FAQs\nconst useFaqsQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS,\n        options\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.paginated(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        keepPreviousData: true\n    });\n    var _data_data;\n    return {\n        faqs: (_data_data = data === null || data === void 0 ? void 0 : data.data) !== null && _data_data !== void 0 ? _data_data : [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n// Read All FAQs paginated\nconst useFaqsLoadMoreQuery = (options, config)=>{\n    const { data, error, isLoading, isFetchingNextPage, hasNextPage, fetchNextPage } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useInfiniteQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS,\n        options\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.all(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        ...config,\n        getNextPageParam: (param)=>{\n            let { current_page, last_page } = param;\n            return last_page > current_page && {\n                page: current_page + 1\n            };\n        }\n    });\n    function handleLoadMore() {\n        fetchNextPage();\n    }\n    var _data_pages_flatMap;\n    return {\n        faqs: (_data_pages_flatMap = data === null || data === void 0 ? void 0 : data.pages.flatMap((page)=>page === null || page === void 0 ? void 0 : page.data)) !== null && _data_pages_flatMap !== void 0 ? _data_pages_flatMap : [],\n        paginatorInfo: Array.isArray(data === null || data === void 0 ? void 0 : data.pages) ? data === null || data === void 0 ? void 0 : data.pages[data.pages.length - 1] : null,\n        error,\n        hasNextPage,\n        loading: isLoading,\n        isLoadingMore: isFetchingNextPage,\n        loadMore: handleLoadMore\n    };\n};\n// Create FAQ\nconst useCreateFaqsMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? \"/\".concat(router.query.shop).concat(_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list) : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\n// Update FAQ\nconst useUpdateFaqsMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? \"/\".concat(router.query.shop).concat(_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list) : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list;\n            await router.push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\n// Delete FAQ\nconst useDeleteFaqsMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/faqs.ts\n"));

/***/ })

}]);