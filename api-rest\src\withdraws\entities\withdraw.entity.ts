import {
  Column,
  Model,
  Table,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Shop } from 'src/shops/entities/shop.entity';

@Table({
  tableName: 'withdraws',
  timestamps: true,
})
export class Withdraw extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
  })
  amount: number;

  @Column({
    type: DataType.ENUM(
      'Approved',
      'Pending',
      'On hold',
      'Rejected',
      'Processing',
    ),
    allowNull: false,
    defaultValue: 'Pending',
  })
  status: WithdrawStatus;

  @ForeignKey(() => Shop)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  shop_id: number;

  @BelongsTo(() => Shop)
  shop: Shop;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  payment_method: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  details: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  note: string;
}

export enum WithdrawStatus {
  APPROVED = 'Approved',
  PENDING = 'Pending',
  ON_HOLD = 'On hold',
  REJECTED = 'Rejected',
  PROCESSING = 'Processing',
}
