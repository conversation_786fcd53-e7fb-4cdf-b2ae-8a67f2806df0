"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_product_product-delete-view_tsx";
exports.ids = ["src_components_product_product-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/product/product-delete-view.tsx":
/*!********************************************************!*\
  !*** ./src/components/product/product-delete-view.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_product__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/product */ \"./src/data/product.ts\");\n/* harmony import */ var _utils_form_error__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/form-error */ \"./src/utils/form-error.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_product__WEBPACK_IMPORTED_MODULE_3__, _utils_form_error__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_product__WEBPACK_IMPORTED_MODULE_3__, _utils_form_error__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst ProductDeleteView = ()=>{\n    const { mutate: deleteProduct, isLoading: loading } = (0,_data_product__WEBPACK_IMPORTED_MODULE_3__.useDeleteProductMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    async function handleDelete() {\n        try {\n            deleteProduct({\n                id: data\n            });\n            closeModal();\n        } catch (error) {\n            closeModal();\n            (0,_utils_form_error__WEBPACK_IMPORTED_MODULE_4__.getErrorMessage)(error);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\product\\\\product-delete-view.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/product/product-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/product.ts":
/*!************************************!*\
  !*** ./src/data/client/product.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   productClient: () => (/* binding */ productClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst productClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.PRODUCTS),\n    get ({ slug, language }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.PRODUCTS}/${slug}`, {\n            language,\n            with: \"type;shop;categories;tags;variations.attribute.values;variation_options;variation_options.digital_file;author;manufacturer;digital_file\"\n        });\n    },\n    paginated: ({ type, name, categories, shop_id, product_type, status, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.PRODUCTS, {\n            searchJoin: \"and\",\n            with: \"shop;type;categories\",\n            shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                name,\n                categories,\n                shop_id,\n                product_type,\n                status\n            })\n        });\n    },\n    popular ({ shop_id, ...params }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.POPULAR_PRODUCTS, {\n            searchJoin: \"and\",\n            with: \"type;shop\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                shop_id\n            })\n        });\n    },\n    lowStock ({ shop_id, ...params }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.LOW_STOCK_PRODUCTS_ANALYTICS, {\n            searchJoin: \"and\",\n            with: \"type;shop\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                shop_id\n            })\n        });\n    },\n    generateDescription: (data)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.GENERATE_DESCRIPTION, data);\n    },\n    newOrInActiveProducts: ({ user_id, shop_id, status, name, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.NEW_OR_INACTIVE_PRODUCTS, {\n            searchJoin: \"and\",\n            user_id,\n            shop_id,\n            status,\n            name,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                status,\n                name\n            })\n        });\n    },\n    lowOrOutOfStockProducts: ({ user_id, shop_id, status, categories, name, type, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.LOW_OR_OUT_OF_STOCK_PRODUCTS, {\n            searchJoin: \"and\",\n            user_id,\n            shop_id,\n            status,\n            name,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                status,\n                name,\n                categories,\n                type\n            })\n        });\n    },\n    productByCategory ({ limit, language }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.CATEGORY_WISE_PRODUCTS, {\n            limit,\n            language\n        });\n    },\n    // productByCategory({ shop_id, ...params }: Partial<ProductQueryOptions>) {\n    //   return HttpClient.get<Product[]>(API_ENDPOINTS.CATEGORY_WISE_PRODUCTS, {\n    //     searchJoin: 'and',\n    //     ...params,\n    //     search: HttpClient.formatSearchParams({ shop_id }),\n    //   });\n    // },\n    mostSoldProductByCategory ({ shop_id, ...params }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.CATEGORY_WISE_PRODUCTS_SALE, {\n            searchJoin: \"and\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                shop_id\n            })\n        });\n    },\n    getProductsByFlashSale: ({ user_id, shop_id, slug, name, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.PRODUCTS_BY_FLASH_SALE, {\n            searchJoin: \"and\",\n            user_id,\n            shop_id,\n            slug,\n            name,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    },\n    topRated ({ shop_id, ...params }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TOP_RATED_PRODUCTS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                shop_id\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/product.ts\n");

/***/ }),

/***/ "./src/data/product.ts":
/*!*****************************!*\
  !*** ./src/data/product.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateProductMutation: () => (/* binding */ useCreateProductMutation),\n/* harmony export */   useDeleteProductMutation: () => (/* binding */ useDeleteProductMutation),\n/* harmony export */   useGenerateDescriptionMutation: () => (/* binding */ useGenerateDescriptionMutation),\n/* harmony export */   useInActiveProductsQuery: () => (/* binding */ useInActiveProductsQuery),\n/* harmony export */   useProductQuery: () => (/* binding */ useProductQuery),\n/* harmony export */   useProductStockQuery: () => (/* binding */ useProductStockQuery),\n/* harmony export */   useProductsByFlashSaleQuery: () => (/* binding */ useProductsByFlashSaleQuery),\n/* harmony export */   useProductsQuery: () => (/* binding */ useProductsQuery),\n/* harmony export */   useUpdateProductMutation: () => (/* binding */ useUpdateProductMutation)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _client_product__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/product */ \"./src/data/client/product.ts\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_1__, _client_product__WEBPACK_IMPORTED_MODULE_5__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _config__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_1__, _client_product__WEBPACK_IMPORTED_MODULE_5__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _config__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst useCreateProductMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(_client_product__WEBPACK_IMPORTED_MODULE_5__.productClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_7__.Routes.product.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_7__.Routes.product.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.PRODUCTS);\n        },\n        onError: (error)=>{\n            const { data, status } = error?.response;\n            if (status === 422) {\n                const errorMessage = Object.values(data).flat();\n                react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(errorMessage[0]);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(t(`common:${error?.response?.data.message}`));\n            }\n        }\n    });\n};\nconst useUpdateProductMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(_client_product__WEBPACK_IMPORTED_MODULE_5__.productClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_7__.Routes.product.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_7__.Routes.product.list;\n            await router.push(`${generateRedirectUrl}/${data?.slug}/edit`, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.PRODUCTS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\nconst useDeleteProductMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(_client_product__WEBPACK_IMPORTED_MODULE_5__.productClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.PRODUCTS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\nconst useProductQuery = ({ slug, language })=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.PRODUCTS,\n        {\n            slug,\n            language\n        }\n    ], ()=>_client_product__WEBPACK_IMPORTED_MODULE_5__.productClient.get({\n            slug,\n            language\n        }));\n    return {\n        product: data,\n        error,\n        isLoading\n    };\n};\nconst useProductsQuery = (params, options = {})=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.PRODUCTS,\n        params\n    ], ({ queryKey, pageParam })=>_client_product__WEBPACK_IMPORTED_MODULE_5__.productClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true,\n        ...options\n    });\n    return {\n        products: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\nconst useGenerateDescriptionMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(_client_product__WEBPACK_IMPORTED_MODULE_5__.productClient.generateDescription, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"Generated...\"));\n        },\n        // Always refetch after error or success:\n        onSettled: (data)=>{\n            queryClient.refetchQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.GENERATE_DESCRIPTION);\n            data;\n        }\n    });\n};\nconst useInActiveProductsQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.NEW_OR_INACTIVE_PRODUCTS,\n        options\n    ], ({ queryKey, pageParam })=>_client_product__WEBPACK_IMPORTED_MODULE_5__.productClient.newOrInActiveProducts(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        products: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\nconst useProductStockQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.LOW_OR_OUT_OF_STOCK_PRODUCTS,\n        options\n    ], ({ queryKey, pageParam })=>_client_product__WEBPACK_IMPORTED_MODULE_5__.productClient.lowOrOutOfStockProducts(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        products: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n// Read All products by flash sale\nconst useProductsByFlashSaleQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.PRODUCTS_BY_FLASH_SALE,\n        options\n    ], ({ queryKey, pageParam })=>_client_product__WEBPACK_IMPORTED_MODULE_5__.productClient.getProductsByFlashSale(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        products: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/product.ts\n");

/***/ }),

/***/ "./src/utils/form-error.tsx":
/*!**********************************!*\
  !*** ./src/utils/form-error.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_1__]);\njs_cookie__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction getErrorMessage(error) {\n    let processedError = {\n        message: \"\",\n        validation: []\n    };\n    if (error.graphQLErrors) {\n        for (const graphQLError of error.graphQLErrors){\n            if (graphQLError.extensions && graphQLError.extensions.category === \"validation\") {\n                processedError[\"message\"] = graphQLError.message;\n                processedError[\"validation\"] = graphQLError.extensions.validation;\n                return processedError;\n            } else if (graphQLError.extensions && graphQLError.extensions.category === \"authorization\") {\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_token\");\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_permissions\");\n                next_router__WEBPACK_IMPORTED_MODULE_0___default().push(\"/\");\n            }\n        }\n    }\n    processedError[\"message\"] = error.message;\n    return processedError;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/form-error.tsx\n");

/***/ })

};
;