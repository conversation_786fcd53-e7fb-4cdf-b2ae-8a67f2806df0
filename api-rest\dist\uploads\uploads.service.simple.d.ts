/// <reference types="multer" />
export interface UploadedFile {
    id: string;
    original: string;
    thumbnail: string;
    file_name: string;
    size: number;
    mime_type: string;
}
export declare class UploadsService {
    uploadFiles(files: Express.Multer.File[]): Promise<UploadedFile[]>;
    deleteFile(fileName: string): Promise<void>;
    getFileUrl(fileName: string): Promise<string>;
    getPublicUrl(fileName: string): string;
}
