"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_checkout_contact_add-or-update_tsx";
exports.ids = ["src_components_checkout_contact_add-or-update_tsx"];
exports.modules = {

/***/ "./src/components/checkout/contact/add-or-update.tsx":
/*!***********************************************************!*\
  !*** ./src/components/checkout/contact/add-or-update.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddOrUpdateContact)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _components_otp_otp_form__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/otp/otp-form */ \"./src/components/otp/otp-form.tsx\");\n/* harmony import */ var _store_checkout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/checkout */ \"./src/store/checkout.ts\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jotai */ \"jotai\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\n/* harmony import */ var _components_otp_phone_number_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/otp/phone-number-form */ \"./src/components/otp/phone-number-form.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_otp_otp_form__WEBPACK_IMPORTED_MODULE_2__, _store_checkout__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_4__, _framework_settings__WEBPACK_IMPORTED_MODULE_6__, _components_otp_phone_number_form__WEBPACK_IMPORTED_MODULE_7__]);\n([_components_otp_otp_form__WEBPACK_IMPORTED_MODULE_2__, _store_checkout__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_4__, _framework_settings__WEBPACK_IMPORTED_MODULE_6__, _components_otp_phone_number_form__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction AddOrUpdateContact() {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"common\");\n    const { settings } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_6__.useSettings)();\n    const useOtp = settings?.useOtp;\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_1__.useModalAction)();\n    const [contactNumber, setContactNumber] = (0,jotai__WEBPACK_IMPORTED_MODULE_4__.useAtom)(_store_checkout__WEBPACK_IMPORTED_MODULE_3__.customerContactAtom);\n    function onSubmit({ phone_number }) {\n        setContactNumber(phone_number);\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col justify-center min-h-screen p-5 bg-light sm:p-8 md:min-h-0 md:rounded-xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"mb-5 text-sm font-semibold text-center text-heading sm:mb-6\",\n                children: [\n                    contactNumber ? t(\"text-update\") : t(\"text-add-new\"),\n                    \" \",\n                    t(\"text-contact-number\")\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            useOtp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_otp_otp_form__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                phoneNumber: contactNumber,\n                onVerifySuccess: onSubmit\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_otp_phone_number_form__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onSubmit: onSubmit,\n                phoneNumber: contactNumber\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/checkout/contact/add-or-update.tsx\n");

/***/ }),

/***/ "./src/components/otp/code-verify-form.tsx":
/*!*************************************************!*\
  !*** ./src/components/otp/code-verify-form.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OtpCodeForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_otp_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-otp-input */ \"react-otp-input\");\n/* harmony import */ var react_otp_input__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_otp_input__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_forms_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/forms/label */ \"./src/components/ui/forms/label.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _components_ui_forms_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/forms/form */ \"./src/components/ui/forms/form.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"react-hook-form\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! yup */ \"yup\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(yup__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_8__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_forms_form__WEBPACK_IMPORTED_MODULE_5__, react_hook_form__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_ui_forms_form__WEBPACK_IMPORTED_MODULE_5__, react_hook_form__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst otpLoginFormSchemaForExistingUser = yup__WEBPACK_IMPORTED_MODULE_7__.object().shape({\n    code: yup__WEBPACK_IMPORTED_MODULE_7__.string().required(\"error-code-required\")\n});\nfunction OtpCodeForm({ onSubmit, isLoading }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"common\");\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__.useModalAction)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-5 space-y-5 border border-gray-200 rounded\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_form__WEBPACK_IMPORTED_MODULE_5__.Form, {\n            onSubmit: onSubmit,\n            validationSchema: otpLoginFormSchemaForExistingUser,\n            children: ({ control, formState: { errors } })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_label__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    children: t(\"text-otp-code\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\code-verify-form.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_6__.Controller, {\n                                    control: control,\n                                    render: ({ field: { onChange, onBlur, value } })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_otp_input__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            value: value,\n                                            onChange: onChange,\n                                            numInputs: 6,\n                                            renderSeparator: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline-block\",\n                                                children: \"-\"\n                                            }, void 0, false, void 0, void 0),\n                                            containerStyle: \"flex items-center justify-between -mx-2\",\n                                            inputStyle: \"flex items-center justify-center !w-full mx-2 sm:!w-9 !px-0 appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-0 focus:ring-0 border border-border-base rounded focus:border-accent h-12\",\n                                            renderInput: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...props\n                                                }, void 0, false, void 0, void 0)\n                                        }, void 0, false, void 0, void 0),\n                                    name: \"code\",\n                                    defaultValue: \"\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\code-verify-form.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\code-verify-form.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"outline\",\n                                    onClick: closeModal,\n                                    className: \"hover:border-red-500 hover:bg-red-500\",\n                                    children: t(\"text-cancel\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\code-verify-form.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    loading: isLoading,\n                                    disabled: isLoading,\n                                    children: t(\"text-verify-code\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\code-verify-form.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\code-verify-form.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\code-verify-form.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\code-verify-form.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/otp/code-verify-form.tsx\n");

/***/ }),

/***/ "./src/components/otp/otp-form.tsx":
/*!*****************************************!*\
  !*** ./src/components/otp/otp-form.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OtpForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jotai */ \"jotai\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_otp_phone_number_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/otp/phone-number-form */ \"./src/components/otp/phone-number-form.tsx\");\n/* harmony import */ var _components_otp_atom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/otp/atom */ \"./src/components/otp/atom.ts\");\n/* harmony import */ var _components_otp_code_verify_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/otp/code-verify-form */ \"./src/components/otp/code-verify-form.tsx\");\n/* harmony import */ var _framework_user__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/framework/user */ \"./src/framework/rest/user.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([jotai__WEBPACK_IMPORTED_MODULE_2__, _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__, _components_otp_phone_number_form__WEBPACK_IMPORTED_MODULE_4__, _components_otp_atom__WEBPACK_IMPORTED_MODULE_5__, _components_otp_code_verify_form__WEBPACK_IMPORTED_MODULE_6__, _framework_user__WEBPACK_IMPORTED_MODULE_7__]);\n([jotai__WEBPACK_IMPORTED_MODULE_2__, _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__, _components_otp_phone_number_form__WEBPACK_IMPORTED_MODULE_4__, _components_otp_atom__WEBPACK_IMPORTED_MODULE_5__, _components_otp_code_verify_form__WEBPACK_IMPORTED_MODULE_6__, _framework_user__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction OtpForm({ phoneNumber, onVerifySuccess }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"common\");\n    const [otpState] = (0,jotai__WEBPACK_IMPORTED_MODULE_2__.useAtom)(_components_otp_atom__WEBPACK_IMPORTED_MODULE_5__.optAtom);\n    const { mutate: verifyOtpCode, isLoading: otpVerifyLoading } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_7__.useVerifyOtpCode)({\n        onVerifySuccess\n    });\n    const { mutate: sendOtpCode, isLoading, serverError, setServerError } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_7__.useSendOtpCode)({\n        verifyOnly: true\n    });\n    function onSendCodeSubmission({ phone_number }) {\n        sendOtpCode({\n            phone_number: `+${phone_number}`\n        });\n    }\n    function onVerifyCodeSubmission({ code }) {\n        verifyOtpCode({\n            code,\n            phone_number: otpState.phoneNumber,\n            otp_id: otpState.otpId\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            otpState.step === \"PhoneNumber\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        variant: \"error\",\n                        message: serverError && t(serverError),\n                        className: \"mb-4\",\n                        closeable: true,\n                        onClose: ()=>setServerError(null)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\otp-form.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_otp_phone_number_form__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        onSubmit: onSendCodeSubmission,\n                        isLoading: isLoading,\n                        phoneNumber: phoneNumber\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\otp-form.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            otpState.step === \"OtpForm\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_otp_code_verify_form__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onSubmit: onVerifyCodeSubmission,\n                isLoading: otpVerifyLoading\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\otp-form.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/otp/otp-form.tsx\n");

/***/ }),

/***/ "./src/components/otp/phone-number-form.tsx":
/*!**************************************************!*\
  !*** ./src/components/otp/phone-number-form.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PhoneNumberForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hook-form */ \"react-hook-form\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_forms_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/forms/form */ \"./src/components/ui/forms/form.tsx\");\n/* harmony import */ var _components_ui_forms_phone_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/forms/phone-input */ \"./src/components/ui/forms/phone-input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! yup */ \"yup\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(yup__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hook_form__WEBPACK_IMPORTED_MODULE_1__, _components_ui_forms_form__WEBPACK_IMPORTED_MODULE_3__]);\n([react_hook_form__WEBPACK_IMPORTED_MODULE_1__, _components_ui_forms_form__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst checkoutContactSchema = yup__WEBPACK_IMPORTED_MODULE_6__.object().shape({\n    phone_number: yup__WEBPACK_IMPORTED_MODULE_6__.string().required(\"error-contact-required\")\n});\nfunction PhoneNumberForm({ phoneNumber, onSubmit, isLoading, view }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n        onSubmit: onSubmit,\n        validationSchema: checkoutContactSchema,\n        className: \"w-full\",\n        useFormProps: {\n            defaultValues: {\n                phone_number: phoneNumber\n            }\n        },\n        children: ({ control, formState: { errors } })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex w-full items-center md:min-w-[360px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_1__.Controller, {\n                                name: \"phone_number\",\n                                control: control,\n                                render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_phone_input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        country: \"us\",\n                                        inputClass: \"!p-0 ltr:!pr-4 rtl:!pl-4 ltr:!pl-14 rtl:!pr-14 !flex !items-center !w-full !appearance-none !transition !duration-300 !ease-in-out !text-heading !text-sm focus:!outline-none focus:!ring-0 !border !border-border-base ltr:!border-r-0 rtl:!border-l-0 !rounded ltr:!rounded-r-none rtl:!rounded-l-none focus:!border-accent !h-12\",\n                                        dropdownClass: \"focus:!ring-0 !border !border-border-base !shadow-350\",\n                                        ...field\n                                    }, void 0, false, void 0, void 0)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\phone-number-form.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"!text-sm ltr:!rounded-l-none rtl:!rounded-r-none\",\n                                loading: isLoading,\n                                disabled: isLoading,\n                                children: view === \"login\" ? t(\"text-send-otp\") : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        Boolean(phoneNumber) ? t(\"text-update\") : t(\"text-add\"),\n                                        \" \",\n                                        t(\"nav-menu-contact\")\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\phone-number-form.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\phone-number-form.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this),\n                    errors.phone_number?.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-xs text-red-500 ltr:text-left rtl:text-right\",\n                        children: t(errors.phone_number.message)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\phone-number-form.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\phone-number-form.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\otp\\\\phone-number-form.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/otp/phone-number-form.tsx\n");

/***/ }),

/***/ "./src/components/ui/forms/label.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/forms/label.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Label = ({ className, ...rest })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"block text-body-dark font-semibold text-sm leading-none mb-3\", className),\n        ...rest\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\forms\\\\label.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Label);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9mb3Jtcy9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRCO0FBRTVCLE1BQU1DLFFBQStELENBQUMsRUFDcEVDLFNBQVMsRUFDVCxHQUFHQyxNQUNKO0lBQ0MscUJBQ0UsOERBQUNDO1FBQ0NGLFdBQVdGLGlEQUFFQSxDQUNYLGdFQUNBRTtRQUVELEdBQUdDLElBQUk7Ozs7OztBQUdkO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AcGljay1iYXphci9zaG9wLy4vc3JjL2NvbXBvbmVudHMvdWkvZm9ybXMvbGFiZWwudHN4PzU0OTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNuIGZyb20gJ2NsYXNzbmFtZXMnO1xuXG5jb25zdCBMYWJlbDogUmVhY3QuRkM8UmVhY3QuTGFiZWxIVE1MQXR0cmlidXRlczxIVE1MTGFiZWxFbGVtZW50Pj4gPSAoe1xuICBjbGFzc05hbWUsXG4gIC4uLnJlc3Rcbn0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8bGFiZWxcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICdibG9jayB0ZXh0LWJvZHktZGFyayBmb250LXNlbWlib2xkIHRleHQtc20gbGVhZGluZy1ub25lIG1iLTMnLFxuICAgICAgICBjbGFzc05hbWVcbiAgICAgICl9XG4gICAgICB7Li4ucmVzdH1cbiAgICAvPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTGFiZWw7XG4iXSwibmFtZXMiOlsiY24iLCJMYWJlbCIsImNsYXNzTmFtZSIsInJlc3QiLCJsYWJlbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/ui/forms/label.tsx\n");

/***/ }),

/***/ "./src/components/ui/forms/phone-input.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/forms/phone-input.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport default from dynamic */ react_phone_input_2__WEBPACK_IMPORTED_MODULE_1___default.a)\n/* harmony export */ });\n/* harmony import */ var react_phone_input_2_lib_bootstrap_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-phone-input-2/lib/bootstrap.css */ \"./node_modules/react-phone-input-2/lib/bootstrap.css\");\n/* harmony import */ var react_phone_input_2_lib_bootstrap_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_phone_input_2_lib_bootstrap_css__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_phone_input_2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-phone-input-2 */ \"react-phone-input-2\");\n/* harmony import */ var react_phone_input_2__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_phone_input_2__WEBPACK_IMPORTED_MODULE_1__);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9mb3Jtcy9waG9uZS1pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBK0M7QUFDRCIsInNvdXJjZXMiOlsid2VicGFjazovL0BwaWNrLWJhemFyL3Nob3AvLi9zcmMvY29tcG9uZW50cy91aS9mb3Jtcy9waG9uZS1pbnB1dC50c3g/MTE2MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJ3JlYWN0LXBob25lLWlucHV0LTIvbGliL2Jvb3RzdHJhcC5jc3MnO1xuZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gJ3JlYWN0LXBob25lLWlucHV0LTInO1xuIl0sIm5hbWVzIjpbImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/ui/forms/phone-input.tsx\n");

/***/ })

};
;