import { CreateReviewDto } from './dto/create-review.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
import { GetReviewsDto, ReviewPaginator } from './dto/get-reviews.dto';
import { Review } from './entities/review.entity';
export declare class ReviewService {
    private reviewModel;
    constructor(reviewModel: typeof Review);
    findAllReviews({ limit, page, search, product_id, }: GetReviewsDto): Promise<ReviewPaginator>;
    findReview(id: number): Promise<Review | null>;
    create(createReviewDto: CreateReviewDto): Promise<Review>;
    update(id: number, updateReviewDto: UpdateReviewDto): Promise<Review | null>;
    delete(id: number): Promise<number>;
}
