"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_dashboard_widgets_table_widget-product-count-by-category_tsx-_361a0";
exports.ids = ["src_components_dashboard_widgets_table_widget-product-count-by-category_tsx-_361a0"];
exports.modules = {

/***/ "./src/components/dashboard/widgets/table/widget-product-count-by-category.tsx":
/*!*************************************************************************************!*\
  !*** ./src/components/dashboard/widgets/table/widget-product-count-by-category.tsx ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/table */ \"./src/components/ui/table.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_locals__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/locals */ \"./src/utils/locals.tsx\");\n/* harmony import */ var _components_icons_no_data_found__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons/no-data-found */ \"./src/components/icons/no-data-found.tsx\");\n\n\n\n\n\n\nconst ProductCountByCategory = ({ products, title, className })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const { alignLeft } = (0,_utils_locals__WEBPACK_IMPORTED_MODULE_4__.useIsRTL)();\n    let columns = [\n        {\n            title: t(\"table:table-item-category-id\"),\n            dataIndex: \"category_id\",\n            key: \"category_id\",\n            align: alignLeft,\n            width: 120,\n            render: (id)=>`#${t(\"table:table-item-id\")}: ${id}`\n        },\n        {\n            title: t(\"table:table-item-category-name\"),\n            className: \"cursor-pointer\",\n            dataIndex: \"category_name\",\n            key: \"category_name\",\n            align: alignLeft,\n            width: 220,\n            ellipsis: true\n        },\n        {\n            title: t(\"table:table-item-shop\"),\n            dataIndex: \"shop_name\",\n            key: \"shop\",\n            align: alignLeft,\n            ellipsis: true,\n            width: 200,\n            render: (shop_name)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"truncate whitespace-nowrap\",\n                    children: shop_name\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: t(\"table:table-item-Product-count\"),\n            className: \"cursor-pointer\",\n            dataIndex: \"product_count\",\n            key: \"product_count\",\n            width: 180,\n            align: \"center\",\n            render: (product_count)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: product_count\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, undefined);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(\"overflow-hidden rounded-lg bg-white p-7\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between pb-7\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"before:content-'' relative mt-1.5 bg-light text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:w-1 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-7 rtl:before:-right-7\",\n                        children: t(title)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.Table, {\n                    /* @ts-ignore */ columns: columns,\n                    emptyText: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center py-7\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_no_data_found__WEBPACK_IMPORTED_MODULE_5__.NoDataFound, {\n                                    className: \"w-52\"\n                                }, void 0, false, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-1 pt-6 text-base font-semibold text-heading\",\n                                    children: t(\"table:empty-table-data\")\n                                }, void 0, false, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[13px]\",\n                                    children: t(\"table:empty-table-sorry-text\")\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                    data: products,\n                    rowKey: \"category_id\",\n                    scroll: {\n                        x: 200\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductCountByCategory);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/dashboard/widgets/table/widget-product-count-by-category.tsx\n");

/***/ })

};
;