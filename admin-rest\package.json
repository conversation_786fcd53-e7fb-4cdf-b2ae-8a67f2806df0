{"name": "@onekart/admin-rest", "version": "11.10.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint"}, "dependencies": {"@floating-ui/react": "0.26.25", "@floating-ui/react-dom-interactions": "0.13.3", "@headlessui/react": "1.7.17", "@hookform/resolvers": "3.9.1", "@reach/portal": "0.16.2", "@react-google-maps/api": "2.20.3", "apexcharts": "3.44.0", "axios": "1.7.7", "body-scroll-lock": "4.0.0-beta.0", "camelcase-keys": "9.1.3", "classnames": "2.5.1", "cookie": "0.6.0", "dayjs": "1.11.13", "framer-motion": "10.16.4", "i18next": "23.16.4", "jotai": "2.10.1", "js-cookie": "3.0.5", "libphonenumber-js": "1.11.12", "lodash": "4.17.21", "next": "13.5.6", "next-i18next": "15.3.1", "next-pwa": "5.6.0", "next-seo": "6.6.0", "overlayscrollbars": "2.10.0", "overlayscrollbars-react": "0.5.6", "quill-color-picker-enhance": "1.1.5", "rc-pagination": "3.7.0", "rc-progress": "3.5.1", "rc-table": "7.22.2", "react": "18.3.1", "react-apexcharts": "1.5.0", "react-content-loader": "6.2.1", "react-countdown": "2.3.6", "react-datepicker": "4.21.0", "react-dom": "18.3.1", "react-dropzone": "14.2.10", "react-hook-form": "7.53.1", "react-i18next": "13.3.1", "react-laag": "2.0.5", "react-phone-input-2": "2.15.1", "react-query": "3.39.3", "react-quill": "2.0.0", "react-quill-emoji": "0.1.9", "react-scroll": "1.9.0", "react-select": "5.8.2", "react-toastify": "9.1.3", "react-use": "17.5.1", "sanitize-html": "2.13.1", "sharp": "0.32.6", "swiper": "11.1.14", "tailwind-merge": "2.5.4", "tiny-invariant": "1.3.3", "yup": "1.4.0"}, "devDependencies": {"@tailwindcss/forms": "0.5.9", "@tailwindcss/typography": "0.5.15", "@types/body-scroll-lock": "3.1.2", "@types/cookie": "0.5.4", "@types/js-cookie": "3.0.6", "@types/lodash": "4.17.13", "@types/node": "20.8.10", "@types/overlayscrollbars": "1.12.5", "@types/react": "18.3.12", "@types/react-datepicker": "4.19.1", "@types/react-dom": "18.3.1", "@types/react-scroll": "1.8.10", "@types/sanitize-html": "2.13.0", "autoprefixer": "10.4.20", "eslint": "8.53.0", "eslint-config-next": "14.0.1", "eslint-config-prettier": "9.1.0", "laravel-echo": "1.16.1", "postcss": "8.4.47", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "0.5.6", "pusher-js": "8.4.0-rc2", "tailwindcss": "3.4.14", "tailwindcss-rtl": "0.9.0", "typescript": "5.6.3"}}