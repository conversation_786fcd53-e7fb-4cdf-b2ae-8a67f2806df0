{"name": "student-service", "version": "1.0.0", "description": "Student Management Microservice", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "uuid": "^9.0.0", "joi": "^17.9.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "keywords": ["student", "microservice", "api", "nodejs", "express"], "author": "oneKart Team", "license": "MIT"}