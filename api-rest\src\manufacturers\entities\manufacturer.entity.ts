import {
  Column,
  Model,
  Table,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Type } from '../../types/entities/type.entity';

@Table({
  tableName: 'manufacturers',
  timestamps: true,
})
export class Manufacturer extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  cover_image?: any; // Attachment

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description?: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  image?: any; // Attachment

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  is_approved?: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  products_count?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    unique: true,
  })
  slug?: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  socials?: any; // ShopSocials

  @ForeignKey(() => Type)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  type_id?: number;

  @BelongsTo(() => Type)
  type: Type;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  website?: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'en',
  })
  language?: string;

  @Column({
    type: DataType.ARRAY(DataType.STRING),
    allowNull: true,
  })
  translated_languages?: string[];

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}
