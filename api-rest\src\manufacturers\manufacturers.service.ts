import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Manufacturer } from './entities/manufacturer.entity';
import Fuse from 'fuse.js';
import { GetTopManufacturersDto } from './dto/get-top-manufacturers.dto';
import {
  GetManufacturersDto,
  ManufacturerPaginator,
} from './dto/get-manufactures.dto';
import { paginate } from '../common/pagination/paginate';
import { CreateManufacturerDto } from './dto/create-manufacturer.dto';
import { UpdateManufacturerDto } from './dto/update-manufacturer.dto';

@Injectable()
export class ManufacturersService {
  constructor(
    @InjectModel(Manufacturer)
    private manufacturerModel: typeof Manufacturer,
  ) {}

  async create(
    createManufactureDto: CreateManufacturerDto,
  ): Promise<Manufacturer> {
    return this.manufacturerModel.create(createManufactureDto as any);
  }

  async getManufactures({
    limit,
    page,
    search,
  }: GetManufacturersDto): Promise<ManufacturerPaginator> {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    // TODO: Implement proper Sequelize query
    const { count, rows: data } = await this.manufacturerModel.findAndCountAll({
      limit,
      offset: (page - 1) * limit,
    });
    // TODO: Implement search functionality

    const url = `/manufacturers?search=${search}&limit=${limit}`;
    return {
      data,
      ...paginate(count, page, limit, data.length, url),
    };
  }

  async getTopManufactures({
    limit = 10,
  }: GetTopManufacturersDto): Promise<Manufacturer[]> {
    return this.manufacturerModel.findAll({ limit });
  }

  async getManufacturesBySlug(slug: string): Promise<Manufacturer | null> {
    return this.manufacturerModel.findOne({ where: { slug } });
  }

  async update(
    id: number,
    updateManufacturesDto: UpdateManufacturerDto,
  ): Promise<Manufacturer | null> {
    await this.manufacturerModel.update(updateManufacturesDto as any, {
      where: { id },
    });
    return this.manufacturerModel.findByPk(id);
  }

  remove(id: number) {
    return `This action removes a #${id} product`;
  }
}
