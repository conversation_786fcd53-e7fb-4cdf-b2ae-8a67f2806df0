{"c": ["pages/my-shops", "webpack", "src_components_dashboard_owner_tsx"], "r": ["pages/index", "src_components_dashboard_admin_tsx", "src_components_dashboard_owner_tsx", "src_components_layouts_admin_index_tsx", "src_components_layouts_owner_index_tsx", "src_components_dashboard_widgets_table_widget-product-count-by-category_tsx-_32bc0", "src_components_dashboard_widgets_box_widget-top-rate-product_tsx-_47910", "src_components_dashboard_widgets_table_widget-product-count-by-category_tsx-_32bc1", "src_components_dashboard_widgets_box_widget-top-rate-product_tsx-_47911"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CProjects%5CBB%5CProjects%5Ce-commerce%5Clogorithm-e-site%5Cadmin-rest%5Csrc%5Cpages%5Cindex.tsx&page=%2F!", "./src/components/layouts/app.tsx", "./src/pages/index.tsx", "./src/components/dashboard/admin.tsx", "./src/components/order/recent-orders.tsx", "./src/components/order/status-color.tsx", "./src/components/product/popular-product-list.tsx", "./src/components/product/product-stock.tsx", "./src/components/withdraw/withdraw-table.tsx", "./src/data/client/withdraw.ts", "./src/data/withdraw.ts", "./src/components/icons/avatar-icon.tsx", "./src/components/icons/checkmark-circle-fill.tsx", "./src/components/icons/email.tsx", "./src/components/icons/quote.tsx", "./src/components/layouts/owner/index.tsx", "./src/components/layouts/owner/menu.tsx", "./src/components/ui/blockquote.tsx", "./src/components/ui/truncate.tsx", "./src/components/user/user-details.tsx"]}