import {
  Column,
  Model,
  Table,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  HasOne,
} from 'sequelize-typescript';
import { User } from 'src/users/entities/user.entity';

@Table({
  tableName: 'shops',
  timestamps: true,
})
export class Shop extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  owner_id: number;

  @BelongsTo(() => User, 'owner_id')
  owner: User;

  @HasMany(() => User, 'managed_shop_id')
  staffs?: User[];

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
  })
  is_active: boolean;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  orders_count: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  products_count: number;

  @HasOne(() => Balance)
  balance?: Balance;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  slug: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description?: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  cover_image: any; // Attachment

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  logo?: any; // Attachment

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  address: any; // UserAddress

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  settings?: ShopSettings;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  distance?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  lat?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  lng?: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}

@Table({
  tableName: 'balances',
  timestamps: true,
})
export class Balance extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0,
  })
  admin_commission_rate: number;

  @ForeignKey(() => Shop)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  shop_id: number;

  @BelongsTo(() => Shop)
  shop: Shop;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  total_earnings: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  withdrawn_amount: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  current_balance: number;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  payment_info: PaymentInfo;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}

export class PaymentInfo {
  account: string;
  name: string;
  email: string;
  bank: string;
}

export class ShopSettings {
  socials: any[]; // ShopSocials[]
  contact: string;
  location: any; // Location
  website: string;
}
