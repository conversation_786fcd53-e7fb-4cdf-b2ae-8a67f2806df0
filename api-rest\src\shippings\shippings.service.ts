import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreateShippingDto } from './dto/create-shipping.dto';
import { GetShippingsDto } from './dto/get-shippings.dto';
import { UpdateShippingDto } from './dto/update-shipping.dto';
import { Shipping } from './entities/shipping.entity';

@Injectable()
export class ShippingsService {
  constructor(
    @InjectModel(Shipping)
    private shippingModel: typeof Shipping,
  ) {}

  async create(createShippingDto: CreateShippingDto): Promise<Shipping> {
    return this.shippingModel.create(createShippingDto as any);
  }

  async getShippings({}: GetShippingsDto): Promise<Shipping[]> {
    return this.shippingModel.findAll();
  }

  async findOne(id: number): Promise<Shipping | null> {
    return this.shippingModel.findByPk(id);
  }

  async update(
    id: number,
    updateShippingDto: UpdateShippingDto,
  ): Promise<Shipping | null> {
    await this.shippingModel.update(updateShippingDto as any, {
      where: { id },
    });
    return this.shippingModel.findByPk(id);
  }

  remove(id: number) {
    return `This action removes a #${id} shipping`;
  }
}
