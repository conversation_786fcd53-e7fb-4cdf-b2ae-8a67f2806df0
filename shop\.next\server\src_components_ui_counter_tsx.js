"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_ui_counter_tsx";
exports.ids = ["src_components_ui_counter_tsx"];
exports.modules = {

/***/ "./src/components/ui/counter.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/counter.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_icons_plus_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons/plus-icon */ \"./src/components/icons/plus-icon.tsx\");\n/* harmony import */ var _components_icons_minus_icon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/icons/minus-icon */ \"./src/components/icons/minus-icon.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tailwind_merge__WEBPACK_IMPORTED_MODULE_5__]);\ntailwind_merge__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\nconst variantClasses = {\n    helium: \"w-7 h-18 sm:w-20 sm:h-7 md:h-9 md:w-24 bg-accent flex-col-reverse sm:flex-row absolute sm:static bottom-3 ltr:right-3 rtl:left-3 sm:bottom-0 ltr:sm:right-0 ltr:sm:left-0 text-light rounded\",\n    neon: \"w-full h-7 md:h-9 bg-accent text-light rounded\",\n    argon: \"w-7 h-18 sm:w-20 sm:h-7 md:h-9 md:w-24 bg-accent flex-col-reverse sm:flex-row text-light rounded\",\n    oganesson: \"w-20 h-8 md:w-24 md:h-10 bg-accent text-light rounded-full shadow-500\",\n    single: \"order-5 sm:order-4 w-9 sm:w-24 h-24 sm:h-10 bg-accent text-light rounded-full flex-col-reverse sm:flex-row absolute sm:relative bottom-0 sm:bottom-auto ltr:right-0 rtl:left-0 ltr:sm:right-auto ltr:sm:left-auto\",\n    details: \"order-5 sm:order-4 w-full sm:w-24 h-10 bg-accent text-light rounded-full\",\n    pillVertical: \"flex-col-reverse items-center w-8 h-24 bg-gray-100 text-heading rounded-full\",\n    big: \"w-full h-14 rounded text-light bg-accent inline-flex justify-between\",\n    text: \"w-7 h-18 sm:w-20 sm:h-7 md:h-9 md:w-24 bg-accent flex-col-reverse sm:flex-row text-light rounded\",\n    bordered: \"h-14 rounded text-heading bg-transparent inline-flex justify-between shrink-0\",\n    florine: \"\"\n};\nconst Counter = ({ value, variant = \"helium\", onDecrement, onIncrement, className, disabled })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(\"common\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_5__.twMerge)(variant !== \"florine\" ? classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"flex overflow-hidden\", variantClasses[variant], className) : classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"flex w-24 items-center justify-between rounded-[0.25rem] border border-[#dbdbdb]\", className)),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onDecrement,\n                className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_5__.twMerge)(variant !== \"florine\" ? classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"cursor-pointer p-2 transition-colors duration-200 hover:bg-accent-hover focus:outline-0\", {\n                    \"px-3 py-3 sm:px-2\": variant === \"single\",\n                    \"px-5\": variant === \"big\",\n                    \"border border-gray-300 px-5 hover:border-accent hover:!bg-transparent ltr:rounded-l rtl:rounded-r\": variant === \"bordered\",\n                    \"hover:!bg-gray-100\": variant === \"pillVertical\"\n                }) : classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"p-2 text-base\", disabled ? \"text-[#c1c1c1]\" : \"text-accent\")),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"text-minus\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\counter.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined),\n                    variant !== \"florine\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_minus_icon__WEBPACK_IMPORTED_MODULE_3__.MinusIcon, {\n                        className: \"h-3 w-3 stroke-2.5\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\counter.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_minus_icon__WEBPACK_IMPORTED_MODULE_3__.MinusIconNew, {}, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\counter.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\counter.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"flex flex-1 items-center justify-center px-3 text-sm font-semibold\", variant === \"pillVertical\" && \"!px-0 text-heading\", variant === \"bordered\" && \"border-t border-b border-gray-300 !px-8 text-heading\"),\n                children: value\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\counter.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onIncrement,\n                disabled: disabled,\n                className: variant !== \"florine\" ? classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"cursor-pointer p-2 transition-colors duration-200 hover:bg-accent-hover focus:outline-0\", {\n                    \"px-3 py-3 sm:px-2\": variant === \"single\",\n                    \"px-5\": variant === \"big\",\n                    \"border border-gray-300 px-5 hover:border-accent hover:!bg-transparent hover:!text-accent ltr:rounded-r rtl:rounded-l\": variant === \"bordered\",\n                    \"hover:!bg-gray-100\": variant === \"pillVertical\"\n                }) : classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"p-2 text-base\", disabled ? \"text-[#c1c1c1]\" : \"text-accent\"),\n                title: disabled ? t(\"text-out-stock\") : \"\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"text-plus\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\counter.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    variant !== \"florine\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_plus_icon__WEBPACK_IMPORTED_MODULE_2__.PlusIcon, {\n                        className: \"md:w-4.5 h-3.5 w-3.5 stroke-2.5 md:h-4.5\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\counter.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_plus_icon__WEBPACK_IMPORTED_MODULE_2__.PlusIconNew, {}, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\counter.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\counter.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\counter.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Counter);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/counter.tsx\n");

/***/ })

};
;