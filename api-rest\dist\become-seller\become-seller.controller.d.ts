import { CreateBecomeSellerDto } from './dto/create-become-seller.dto';
import { BecomeSellerService } from './become-seller.service';
export declare class BecomeSellerController {
    private readonly becomeSellerService;
    constructor(becomeSellerService: BecomeSellerService);
    create(createBecomeSellerDto: CreateBecomeSellerDto): import("./entities/become-seller.entity").BecomeSeller;
    findAll(): import("./entities/become-seller.entity").BecomeSeller;
}
