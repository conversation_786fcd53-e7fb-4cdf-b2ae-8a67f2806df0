"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("@nestjs/config");
const user_entity_1 = require("../users/entities/user.entity");
const profile_entity_1 = require("../users/entities/profile.entity");
const category_entity_1 = require("../categories/entities/category.entity");
const product_entity_1 = require("../products/entities/product.entity");
const shop_entity_1 = require("../shops/entities/shop.entity");
const type_entity_1 = require("../types/entities/type.entity");
const tag_entity_1 = require("../tags/entities/tag.entity");
const manufacturer_entity_1 = require("../manufacturers/entities/manufacturer.entity");
const shipping_entity_1 = require("../shippings/entities/shipping.entity");
const tax_entity_1 = require("../taxes/entities/tax.entity");
const coupon_entity_1 = require("../coupons/entities/coupon.entity");
const review_entity_1 = require("../reviews/entities/review.entity");
const address_entity_1 = require("../addresses/entities/address.entity");
const analytics_entity_1 = require("../analytics/entities/analytics.entity");
const ai_entity_1 = require("../ai/entities/ai.entity");
exports.default = (0, config_1.registerAs)('database', () => ({
    dialect: process.env.DB_DIALECT || 'sqlite',
    storage: process.env.DB_STORAGE || './database.sqlite',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'ecommerce_db',
    models: [
        user_entity_1.User,
        user_entity_1.Permission,
        profile_entity_1.Profile,
        category_entity_1.Category,
        category_entity_1.ProductCategory,
        product_entity_1.Product,
        product_entity_1.ProductTag,
        shop_entity_1.Shop,
        shop_entity_1.Balance,
        type_entity_1.Type,
        tag_entity_1.Tag,
        manufacturer_entity_1.Manufacturer,
        shipping_entity_1.Shipping,
        tax_entity_1.Tax,
        coupon_entity_1.Coupon,
        review_entity_1.Review,
        address_entity_1.Address,
        analytics_entity_1.Analytics,
        ai_entity_1.AiEntity,
    ],
    autoLoadModels: true,
    synchronize: process.env.NODE_ENV !== 'production',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
}));
//# sourceMappingURL=database.config.js.map