"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("@nestjs/config");
const user_entity_1 = require("../users/entities/user.entity");
const profile_entity_1 = require("../users/entities/profile.entity");
const category_entity_1 = require("../categories/entities/category.entity");
const product_entity_1 = require("../products/entities/product.entity");
const shop_entity_1 = require("../shops/entities/shop.entity");
const type_entity_1 = require("../types/entities/type.entity");
const tag_entity_1 = require("../tags/entities/tag.entity");
const manufacturer_entity_1 = require("../manufacturers/entities/manufacturer.entity");
const shipping_entity_1 = require("../shippings/entities/shipping.entity");
const tax_entity_1 = require("../taxes/entities/tax.entity");
const coupon_entity_1 = require("../coupons/entities/coupon.entity");
const review_entity_1 = require("../reviews/entities/review.entity");
const address_entity_1 = require("../addresses/entities/address.entity");
const analytics_entity_1 = require("../analytics/entities/analytics.entity");
const total_year_sale_by_month_entity_1 = require("../analytics/entities/total-year-sale-by-month.entity");
const category_wise_product_entity_1 = require("../analytics/entities/category-wise-product.entity");
const top_rate_product_entity_1 = require("../analytics/entities/top-rate-product.entity");
const ai_entity_1 = require("../ai/entities/ai.entity");
const attribute_entity_1 = require("../attributes/entities/attribute.entity");
const attribute_value_entity_1 = require("../attributes/entities/attribute-value.entity");
const become_seller_entity_1 = require("../become-seller/entities/become-seller.entity");
const wishlist_entity_1 = require("../wishlists/entities/wishlist.entity");
const withdraw_entity_1 = require("../withdraws/entities/withdraw.entity");
const flash_sale_entity_1 = require("../flash-sale/entities/flash-sale.entity");
exports.default = (0, config_1.registerAs)('database', () => ({
    dialect: 'postgres',
    host: 'ep-bitter-flower-a1q5lyqy-pooler.ap-southeast-1.aws.neon.tech',
    port: 5432,
    username: 'ecommerce_owner',
    password: 'npg_aI0Dn8AMfbWj',
    database: 'ecommerce',
    dialectOptions: {
        ssl: {
            require: true,
            rejectUnauthorized: false,
        },
    },
    models: [
        user_entity_1.User,
        user_entity_1.Permission,
        profile_entity_1.Profile,
        category_entity_1.Category,
        category_entity_1.ProductCategory,
        product_entity_1.Product,
        product_entity_1.ProductTag,
        shop_entity_1.Shop,
        shop_entity_1.Balance,
        type_entity_1.Type,
        tag_entity_1.Tag,
        manufacturer_entity_1.Manufacturer,
        shipping_entity_1.Shipping,
        tax_entity_1.Tax,
        coupon_entity_1.Coupon,
        review_entity_1.Review,
        address_entity_1.Address,
        analytics_entity_1.Analytics,
        total_year_sale_by_month_entity_1.TotalYearSaleByMonth,
        category_wise_product_entity_1.CategoryWiseProduct,
        top_rate_product_entity_1.TopRateProduct,
        ai_entity_1.Ai,
        attribute_entity_1.Attribute,
        attribute_value_entity_1.AttributeValue,
        become_seller_entity_1.BecomeSeller,
        wishlist_entity_1.Wishlist,
        withdraw_entity_1.Withdraw,
        flash_sale_entity_1.FlashSale,
    ],
    autoLoadModels: true,
    synchronize: false,
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
}));
//# sourceMappingURL=database.config.js.map