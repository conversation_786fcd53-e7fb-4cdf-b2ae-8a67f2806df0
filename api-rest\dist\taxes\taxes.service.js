"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaxesService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const tax_entity_1 = require("./entities/tax.entity");
let TaxesService = class TaxesService {
    constructor(taxModel) {
        this.taxModel = taxModel;
    }
    async create(createTaxDto) {
        return this.taxModel.create(createTaxDto);
    }
    async findAll() {
        return this.taxModel.findAll();
    }
    async findOne(id) {
        return this.taxModel.findByPk(id);
    }
    async update(id, updateTaxDto) {
        await this.taxModel.update(updateTaxDto, { where: { id } });
        return this.taxModel.findByPk(id);
    }
    remove(id) {
        return `This action removes a #${id} tax`;
    }
};
TaxesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(tax_entity_1.Tax)),
    __metadata("design:paramtypes", [Object])
], TaxesService);
exports.TaxesService = TaxesService;
//# sourceMappingURL=taxes.service.js.map