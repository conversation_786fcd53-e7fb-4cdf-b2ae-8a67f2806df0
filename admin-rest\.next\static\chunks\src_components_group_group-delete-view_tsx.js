"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_group_group-delete-view_tsx"],{

/***/ "./src/components/group/group-delete-view.tsx":
/*!****************************************************!*\
  !*** ./src/components/group/group-delete-view.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_type__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/type */ \"./src/data/type.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst TypeDeleteView = ()=>{\n    _s();\n    const { mutate: deleteType, isLoading: loading } = (0,_data_type__WEBPACK_IMPORTED_MODULE_3__.useDeleteTypeMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    async function handleDelete() {\n        deleteType({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\group\\\\group-delete-view.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TypeDeleteView, \"Uf4G9lE0anmmgYHhqxdtXgpfxjQ=\", false, function() {\n    return [\n        _data_type__WEBPACK_IMPORTED_MODULE_3__.useDeleteTypeMutation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction\n    ];\n});\n_c = TypeDeleteView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TypeDeleteView);\nvar _c;\n$RefreshReg$(_c, \"TypeDeleteView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/group/group-delete-view.tsx\n"));

/***/ }),

/***/ "./src/data/client/type.ts":
/*!*********************************!*\
  !*** ./src/data/client/type.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   typeClient: function() { return /* binding */ typeClient; }\n/* harmony export */ });\n/* harmony import */ var _data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/client/curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\n\n\n\nconst typeClient = {\n    ...(0,_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__.crudFactory)(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.TYPES),\n    all: (param)=>{\n        let { name, ...params } = param;\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.TYPES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZGF0YS9jbGllbnQvdHlwZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXlEO0FBRUc7QUFDTDtBQUVoRCxNQUFNRyxhQUFhO0lBQ3hCLEdBQUdILHNFQUFXQSxDQUFzQ0MscUVBQWFBLENBQUNHLEtBQUssQ0FBQztJQUN4RUMsS0FBSztZQUFDLEVBQUVDLElBQUksRUFBRSxHQUFHQyxRQUFtQztRQUNsRCxPQUFPTCxnRUFBVUEsQ0FBQ00sR0FBRyxDQUFTUCxxRUFBYUEsQ0FBQ0csS0FBSyxFQUFFO1lBQ2pESyxZQUFZO1lBQ1osR0FBR0YsTUFBTTtZQUNURyxRQUFRUixnRUFBVUEsQ0FBQ1Msa0JBQWtCLENBQUM7Z0JBQUVMO1lBQUs7UUFDL0M7SUFDRjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2RhdGEvY2xpZW50L3R5cGUudHM/OTk3NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcnVkRmFjdG9yeSB9IGZyb20gJ0AvZGF0YS9jbGllbnQvY3VyZC1mYWN0b3J5JztcclxuaW1wb3J0IHsgQ3JlYXRlVHlwZUlucHV0LCBRdWVyeU9wdGlvbnMsIFR5cGUsIFR5cGVRdWVyeU9wdGlvbnMgfSBmcm9tICdAL3R5cGVzJztcclxuaW1wb3J0IHsgQVBJX0VORFBPSU5UUyB9IGZyb20gJ0AvZGF0YS9jbGllbnQvYXBpLWVuZHBvaW50cyc7XHJcbmltcG9ydCB7IEh0dHBDbGllbnQgfSBmcm9tICdAL2RhdGEvY2xpZW50L2h0dHAtY2xpZW50JztcclxuXHJcbmV4cG9ydCBjb25zdCB0eXBlQ2xpZW50ID0ge1xyXG4gIC4uLmNydWRGYWN0b3J5PFR5cGUsIFF1ZXJ5T3B0aW9ucywgQ3JlYXRlVHlwZUlucHV0PihBUElfRU5EUE9JTlRTLlRZUEVTKSxcclxuICBhbGw6ICh7IG5hbWUsIC4uLnBhcmFtcyB9OiBQYXJ0aWFsPFR5cGVRdWVyeU9wdGlvbnM+KSA9PiB7XHJcbiAgICByZXR1cm4gSHR0cENsaWVudC5nZXQ8VHlwZVtdPihBUElfRU5EUE9JTlRTLlRZUEVTLCB7XHJcbiAgICAgIHNlYXJjaEpvaW46ICdhbmQnLFxyXG4gICAgICAuLi5wYXJhbXMsXHJcbiAgICAgIHNlYXJjaDogSHR0cENsaWVudC5mb3JtYXRTZWFyY2hQYXJhbXMoeyBuYW1lIH0pLFxyXG4gICAgfSk7XHJcbiAgfSxcclxufTtcclxuIl0sIm5hbWVzIjpbImNydWRGYWN0b3J5IiwiQVBJX0VORFBPSU5UUyIsIkh0dHBDbGllbnQiLCJ0eXBlQ2xpZW50IiwiVFlQRVMiLCJhbGwiLCJuYW1lIiwicGFyYW1zIiwiZ2V0Iiwic2VhcmNoSm9pbiIsInNlYXJjaCIsImZvcm1hdFNlYXJjaFBhcmFtcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/data/client/type.ts\n"));

/***/ }),

/***/ "./src/data/type.ts":
/*!**************************!*\
  !*** ./src/data/type.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateTypeMutation: function() { return /* binding */ useCreateTypeMutation; },\n/* harmony export */   useDeleteTypeMutation: function() { return /* binding */ useDeleteTypeMutation; },\n/* harmony export */   useTypeQuery: function() { return /* binding */ useTypeQuery; },\n/* harmony export */   useTypesQuery: function() { return /* binding */ useTypesQuery; },\n/* harmony export */   useUpdateTypeMutation: function() { return /* binding */ useUpdateTypeMutation; }\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _data_client_type__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/client/type */ \"./src/data/client/type.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\n\n\n\n\n\n\n\n\nconst useCreateTypeMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_type__WEBPACK_IMPORTED_MODULE_6__.typeClient.create, {\n        onSuccess: ()=>{\n            next_router__WEBPACK_IMPORTED_MODULE_0___default().push(_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.type.list, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.TYPES);\n        }\n    });\n};\nconst useDeleteTypeMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_type__WEBPACK_IMPORTED_MODULE_6__.typeClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.TYPES);\n        }\n    });\n};\nconst useUpdateTypeMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_type__WEBPACK_IMPORTED_MODULE_6__.typeClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? \"/\".concat(router.query.shop).concat(_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.type.list) : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.type.list;\n            await router.push(\"\".concat(generateRedirectUrl, \"/\").concat(data === null || data === void 0 ? void 0 : data.slug, \"/edit\"), undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // onSuccess: () => {\n        //   toast.success(t('common:successfully-updated'));\n        // },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.TYPES);\n        }\n    });\n};\nconst useTypeQuery = (param)=>{\n    let { slug, language } = param;\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.TYPES,\n        {\n            slug,\n            language\n        }\n    ], ()=>_data_client_type__WEBPACK_IMPORTED_MODULE_6__.typeClient.get({\n            slug,\n            language\n        }));\n};\nconst useTypesQuery = (options)=>{\n    const { data, isLoading, error } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.TYPES,\n        options\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _data_client_type__WEBPACK_IMPORTED_MODULE_6__.typeClient.all(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        keepPreviousData: true\n    });\n    return {\n        types: data !== null && data !== void 0 ? data : [],\n        loading: isLoading,\n        error\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/type.ts\n"));

/***/ })

}]);