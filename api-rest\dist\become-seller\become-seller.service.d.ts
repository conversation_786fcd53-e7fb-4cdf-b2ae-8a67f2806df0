import { BecomeSeller } from './entities/become-seller.entity';
import { CreateBecomeSellerDto } from './dto/create-become-seller.dto';
import { UpdateBecomeSellerDto } from './dto/update-become-seller.dto';
export declare class BecomeSellerService {
    private becomeSeller;
    create(createBecomeSellerDto: CreateBecomeSellerDto): BecomeSeller;
    findAll(): BecomeSeller;
    findOne(id: number): string;
    update(id: number, updateBecomeSellerDto: UpdateBecomeSellerDto): BecomeSeller;
    remove(id: number): string;
}
