import { BecomeSeller } from './entities/become-seller.entity';
import { CreateBecomeSellerDto } from './dto/create-become-seller.dto';
import { UpdateBecomeSellerDto } from './dto/update-become-seller.dto';
export declare class BecomeSellerService {
    private becomeSellerModel;
    constructor(becomeSellerModel: typeof BecomeSeller);
    create(createBecomeSellerDto: CreateBecomeSellerDto): Promise<BecomeSeller>;
    findAll(): Promise<BecomeSeller[]>;
    findOne(id: number): Promise<BecomeSeller | null>;
    update(id: number, updateBecomeSellerDto: UpdateBecomeSellerDto): Promise<BecomeSeller | null>;
    remove(id: number): string;
}
