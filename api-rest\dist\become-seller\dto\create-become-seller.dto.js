"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateBecomeSellerDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const become_seller_entity_1 = require("../entities/become-seller.entity");
class CreateBecomeSellerDto extends (0, swagger_1.OmitType)(become_seller_entity_1.BecomeSeller, [
    'id',
    'createdAt',
    'updatedAt',
]) {
    static _OPENAPI_METADATA_FACTORY() {
        return {};
    }
}
exports.CreateBecomeSellerDto = CreateBecomeSellerDto;
//# sourceMappingURL=create-become-seller.dto.js.map