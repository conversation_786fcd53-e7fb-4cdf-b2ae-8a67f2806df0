const http = require('http');

// Test low stock product creation
const productData = JSON.stringify({
  name: 'Low Stock Product',
  description: 'A product with low stock for inventory testing',
  slug: 'low-stock-product',
  type_id: 1,
  shop_id: 3, // Ridwan's shop ID
  product_type: 'simple',
  status: 'publish',
  visibility: 'public',
  price: 49.99,
  sale_price: 39.99,
  sku: 'LOW-STOCK-001',
  quantity: 5, // Low stock quantity (≤ 9)
  in_stock: true,
  is_taxable: true,
  shipping_class_id: null,
  categories: [1],
  tags: [],
  image: {
    id: 'low-stock-image-id',
    original: 'https://example.com/low-stock-image.jpg',
    thumbnail: 'https://example.com/low-stock-image-thumb.jpg'
  },
  gallery: [],
  variations: [],
  variation_options: []
});

const options = {
  hostname: 'localhost',
  port: 9000,
  path: '/api/products',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': productData.length
  }
};

console.log('Creating low stock product for inventory testing...');
console.log('Request data:', productData);

const req = http.request(options, (res) => {
  console.log(`\nStatus: ${res.statusCode}`);
  console.log(`Headers: ${JSON.stringify(res.headers, null, 2)}`);
  
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });
  
  res.on('end', () => {
    console.log('\nResponse body:', body);
    
    if (res.statusCode >= 400) {
      console.log('\n❌ Low stock product creation failed!');
      try {
        const errorData = JSON.parse(body);
        console.log('Error details:', JSON.stringify(errorData, null, 2));
      } catch (e) {
        console.log('Raw error response:', body);
      }
    } else {
      console.log('\n✅ Low stock product created successfully!');
      console.log('This product should now appear in the inventory page.');
    }
  });
});

req.on('error', (e) => {
  console.error(`❌ Request error: ${e.message}`);
});

req.write(productData);
req.end();
