import { registerAs } from '@nestjs/config';
import { User, Permission } from '../users/entities/user.entity';
import { Profile } from '../users/entities/profile.entity';
import {
  Category,
  ProductCategory,
} from '../categories/entities/category.entity';
import { Product, ProductTag } from '../products/entities/product.entity';
import { Shop, Balance } from '../shops/entities/shop.entity';
import { Type } from '../types/entities/type.entity';
import { Tag } from '../tags/entities/tag.entity';
import { Manufacturer } from '../manufacturers/entities/manufacturer.entity';
import { Shipping } from '../shippings/entities/shipping.entity';
import { Tax } from '../taxes/entities/tax.entity';
import { Coupon } from '../coupons/entities/coupon.entity';
import { Review } from '../reviews/entities/review.entity';
import { Address } from '../addresses/entities/address.entity';
import { Analytics } from '../analytics/entities/analytics.entity';
import { Ai } from '../ai/entities/ai.entity';
import { Attribute } from '../attributes/entities/attribute.entity';
import { BecomeSeller } from '../become-seller/entities/become-seller.entity';
import { Wishlist } from '../wishlists/entities/wishlist.entity';
import { Withdraw } from '../withdraws/entities/withdraw.entity';

export default registerAs('database', () => ({
  dialect: 'postgres',
  host: 'ep-bitter-flower-a1q5lyqy-pooler.ap-southeast-1.aws.neon.tech',
  port: 5432,
  username: 'ecommerce_owner',
  password: 'npg_aI0Dn8AMfbWj',
  database: 'ecommerce',
  dialectOptions: {
    ssl: {
      require: true,
      rejectUnauthorized: false,
    },
  },
  models: [
    User,
    Permission,
    Profile,
    Category,
    ProductCategory,
    Product,
    ProductTag,
    Shop,
    Balance,
    Type,
    Tag,
    Manufacturer,
    Shipping,
    Tax,
    Coupon,
    Review,
    Address,
    Analytics,
    Ai,
    Attribute,
    BecomeSeller,
    Wishlist,
    Withdraw,
  ],
  autoLoadModels: true,
  synchronize: process.env.NODE_ENV !== 'production',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
}));
