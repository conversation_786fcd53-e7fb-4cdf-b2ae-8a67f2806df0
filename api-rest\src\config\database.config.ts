import { registerAs } from '@nestjs/config';
import { User, Permission } from '../users/entities/user.entity';
import { Profile } from '../users/entities/profile.entity';
import {
  Category,
  ProductCategory,
} from '../categories/entities/category.entity';
import { Product, ProductTag } from '../products/entities/product.entity';
import { Shop, Balance } from '../shops/entities/shop.entity';
import { Type } from '../types/entities/type.entity';
import { Tag } from '../tags/entities/tag.entity';
import { Manufacturer } from '../manufacturers/entities/manufacturer.entity';
import { Shipping } from '../shippings/entities/shipping.entity';
import { Tax } from '../taxes/entities/tax.entity';
import { Coupon } from '../coupons/entities/coupon.entity';
import { Review } from '../reviews/entities/review.entity';
import { Address } from '../addresses/entities/address.entity';
import { Analytics } from '../analytics/entities/analytics.entity';
import { Ai } from '../ai/entities/ai.entity';

export default registerAs('database', () => ({
  dialect: process.env.DB_DIALECT || 'sqlite',
  storage: process.env.DB_STORAGE || './database.sqlite',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT, 10) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'ecommerce_db',
  models: [
    User,
    Permission,
    Profile,
    Category,
    ProductCategory,
    Product,
    ProductTag,
    Shop,
    Balance,
    Type,
    Tag,
    Manufacturer,
    Shipping,
    Tax,
    Coupon,
    Review,
    Address,
    Analytics,
    Ai,
  ],
  autoLoadModels: true,
  synchronize: process.env.NODE_ENV !== 'production',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
}));
