import { CreateTagDto } from './dto/create-tag.dto';
import { GetTagsDto } from './dto/get-tags.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { Tag } from './entities/tag.entity';
export declare class TagsService {
    private tagModel;
    constructor(tagModel: typeof Tag);
    create(createTagDto: CreateTagDto): Promise<Tag>;
    findAll({ page, limit, search }: GetTagsDto): Promise<{
        data: Tag[];
        count: number;
        current_page: number;
        firstItem: number;
        lastItem: number;
        last_page: number;
        per_page: number;
        total: number;
        first_page_url: string;
        last_page_url: string;
        next_page_url: string;
        prev_page_url: string;
    }>;
    findOne(param: string, language: string): Promise<Tag>;
    update(id: number, updateTagDto: UpdateTagDto): Promise<[number, Tag[]]>;
    remove(id: number): Promise<number>;
}
