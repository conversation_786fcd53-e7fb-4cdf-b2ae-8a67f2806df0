"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_tax_tax-delete-view_tsx";
exports.ids = ["src_components_tax_tax-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/tax/tax-delete-view.tsx":
/*!************************************************!*\
  !*** ./src/components/tax/tax-delete-view.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_tax__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/tax */ \"./src/data/tax.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_tax__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_tax__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst TaxDeleteView = ()=>{\n    const { mutate: deleteTax, isLoading: loading } = (0,_data_tax__WEBPACK_IMPORTED_MODULE_3__.useDeleteTaxMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    async function handleDelete() {\n        deleteTax({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\tax\\\\tax-delete-view.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaxDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy90YXgvdGF4LWRlbGV0ZS12aWV3LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXFFO0FBSXhCO0FBQ0s7QUFFbEQsTUFBTUksZ0JBQWdCO0lBQ3BCLE1BQU0sRUFBRUMsUUFBUUMsU0FBUyxFQUFFQyxXQUFXQyxPQUFPLEVBQUUsR0FBR0wsK0RBQW9CQTtJQUN0RSxNQUFNLEVBQUVNLElBQUksRUFBRSxHQUFHUCxpRkFBYUE7SUFDOUIsTUFBTSxFQUFFUSxVQUFVLEVBQUUsR0FBR1Qsa0ZBQWNBO0lBRXJDLGVBQWVVO1FBQ2JMLFVBQVU7WUFDUk0sSUFBSUg7UUFDTjtRQUNBQztJQUNGO0lBRUEscUJBQ0UsOERBQUNWLDRFQUFnQkE7UUFDZmEsVUFBVUg7UUFDVkksVUFBVUg7UUFDVkksa0JBQWtCUDs7Ozs7O0FBR3hCO0FBRUEsaUVBQWVKLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab25la2FydC9hZG1pbi1yZXN0Ly4vc3JjL2NvbXBvbmVudHMvdGF4L3RheC1kZWxldGUtdmlldy50c3g/YmY0NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ29uZmlybWF0aW9uQ2FyZCBmcm9tICdAL2NvbXBvbmVudHMvY29tbW9uL2NvbmZpcm1hdGlvbi1jYXJkJztcclxuaW1wb3J0IHtcclxuICB1c2VNb2RhbEFjdGlvbixcclxuICB1c2VNb2RhbFN0YXRlLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9tb2RhbC9tb2RhbC5jb250ZXh0JztcclxuaW1wb3J0IHsgdXNlRGVsZXRlVGF4TXV0YXRpb24gfSBmcm9tICdAL2RhdGEvdGF4JztcclxuXHJcbmNvbnN0IFRheERlbGV0ZVZpZXcgPSAoKSA9PiB7XHJcbiAgY29uc3QgeyBtdXRhdGU6IGRlbGV0ZVRheCwgaXNMb2FkaW5nOiBsb2FkaW5nIH0gPSB1c2VEZWxldGVUYXhNdXRhdGlvbigpO1xyXG4gIGNvbnN0IHsgZGF0YSB9ID0gdXNlTW9kYWxTdGF0ZSgpO1xyXG4gIGNvbnN0IHsgY2xvc2VNb2RhbCB9ID0gdXNlTW9kYWxBY3Rpb24oKTtcclxuXHJcbiAgYXN5bmMgZnVuY3Rpb24gaGFuZGxlRGVsZXRlKCkge1xyXG4gICAgZGVsZXRlVGF4KHtcclxuICAgICAgaWQ6IGRhdGEsXHJcbiAgICB9KTtcclxuICAgIGNsb3NlTW9kYWwoKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8Q29uZmlybWF0aW9uQ2FyZFxyXG4gICAgICBvbkNhbmNlbD17Y2xvc2VNb2RhbH1cclxuICAgICAgb25EZWxldGU9e2hhbmRsZURlbGV0ZX1cclxuICAgICAgZGVsZXRlQnRuTG9hZGluZz17bG9hZGluZ31cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IFRheERlbGV0ZVZpZXc7XHJcbiJdLCJuYW1lcyI6WyJDb25maXJtYXRpb25DYXJkIiwidXNlTW9kYWxBY3Rpb24iLCJ1c2VNb2RhbFN0YXRlIiwidXNlRGVsZXRlVGF4TXV0YXRpb24iLCJUYXhEZWxldGVWaWV3IiwibXV0YXRlIiwiZGVsZXRlVGF4IiwiaXNMb2FkaW5nIiwibG9hZGluZyIsImRhdGEiLCJjbG9zZU1vZGFsIiwiaGFuZGxlRGVsZXRlIiwiaWQiLCJvbkNhbmNlbCIsIm9uRGVsZXRlIiwiZGVsZXRlQnRuTG9hZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/tax/tax-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/tax.ts":
/*!********************************!*\
  !*** ./src/data/client/tax.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   taxClient: () => (/* binding */ taxClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst taxClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES),\n    get ({ id }) {\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES}/${id}`);\n    },\n    paginated: ({ name, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    },\n    all: ({ name, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/tax.ts\n");

/***/ }),

/***/ "./src/data/tax.ts":
/*!*************************!*\
  !*** ./src/data/tax.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateTaxClassMutation: () => (/* binding */ useCreateTaxClassMutation),\n/* harmony export */   useDeleteTaxMutation: () => (/* binding */ useDeleteTaxMutation),\n/* harmony export */   useTaxQuery: () => (/* binding */ useTaxQuery),\n/* harmony export */   useTaxesQuery: () => (/* binding */ useTaxesQuery),\n/* harmony export */   useUpdateTaxClassMutation: () => (/* binding */ useUpdateTaxClassMutation)\n/* harmony export */ });\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _client_tax__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/tax */ \"./src/data/client/tax.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_4__, _client_tax__WEBPACK_IMPORTED_MODULE_6__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_4__, _client_tax__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst useCreateTaxClassMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.create, {\n        onSuccess: ()=>{\n            router.push(_config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.tax.list);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES);\n        }\n    });\n};\nconst useDeleteTaxMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES);\n        }\n    });\n};\nconst useUpdateTaxClassMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.update, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES);\n        }\n    });\n};\nconst useTaxQuery = (id)=>{\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES,\n        id\n    ], ()=>_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.get({\n            id\n        }));\n};\nconst useTaxesQuery = (options = {})=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES,\n        options\n    ], ({ queryKey, pageParam })=>_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.all(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        taxes: data ?? [],\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/tax.ts\n");

/***/ })

};
;