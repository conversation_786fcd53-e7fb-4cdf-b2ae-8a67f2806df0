"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_tax_tax-delete-view_tsx";
exports.ids = ["src_components_tax_tax-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/tax/tax-delete-view.tsx":
/*!************************************************!*\
  !*** ./src/components/tax/tax-delete-view.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_tax__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/tax */ \"./src/data/tax.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_tax__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_tax__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst TaxDeleteView = ()=>{\n    const { mutate: deleteTax, isLoading: loading } = (0,_data_tax__WEBPACK_IMPORTED_MODULE_3__.useDeleteTaxMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    async function handleDelete() {\n        deleteTax({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\tax\\\\tax-delete-view.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaxDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy90YXgvdGF4LWRlbGV0ZS12aWV3LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXFFO0FBSXhCO0FBQ0s7QUFFbEQsTUFBTUksZ0JBQWdCO0lBQ3BCLE1BQU0sRUFBRUMsUUFBUUMsU0FBUyxFQUFFQyxXQUFXQyxPQUFPLEVBQUUsR0FBR0wsK0RBQW9CQTtJQUN0RSxNQUFNLEVBQUVNLElBQUksRUFBRSxHQUFHUCxpRkFBYUE7SUFDOUIsTUFBTSxFQUFFUSxVQUFVLEVBQUUsR0FBR1Qsa0ZBQWNBO0lBRXJDLGVBQWVVO1FBQ2JMLFVBQVU7WUFDUk0sSUFBSUg7UUFDTjtRQUNBQztJQUNGO0lBRUEscUJBQ0UsOERBQUNWLDRFQUFnQkE7UUFDZmEsVUFBVUg7UUFDVkksVUFBVUg7UUFDVkksa0JBQWtCUDs7Ozs7O0FBR3hCO0FBRUEsaUVBQWVKLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbWFydmVsL2FkbWluLXJlc3QvLi9zcmMvY29tcG9uZW50cy90YXgvdGF4LWRlbGV0ZS12aWV3LnRzeD9iZjQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBDb25maXJtYXRpb25DYXJkIGZyb20gJ0AvY29tcG9uZW50cy9jb21tb24vY29uZmlybWF0aW9uLWNhcmQnO1xyXG5pbXBvcnQge1xyXG4gIHVzZU1vZGFsQWN0aW9uLFxyXG4gIHVzZU1vZGFsU3RhdGUsXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL21vZGFsL21vZGFsLmNvbnRleHQnO1xyXG5pbXBvcnQgeyB1c2VEZWxldGVUYXhNdXRhdGlvbiB9IGZyb20gJ0AvZGF0YS90YXgnO1xyXG5cclxuY29uc3QgVGF4RGVsZXRlVmlldyA9ICgpID0+IHtcclxuICBjb25zdCB7IG11dGF0ZTogZGVsZXRlVGF4LCBpc0xvYWRpbmc6IGxvYWRpbmcgfSA9IHVzZURlbGV0ZVRheE11dGF0aW9uKCk7XHJcbiAgY29uc3QgeyBkYXRhIH0gPSB1c2VNb2RhbFN0YXRlKCk7XHJcbiAgY29uc3QgeyBjbG9zZU1vZGFsIH0gPSB1c2VNb2RhbEFjdGlvbigpO1xyXG5cclxuICBhc3luYyBmdW5jdGlvbiBoYW5kbGVEZWxldGUoKSB7XHJcbiAgICBkZWxldGVUYXgoe1xyXG4gICAgICBpZDogZGF0YSxcclxuICAgIH0pO1xyXG4gICAgY2xvc2VNb2RhbCgpO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxDb25maXJtYXRpb25DYXJkXHJcbiAgICAgIG9uQ2FuY2VsPXtjbG9zZU1vZGFsfVxyXG4gICAgICBvbkRlbGV0ZT17aGFuZGxlRGVsZXRlfVxyXG4gICAgICBkZWxldGVCdG5Mb2FkaW5nPXtsb2FkaW5nfVxyXG4gICAgLz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgVGF4RGVsZXRlVmlldztcclxuIl0sIm5hbWVzIjpbIkNvbmZpcm1hdGlvbkNhcmQiLCJ1c2VNb2RhbEFjdGlvbiIsInVzZU1vZGFsU3RhdGUiLCJ1c2VEZWxldGVUYXhNdXRhdGlvbiIsIlRheERlbGV0ZVZpZXciLCJtdXRhdGUiLCJkZWxldGVUYXgiLCJpc0xvYWRpbmciLCJsb2FkaW5nIiwiZGF0YSIsImNsb3NlTW9kYWwiLCJoYW5kbGVEZWxldGUiLCJpZCIsIm9uQ2FuY2VsIiwib25EZWxldGUiLCJkZWxldGVCdG5Mb2FkaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/tax/tax-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/tax.ts":
/*!********************************!*\
  !*** ./src/data/client/tax.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   taxClient: () => (/* binding */ taxClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst taxClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES),\n    get ({ id }) {\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES}/${id}`);\n    },\n    paginated: ({ name, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    },\n    all: ({ name, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZGF0YS9jbGllbnQvdGF4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFPZ0Q7QUFDSDtBQUNVO0FBRWhELE1BQU1HLFlBQVk7SUFDdkIsR0FBR0YsMERBQVdBLENBQThCRCx5REFBYUEsQ0FBQ0ksS0FBSyxDQUFDO0lBQ2hFQyxLQUFJLEVBQUVDLEVBQUUsRUFBa0I7UUFDeEIsT0FBT0osZ0VBQVVBLENBQUNHLEdBQUcsQ0FBTSxDQUFDLEVBQUVMLHlEQUFhQSxDQUFDSSxLQUFLLENBQUMsQ0FBQyxFQUFFRSxHQUFHLENBQUM7SUFDM0Q7SUFDQUMsV0FBVyxDQUFDLEVBQUVDLElBQUksRUFBRSxHQUFHQyxRQUFrQztRQUN2RCxPQUFPUCxnRUFBVUEsQ0FBQ0csR0FBRyxDQUFlTCx5REFBYUEsQ0FBQ0ksS0FBSyxFQUFFO1lBQ3ZETSxZQUFZO1lBQ1osR0FBR0QsTUFBTTtZQUNURSxRQUFRVCxnRUFBVUEsQ0FBQ1Usa0JBQWtCLENBQUM7Z0JBQUVKO1lBQUs7UUFDL0M7SUFDRjtJQUNBSyxLQUFLLENBQUMsRUFBRUwsSUFBSSxFQUFFLEdBQUdDLFFBQWtDO1FBQ2pELE9BQU9QLGdFQUFVQSxDQUFDRyxHQUFHLENBQVFMLHlEQUFhQSxDQUFDSSxLQUFLLEVBQUU7WUFDaERNLFlBQVk7WUFDWixHQUFHRCxNQUFNO1lBQ1RFLFFBQVFULGdFQUFVQSxDQUFDVSxrQkFBa0IsQ0FBQztnQkFBRUo7WUFBSztRQUMvQztJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BtYXJ2ZWwvYWRtaW4tcmVzdC8uL3NyYy9kYXRhL2NsaWVudC90YXgudHM/ZGIyOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xyXG4gIFRheCxcclxuICBRdWVyeU9wdGlvbnMsXHJcbiAgVGF4SW5wdXQsXHJcbiAgVGF4UXVlcnlPcHRpb25zLFxyXG4gIFRheFBhZ2luYXRvcixcclxufSBmcm9tICdAL3R5cGVzJztcclxuaW1wb3J0IHsgQVBJX0VORFBPSU5UUyB9IGZyb20gJy4vYXBpLWVuZHBvaW50cyc7XHJcbmltcG9ydCB7IGNydWRGYWN0b3J5IH0gZnJvbSAnLi9jdXJkLWZhY3RvcnknO1xyXG5pbXBvcnQgeyBIdHRwQ2xpZW50IH0gZnJvbSAnQC9kYXRhL2NsaWVudC9odHRwLWNsaWVudCc7XHJcblxyXG5leHBvcnQgY29uc3QgdGF4Q2xpZW50ID0ge1xyXG4gIC4uLmNydWRGYWN0b3J5PFRheCwgUXVlcnlPcHRpb25zLCBUYXhJbnB1dD4oQVBJX0VORFBPSU5UUy5UQVhFUyksXHJcbiAgZ2V0KHsgaWQgfTogeyBpZDogc3RyaW5nIH0pIHtcclxuICAgIHJldHVybiBIdHRwQ2xpZW50LmdldDxUYXg+KGAke0FQSV9FTkRQT0lOVFMuVEFYRVN9LyR7aWR9YCk7XHJcbiAgfSxcclxuICBwYWdpbmF0ZWQ6ICh7IG5hbWUsIC4uLnBhcmFtcyB9OiBQYXJ0aWFsPFRheFF1ZXJ5T3B0aW9ucz4pID0+IHtcclxuICAgIHJldHVybiBIdHRwQ2xpZW50LmdldDxUYXhQYWdpbmF0b3I+KEFQSV9FTkRQT0lOVFMuVEFYRVMsIHtcclxuICAgICAgc2VhcmNoSm9pbjogJ2FuZCcsXHJcbiAgICAgIC4uLnBhcmFtcyxcclxuICAgICAgc2VhcmNoOiBIdHRwQ2xpZW50LmZvcm1hdFNlYXJjaFBhcmFtcyh7IG5hbWUgfSksXHJcbiAgICB9KTtcclxuICB9LFxyXG4gIGFsbDogKHsgbmFtZSwgLi4ucGFyYW1zIH06IFBhcnRpYWw8VGF4UXVlcnlPcHRpb25zPikgPT4ge1xyXG4gICAgcmV0dXJuIEh0dHBDbGllbnQuZ2V0PFRheFtdPihBUElfRU5EUE9JTlRTLlRBWEVTLCB7XHJcbiAgICAgIHNlYXJjaEpvaW46ICdhbmQnLFxyXG4gICAgICAuLi5wYXJhbXMsXHJcbiAgICAgIHNlYXJjaDogSHR0cENsaWVudC5mb3JtYXRTZWFyY2hQYXJhbXMoeyBuYW1lIH0pLFxyXG4gICAgfSk7XHJcbiAgfSxcclxufTtcclxuIl0sIm5hbWVzIjpbIkFQSV9FTkRQT0lOVFMiLCJjcnVkRmFjdG9yeSIsIkh0dHBDbGllbnQiLCJ0YXhDbGllbnQiLCJUQVhFUyIsImdldCIsImlkIiwicGFnaW5hdGVkIiwibmFtZSIsInBhcmFtcyIsInNlYXJjaEpvaW4iLCJzZWFyY2giLCJmb3JtYXRTZWFyY2hQYXJhbXMiLCJhbGwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/data/client/tax.ts\n");

/***/ }),

/***/ "./src/data/tax.ts":
/*!*************************!*\
  !*** ./src/data/tax.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateTaxClassMutation: () => (/* binding */ useCreateTaxClassMutation),\n/* harmony export */   useDeleteTaxMutation: () => (/* binding */ useDeleteTaxMutation),\n/* harmony export */   useTaxQuery: () => (/* binding */ useTaxQuery),\n/* harmony export */   useTaxesQuery: () => (/* binding */ useTaxesQuery),\n/* harmony export */   useUpdateTaxClassMutation: () => (/* binding */ useUpdateTaxClassMutation)\n/* harmony export */ });\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _client_tax__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/tax */ \"./src/data/client/tax.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_4__, _client_tax__WEBPACK_IMPORTED_MODULE_6__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_4__, _client_tax__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst useCreateTaxClassMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.create, {\n        onSuccess: ()=>{\n            router.push(_config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.tax.list);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES);\n        }\n    });\n};\nconst useDeleteTaxMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES);\n        }\n    });\n};\nconst useUpdateTaxClassMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.update, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES);\n        }\n    });\n};\nconst useTaxQuery = (id)=>{\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES,\n        id\n    ], ()=>_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.get({\n            id\n        }));\n};\nconst useTaxesQuery = (options = {})=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES,\n        options\n    ], ({ queryKey, pageParam })=>_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.all(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        taxes: data ?? [],\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/tax.ts\n");

/***/ })

};
;