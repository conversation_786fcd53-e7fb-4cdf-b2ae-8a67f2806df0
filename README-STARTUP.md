# 🚀 oneKart Development Startup Scripts

Quick reference for starting the oneKart e-commerce platform in development mode.

## 🎯 Recommended Method (Cross-Platform)

**Use the Node.js script for the most reliable experience:**

```bash
# Quick start
node start-dev.js

# Install dependencies first
node start-dev.js --install

# Show help
node start-dev.js --help
```

## 🖥️ Platform-Specific Options

### Windows Users

**Option 1: PowerShell (Recommended for Windows)**
```powershell
.\start-dev.ps1 -Install
```

**Option 2: Command Prompt**
```cmd
start-dev.bat --install
```

### Linux/macOS Users

```bash
chmod +x start-dev.sh
./start-dev.sh --install
```

## 🌐 What Gets Started

All scripts start these services:

- **🔧 API Server**: http://localhost:9000/api
- **📊 Admin Dashboard**: http://localhost:3002
- **🛍️ Shop Frontend**: http://localhost:3005

## 🎨 Features Included

- **🌌 Purple Nebula Dark Theme**
- **🏷️ oneKart Branding**
- **🔄 Hot Reload**
- **📱 Responsive Design**

## 🛑 Stopping Services

Press `Ctrl+C` in the terminal where you ran the script.

## 🔧 Troubleshooting

### Common Issues

**"Port already in use"**
- Scripts automatically handle port conflicts
- If issues persist, restart your computer

**"Command not found"**
- Ensure Node.js 18+ is installed
- Run `node --version` to check

**Services not starting**
- Try the `--install` flag to reinstall dependencies
- Check that you're in the project root directory

### Getting Help

1. Run `node start-dev.js --help` for options
2. Check the full `STARTUP_GUIDE.md` for detailed troubleshooting
3. Ensure all prerequisites are installed

## 📁 Available Scripts

| Script | Platform | Description |
|--------|----------|-------------|
| `start-dev.js` | Cross-platform | **Recommended** - Works everywhere |
| `start-dev.ps1` | Windows | PowerShell version |
| `start-dev.bat` | Windows | Batch file version |
| `start-dev.sh` | Linux/macOS | Bash script version |
| `start-prod.sh` | Linux/macOS | Docker production mode |
| `start-prod.bat` | Windows | Docker production mode |

---

**Happy coding with oneKart! 🌌🛍️**
