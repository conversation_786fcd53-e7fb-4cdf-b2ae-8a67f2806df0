"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_payment_razorpay_razorpay-payment-modal_tsx"],{

/***/ "./src/components/payment/razorpay/razorpay-payment-modal.tsx":
/*!********************************************************************!*\
  !*** ./src/components/payment/razorpay/razorpay-payment-modal.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_use_razorpay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/use-razorpay */ \"./src/lib/use-razorpay.ts\");\n/* harmony import */ var _lib_format_address__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/format-address */ \"./src/lib/format-address.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\n/* harmony import */ var _framework_order__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/framework/order */ \"./src/framework/rest/order.ts\");\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst RazorpayPaymentModal = (param)=>{\n    let { trackingNumber, paymentIntentInfo, paymentGateway } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__.useModalAction)();\n    const { loadRazorpayScript, checkScriptLoaded } = (0,_lib_use_razorpay__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const { settings, isLoading: isSettingsLoading } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_6__.useSettings)();\n    const { order, isLoading, refetch } = (0,_framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrder)({\n        tracking_number: trackingNumber\n    });\n    const { createOrderPayment } = (0,_framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrderPayment)();\n    // @ts-ignore\n    const { customer_name, customer_contact, customer, billing_address } = order !== null && order !== void 0 ? order : {};\n    const paymentHandle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        var _settings_logo;\n        if (!checkScriptLoaded()) {\n            await loadRazorpayScript();\n        }\n        const options = {\n            key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,\n            amount: paymentIntentInfo === null || paymentIntentInfo === void 0 ? void 0 : paymentIntentInfo.amount,\n            currency: paymentIntentInfo === null || paymentIntentInfo === void 0 ? void 0 : paymentIntentInfo.currency,\n            name: customer_name,\n            description: \"\".concat(t(\"text-order\"), \"#\").concat(trackingNumber),\n            image: settings === null || settings === void 0 ? void 0 : (_settings_logo = settings.logo) === null || _settings_logo === void 0 ? void 0 : _settings_logo.original,\n            order_id: paymentIntentInfo === null || paymentIntentInfo === void 0 ? void 0 : paymentIntentInfo.payment_id,\n            handler: async ()=>{\n                closeModal();\n                createOrderPayment({\n                    tracking_number: trackingNumber,\n                    payment_gateway: \"razorpay\"\n                });\n            },\n            prefill: {\n                ...customer_name && {\n                    name: customer_name\n                },\n                ...customer_contact && {\n                    contact: \"+\".concat(customer_contact)\n                },\n                ...(customer === null || customer === void 0 ? void 0 : customer.email) && {\n                    email: customer === null || customer === void 0 ? void 0 : customer.email\n                }\n            },\n            notes: {\n                address: (0,_lib_format_address__WEBPACK_IMPORTED_MODULE_3__.formatAddress)(billing_address)\n            },\n            modal: {\n                ondismiss: async ()=>{\n                    closeModal();\n                    await refetch();\n                }\n            }\n        };\n        const razorpay = window.Razorpay(options);\n        return razorpay.open();\n    }, [\n        isLoading,\n        isSettingsLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !isSettingsLoading) {\n            (async ()=>{\n                await paymentHandle();\n            })();\n        }\n    }, [\n        isLoading,\n        isSettingsLoading\n    ]);\n    if (isLoading || isSettingsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            showText: false\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\razorpay\\\\razorpay-payment-modal.tsx\",\n            lineNumber: 82,\n            columnNumber: 12\n        }, undefined);\n    }\n    return null;\n};\n_s(RazorpayPaymentModal, \"hcUl2sNbUAcfvhIaTW5i7B6+MrI=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__.useModalAction,\n        _lib_use_razorpay__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        _framework_settings__WEBPACK_IMPORTED_MODULE_6__.useSettings,\n        _framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrder,\n        _framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrderPayment\n    ];\n});\n_c = RazorpayPaymentModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RazorpayPaymentModal);\nvar _c;\n$RefreshReg$(_c, \"RazorpayPaymentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/payment/razorpay/razorpay-payment-modal.tsx\n"));

/***/ }),

/***/ "./src/lib/format-address.ts":
/*!***********************************!*\
  !*** ./src/lib/format-address.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAddress: function() { return /* binding */ formatAddress; }\n/* harmony export */ });\nfunction removeFalsy(obj) {\n    return Object.fromEntries(Object.entries(obj).filter((param)=>{\n        let [_, v] = param;\n        return Boolean(v);\n    }));\n}\nfunction formatAddress(address) {\n    if (!address) return;\n    const temp = [\n        \"street_address\",\n        \"state\",\n        \"city\",\n        \"zip\",\n        \"country\"\n    ].reduce((acc, k)=>({\n            ...acc,\n            [k]: address[k]\n        }), {});\n    const formattedAddress = removeFalsy(temp);\n    return Object.values(formattedAddress).join(\", \");\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2Zvcm1hdC1hZGRyZXNzLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFDQSxTQUFTQSxZQUFZQyxHQUFRO0lBQzNCLE9BQU9DLE9BQU9DLFdBQVcsQ0FBQ0QsT0FBT0UsT0FBTyxDQUFDSCxLQUFLSSxNQUFNLENBQUM7WUFBQyxDQUFDQyxHQUFHQyxFQUFFO2VBQUtDLFFBQVFEO0lBQUM7QUFDNUU7QUFFTyxTQUFTRSxjQUFjQyxPQUFvQjtJQUNoRCxJQUFJLENBQUNBLFNBQVM7SUFDZCxNQUFNQyxPQUFPO1FBQUM7UUFBa0I7UUFBUztRQUFRO1FBQU87S0FBVSxDQUFDQyxNQUFNLENBQ3ZFLENBQUNDLEtBQUtDLElBQU87WUFBRSxHQUFHRCxHQUFHO1lBQUUsQ0FBQ0MsRUFBRSxFQUFFLE9BQWdCLENBQUNBLEVBQUU7UUFBQyxJQUNoRCxDQUFDO0lBRUgsTUFBTUMsbUJBQW1CZixZQUFZVztJQUNyQyxPQUFPVCxPQUFPYyxNQUFNLENBQUNELGtCQUFrQkUsSUFBSSxDQUFDO0FBQzlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9saWIvZm9ybWF0LWFkZHJlc3MudHM/Y2IyNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBVc2VyQWRkcmVzcyB9IGZyb20gJ0AvdHlwZXMnO1xuZnVuY3Rpb24gcmVtb3ZlRmFsc3kob2JqOiBhbnkpIHtcbiAgcmV0dXJuIE9iamVjdC5mcm9tRW50cmllcyhPYmplY3QuZW50cmllcyhvYmopLmZpbHRlcigoW18sIHZdKSA9PiBCb29sZWFuKHYpKSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRBZGRyZXNzKGFkZHJlc3M6IFVzZXJBZGRyZXNzKSB7XG4gIGlmICghYWRkcmVzcykgcmV0dXJuO1xuICBjb25zdCB0ZW1wID0gWydzdHJlZXRfYWRkcmVzcycsICdzdGF0ZScsICdjaXR5JywgJ3ppcCcsICdjb3VudHJ5J10ucmVkdWNlKFxuICAgIChhY2MsIGspID0+ICh7IC4uLmFjYywgW2tdOiAoYWRkcmVzcyBhcyBhbnkpW2tdIH0pLFxuICAgIHt9XG4gICk7XG4gIGNvbnN0IGZvcm1hdHRlZEFkZHJlc3MgPSByZW1vdmVGYWxzeSh0ZW1wKTtcbiAgcmV0dXJuIE9iamVjdC52YWx1ZXMoZm9ybWF0dGVkQWRkcmVzcykuam9pbignLCAnKTtcbn1cbiJdLCJuYW1lcyI6WyJyZW1vdmVGYWxzeSIsIm9iaiIsIk9iamVjdCIsImZyb21FbnRyaWVzIiwiZW50cmllcyIsImZpbHRlciIsIl8iLCJ2IiwiQm9vbGVhbiIsImZvcm1hdEFkZHJlc3MiLCJhZGRyZXNzIiwidGVtcCIsInJlZHVjZSIsImFjYyIsImsiLCJmb3JtYXR0ZWRBZGRyZXNzIiwidmFsdWVzIiwiam9pbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/lib/format-address.ts\n"));

/***/ }),

/***/ "./src/lib/use-razorpay.ts":
/*!*********************************!*\
  !*** ./src/lib/use-razorpay.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useRazorpay = ()=>{\n    /* Constants */ const RAZORPAY_SCRIPT = \"https://checkout.razorpay.com/v1/checkout.js\";\n    const isClient = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>\"object\" !== \"undefined\", []);\n    const checkScriptLoaded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isClient || !(\"Razorpay\" in window)) return false;\n        return true;\n    }, []);\n    const loadRazorpayScript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isClient) return; // Don't execute this function if it's rendering on server side\n        return new Promise((resolve, reject)=>{\n            const scriptTag = document.createElement(\"script\");\n            scriptTag.src = RAZORPAY_SCRIPT;\n            scriptTag.onload = (ev)=>resolve(ev);\n            scriptTag.onerror = (err)=>reject(err);\n            document.body.appendChild(scriptTag);\n        });\n    }, []);\n    return {\n        checkScriptLoaded,\n        loadRazorpayScript\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useRazorpay);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/use-razorpay.ts\n"));

/***/ })

}]);