{"name": "student-apigw", "version": "1.0.0", "description": "Student API Gateway", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "http-proxy-middleware": "^2.0.6", "express-rate-limit": "^6.8.1", "dotenv": "^16.3.1", "axios": "^1.4.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "keywords": ["api-gateway", "student", "microservice", "proxy", "nodejs", "express"], "author": "oneKart Team", "license": "MIT"}