import {
  Column,
  Model,
  Table,
  DataType,
  HasMany,
  BelongsTo,
  ForeignKey,
  HasOne,
} from 'sequelize-typescript';
import { Address } from 'src/addresses/entities/address.entity';
// import { Order } from 'src/orders/entities/order.entity';
import { Shop } from 'src/shops/entities/shop.entity';
import { Profile } from './profile.entity';

@Table({
  tableName: 'users',
  timestamps: true,
})
export class User extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  email: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  password?: string;

  @HasOne(() => Profile)
  profile?: Profile;

  @HasMany(() => Shop, 'owner_id')
  shops?: Shop[];

  @BelongsTo(() => Shop, 'managed_shop_id')
  managed_shop?: Shop;

  @ForeignKey(() => Shop)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  managed_shop_id?: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
  })
  is_active?: boolean;

  @HasMany(() => Address, 'customer_id')
  address?: Address[];

  @HasMany(() => Permission, 'user_id')
  permissions?: Permission[];

  // @HasMany(() => Order, 'customer_id')
  // orders?: Order[];

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  wallet?: any;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}

@Table({
  tableName: 'permissions',
  timestamps: true,
})
export class Permission extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  name?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  guard_name?: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  user_id?: number;

  @BelongsTo(() => User)
  user?: User;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  pivot?: any;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}
