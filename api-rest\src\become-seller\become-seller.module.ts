import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { BecomeSellerController } from './become-seller.controller';
import { BecomeSellerService } from './become-seller.service';
import { BecomeSeller } from './entities/become-seller.entity';

@Module({
  imports: [SequelizeModule.forFeature([BecomeSeller])],
  controllers: [BecomeSellerController],
  providers: [BecomeSellerService],
  exports: [BecomeSellerService],
})
export class BecomeSellerModule {}
