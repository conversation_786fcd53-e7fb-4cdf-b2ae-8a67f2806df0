"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_cart_cart-counter-button_tsx"],{

/***/ "./src/components/cart/cart-counter-button.tsx":
/*!*****************************************************!*\
  !*** ./src/components/cart/cart-counter-button.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_icons_cart_check_bag__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/icons/cart-check-bag */ \"./src/components/icons/cart-check-bag.tsx\");\n/* harmony import */ var _lib_format_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/format-string */ \"./src/lib/format-string.tsx\");\n/* harmony import */ var _lib_use_price__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/use-price */ \"./src/lib/use-price.tsx\");\n/* harmony import */ var _store_drawer_atom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/drawer-atom */ \"./src/store/drawer-atom.ts\");\n/* harmony import */ var _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/quick-cart/cart.context */ \"./src/store/quick-cart/cart.context.tsx\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! jotai */ \"./node_modules/jotai/esm/index.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CartCounterButton = ()=>{\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const { totalUniqueItems, total } = (0,_store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_5__.useCart)();\n    const [_, setDisplayCart] = (0,jotai__WEBPACK_IMPORTED_MODULE_7__.useAtom)(_store_drawer_atom__WEBPACK_IMPORTED_MODULE_4__.drawerAtom);\n    const { price: totalPrice } = (0,_lib_use_price__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n        amount: total\n    });\n    function handleCartSidebar() {\n        setDisplayCart({\n            display: true,\n            view: \"cart\"\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"product-cart fixed top-1/2 z-40 -mt-12 hidden flex-col items-center justify-center rounded bg-accent p-3 pt-3.5 text-sm font-semibold text-light shadow-900 transition-colors duration-200 hover:bg-accent-hover focus:outline-0 ltr:right-0 ltr:rounded-tr-none ltr:rounded-br-none rtl:left-0 rtl:rounded-tl-none rtl:rounded-bl-none lg:flex\",\n        onClick: handleCartSidebar,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"flex pb-0.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_cart_check_bag__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        className: \"shrink-0\",\n                        width: 14,\n                        height: 16\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-button.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"flex ltr:ml-2 rtl:mr-2\",\n                        children: (0,_lib_format_string__WEBPACK_IMPORTED_MODULE_2__.formatString)(totalUniqueItems, t(\"common:text-item\"))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-button.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-button.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"w-full px-2 py-2 mt-3 rounded bg-light text-accent\",\n                children: totalPrice\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-button.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-button.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CartCounterButton, \"uxwqxQcqk2RuarPy+16hhR7o5DE=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_5__.useCart,\n        jotai__WEBPACK_IMPORTED_MODULE_7__.useAtom,\n        _lib_use_price__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c = CartCounterButton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CartCounterButton);\nvar _c;\n$RefreshReg$(_c, \"CartCounterButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/cart/cart-counter-button.tsx\n"));

/***/ }),

/***/ "./src/components/icons/cart-check-bag.tsx":
/*!*************************************************!*\
  !*** ./src/components/icons/cart-check-bag.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst CartCheckBag = (param)=>{\n    let { width, height, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: width,\n        height: height,\n        className: className,\n        viewBox: \"0 0 12.686 16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            transform: \"translate(-27.023 -2)\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                    transform: \"translate(27.023 5.156)\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M65.7,111.043l-.714-9A1.125,1.125,0,0,0,63.871,101H62.459V103.1a.469.469,0,1,1-.937,0V101H57.211V103.1a.469.469,0,1,1-.937,0V101H54.862a1.125,1.125,0,0,0-1.117,1.033l-.715,9.006a2.605,2.605,0,0,0,2.6,2.8H63.1a2.605,2.605,0,0,0,2.6-2.806Zm-4.224-4.585-2.424,2.424a.468.468,0,0,1-.663,0l-1.136-1.136a.469.469,0,0,1,.663-.663l.8.8,2.092-2.092a.469.469,0,1,1,.663.663Z\",\n                            transform: \"translate(-53.023 -101.005)\",\n                            fill: \"currentColor\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 7\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 6\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 5\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                    transform: \"translate(30.274 2)\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M160.132,0a3.1,3.1,0,0,0-3.093,3.093v.063h.937V3.093a2.155,2.155,0,1,1,4.311,0v.063h.937V3.093A3.1,3.1,0,0,0,160.132,0Z\",\n                            transform: \"translate(-157.039)\",\n                            fill: \"currentColor\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 7\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 6\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 5\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n            lineNumber: 17,\n            columnNumber: 4\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined);\n};\n_c = CartCheckBag;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CartCheckBag);\nvar _c;\n$RefreshReg$(_c, \"CartCheckBag\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/cart-check-bag.tsx\n"));

/***/ }),

/***/ "./src/lib/format-string.tsx":
/*!***********************************!*\
  !*** ./src/lib/format-string.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatString: function() { return /* binding */ formatString; }\n/* harmony export */ });\nfunction formatString(count, string) {\n    if (!count) return \"\".concat(count, \" \").concat(string);\n    return count > 1 ? \"\".concat(count, \" \").concat(string, \"s\") : \"\".concat(count, \" \").concat(string);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2Zvcm1hdC1zdHJpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxTQUFTQSxhQUFhQyxLQUFnQyxFQUFFQyxNQUFjO0lBQzNFLElBQUksQ0FBQ0QsT0FBTyxPQUFPLEdBQVlDLE9BQVRELE9BQU0sS0FBVSxPQUFQQztJQUMvQixPQUFPRCxRQUFRLElBQUksR0FBWUMsT0FBVEQsT0FBTSxLQUFVLE9BQVBDLFFBQU8sT0FBSyxHQUFZQSxPQUFURCxPQUFNLEtBQVUsT0FBUEM7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9mb3JtYXQtc3RyaW5nLnRzeD8zZTllIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBmb3JtYXRTdHJpbmcoY291bnQ6IG51bWJlciB8IG51bGwgfCB1bmRlZmluZWQsIHN0cmluZzogc3RyaW5nKSB7XG4gIGlmICghY291bnQpIHJldHVybiBgJHtjb3VudH0gJHtzdHJpbmd9YDtcbiAgcmV0dXJuIGNvdW50ID4gMSA/IGAke2NvdW50fSAke3N0cmluZ31zYCA6IGAke2NvdW50fSAke3N0cmluZ31gO1xufVxuIl0sIm5hbWVzIjpbImZvcm1hdFN0cmluZyIsImNvdW50Iiwic3RyaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/lib/format-string.tsx\n"));

/***/ }),

/***/ "./src/lib/use-price.tsx":
/*!*******************************!*\
  !*** ./src/lib/use-price.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ usePrice; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   formatVariantPrice: function() { return /* binding */ formatVariantPrice; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\nvar _s = $RefreshSig$();\n\n\n\nfunction formatPrice(param) {\n    let { amount, currencyCode, locale, fractions } = param;\n    const formatCurrency = new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency: currencyCode,\n        maximumFractionDigits: fractions\n    });\n    return formatCurrency.format(amount);\n}\nfunction formatVariantPrice(param) {\n    let { amount, baseAmount, currencyCode, locale, fractions = 2 } = param;\n    const hasDiscount = baseAmount > amount;\n    const formatDiscount = new Intl.NumberFormat(locale, {\n        style: \"percent\"\n    });\n    const discount = hasDiscount ? formatDiscount.format((baseAmount - amount) / baseAmount) : null;\n    const price = formatPrice({\n        amount,\n        currencyCode,\n        locale,\n        fractions\n    });\n    const basePrice = hasDiscount ? formatPrice({\n        amount: baseAmount,\n        currencyCode,\n        locale,\n        fractions\n    }) : null;\n    return {\n        price,\n        basePrice,\n        discount\n    };\n}\nfunction usePrice(data) {\n    _s();\n    const { settings } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_2__.useSettings)();\n    const currency = settings === null || settings === void 0 ? void 0 : settings.currency;\n    const currencyOptions = settings === null || settings === void 0 ? void 0 : settings.currencyOptions;\n    const { amount, baseAmount, currencyCode, currencyOptionsFormat } = {\n        ...data,\n        currencyCode: currency !== null && currency !== void 0 ? currency : \"USD\",\n        currencyOptionsFormat: currencyOptions !== null && currencyOptions !== void 0 ? currencyOptions : {\n            formation: \"en-US\",\n            fractions: 2\n        }\n    };\n    const { formation = \"en-US\", fractions = 2 } = currencyOptionsFormat;\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (typeof amount !== \"number\" || !currencyCode) return \"\";\n        const fractionalDigit = fractions ? fractions : 2;\n        let currentLocale = formation ? formation : \"en\";\n        // if (process.env.NEXT_PUBLIC_ENABLE_MULTI_LANG) {\n        //   currentLocale = locale ? locale : 'en';\n        // }\n        return baseAmount ? formatVariantPrice({\n            amount,\n            baseAmount,\n            currencyCode,\n            locale: currentLocale,\n            fractions: fractionalDigit\n        }) : formatPrice({\n            amount,\n            currencyCode,\n            locale: currentLocale,\n            fractions: fractionalDigit\n        });\n    }, [\n        amount,\n        baseAmount,\n        currencyCode,\n        locale\n    ]);\n    return typeof value === \"string\" ? {\n        price: value,\n        basePrice: null,\n        discount: null\n    } : value;\n}\n_s(usePrice, \"Bur4/Czn9qVPnH4TQg+8FWM+KEI=\", false, function() {\n    return [\n        _framework_settings__WEBPACK_IMPORTED_MODULE_2__.useSettings,\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/use-price.tsx\n"));

/***/ })

}]);