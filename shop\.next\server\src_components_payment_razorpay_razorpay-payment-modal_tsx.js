"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_payment_razorpay_razorpay-payment-modal_tsx";
exports.ids = ["src_components_payment_razorpay_razorpay-payment-modal_tsx"];
exports.modules = {

/***/ "./src/components/payment/razorpay/razorpay-payment-modal.tsx":
/*!********************************************************************!*\
  !*** ./src/components/payment/razorpay/razorpay-payment-modal.tsx ***!
  \********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_use_razorpay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/use-razorpay */ \"./src/lib/use-razorpay.ts\");\n/* harmony import */ var _lib_format_address__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/format-address */ \"./src/lib/format-address.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\n/* harmony import */ var _framework_order__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/framework/order */ \"./src/framework/rest/order.ts\");\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_framework_settings__WEBPACK_IMPORTED_MODULE_6__, _framework_order__WEBPACK_IMPORTED_MODULE_7__]);\n([_framework_settings__WEBPACK_IMPORTED_MODULE_6__, _framework_order__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst RazorpayPaymentModal = ({ trackingNumber, paymentIntentInfo, paymentGateway })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__.useModalAction)();\n    const { loadRazorpayScript, checkScriptLoaded } = (0,_lib_use_razorpay__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const { settings, isLoading: isSettingsLoading } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_6__.useSettings)();\n    const { order, isLoading, refetch } = (0,_framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrder)({\n        tracking_number: trackingNumber\n    });\n    const { createOrderPayment } = (0,_framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrderPayment)();\n    // @ts-ignore\n    const { customer_name, customer_contact, customer, billing_address } = order ?? {};\n    const paymentHandle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!checkScriptLoaded()) {\n            await loadRazorpayScript();\n        }\n        const options = {\n            key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,\n            amount: paymentIntentInfo?.amount,\n            currency: paymentIntentInfo?.currency,\n            name: customer_name,\n            description: `${t(\"text-order\")}#${trackingNumber}`,\n            image: settings?.logo?.original,\n            order_id: paymentIntentInfo?.payment_id,\n            handler: async ()=>{\n                closeModal();\n                createOrderPayment({\n                    tracking_number: trackingNumber,\n                    payment_gateway: \"razorpay\"\n                });\n            },\n            prefill: {\n                ...customer_name && {\n                    name: customer_name\n                },\n                ...customer_contact && {\n                    contact: `+${customer_contact}`\n                },\n                ...customer?.email && {\n                    email: customer?.email\n                }\n            },\n            notes: {\n                address: (0,_lib_format_address__WEBPACK_IMPORTED_MODULE_3__.formatAddress)(billing_address)\n            },\n            modal: {\n                ondismiss: async ()=>{\n                    closeModal();\n                    await refetch();\n                }\n            }\n        };\n        const razorpay = window.Razorpay(options);\n        return razorpay.open();\n    }, [\n        isLoading,\n        isSettingsLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !isSettingsLoading) {\n            (async ()=>{\n                await paymentHandle();\n            })();\n        }\n    }, [\n        isLoading,\n        isSettingsLoading\n    ]);\n    if (isLoading || isSettingsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            showText: false\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\razorpay\\\\razorpay-payment-modal.tsx\",\n            lineNumber: 82,\n            columnNumber: 12\n        }, undefined);\n    }\n    return null;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RazorpayPaymentModal);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/payment/razorpay/razorpay-payment-modal.tsx\n");

/***/ }),

/***/ "./src/lib/format-address.ts":
/*!***********************************!*\
  !*** ./src/lib/format-address.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAddress: () => (/* binding */ formatAddress)\n/* harmony export */ });\nfunction removeFalsy(obj) {\n    return Object.fromEntries(Object.entries(obj).filter(([_, v])=>Boolean(v)));\n}\nfunction formatAddress(address) {\n    if (!address) return;\n    const temp = [\n        \"street_address\",\n        \"state\",\n        \"city\",\n        \"zip\",\n        \"country\"\n    ].reduce((acc, k)=>({\n            ...acc,\n            [k]: address[k]\n        }), {});\n    const formattedAddress = removeFalsy(temp);\n    return Object.values(formattedAddress).join(\", \");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2Zvcm1hdC1hZGRyZXNzLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFDQSxTQUFTQSxZQUFZQyxHQUFRO0lBQzNCLE9BQU9DLE9BQU9DLFdBQVcsQ0FBQ0QsT0FBT0UsT0FBTyxDQUFDSCxLQUFLSSxNQUFNLENBQUMsQ0FBQyxDQUFDQyxHQUFHQyxFQUFFLEdBQUtDLFFBQVFEO0FBQzNFO0FBRU8sU0FBU0UsY0FBY0MsT0FBb0I7SUFDaEQsSUFBSSxDQUFDQSxTQUFTO0lBQ2QsTUFBTUMsT0FBTztRQUFDO1FBQWtCO1FBQVM7UUFBUTtRQUFPO0tBQVUsQ0FBQ0MsTUFBTSxDQUN2RSxDQUFDQyxLQUFLQyxJQUFPO1lBQUUsR0FBR0QsR0FBRztZQUFFLENBQUNDLEVBQUUsRUFBRSxPQUFnQixDQUFDQSxFQUFFO1FBQUMsSUFDaEQsQ0FBQztJQUVILE1BQU1DLG1CQUFtQmYsWUFBWVc7SUFDckMsT0FBT1QsT0FBT2MsTUFBTSxDQUFDRCxrQkFBa0JFLElBQUksQ0FBQztBQUM5QyIsInNvdXJjZXMiOlsid2VicGFjazovL0BvbmVrYXJ0L3Nob3AvLi9zcmMvbGliL2Zvcm1hdC1hZGRyZXNzLnRzP2NiMjQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVXNlckFkZHJlc3MgfSBmcm9tICdAL3R5cGVzJztcbmZ1bmN0aW9uIHJlbW92ZUZhbHN5KG9iajogYW55KSB7XG4gIHJldHVybiBPYmplY3QuZnJvbUVudHJpZXMoT2JqZWN0LmVudHJpZXMob2JqKS5maWx0ZXIoKFtfLCB2XSkgPT4gQm9vbGVhbih2KSkpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0QWRkcmVzcyhhZGRyZXNzOiBVc2VyQWRkcmVzcykge1xuICBpZiAoIWFkZHJlc3MpIHJldHVybjtcbiAgY29uc3QgdGVtcCA9IFsnc3RyZWV0X2FkZHJlc3MnLCAnc3RhdGUnLCAnY2l0eScsICd6aXAnLCAnY291bnRyeSddLnJlZHVjZShcbiAgICAoYWNjLCBrKSA9PiAoeyAuLi5hY2MsIFtrXTogKGFkZHJlc3MgYXMgYW55KVtrXSB9KSxcbiAgICB7fVxuICApO1xuICBjb25zdCBmb3JtYXR0ZWRBZGRyZXNzID0gcmVtb3ZlRmFsc3kodGVtcCk7XG4gIHJldHVybiBPYmplY3QudmFsdWVzKGZvcm1hdHRlZEFkZHJlc3MpLmpvaW4oJywgJyk7XG59XG4iXSwibmFtZXMiOlsicmVtb3ZlRmFsc3kiLCJvYmoiLCJPYmplY3QiLCJmcm9tRW50cmllcyIsImVudHJpZXMiLCJmaWx0ZXIiLCJfIiwidiIsIkJvb2xlYW4iLCJmb3JtYXRBZGRyZXNzIiwiYWRkcmVzcyIsInRlbXAiLCJyZWR1Y2UiLCJhY2MiLCJrIiwiZm9ybWF0dGVkQWRkcmVzcyIsInZhbHVlcyIsImpvaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/lib/format-address.ts\n");

/***/ }),

/***/ "./src/lib/use-razorpay.ts":
/*!*********************************!*\
  !*** ./src/lib/use-razorpay.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useRazorpay = ()=>{\n    /* Constants */ const RAZORPAY_SCRIPT = \"https://checkout.razorpay.com/v1/checkout.js\";\n    const isClient = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>\"undefined\" !== \"undefined\", []);\n    const checkScriptLoaded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isClient || !(\"Razorpay\" in window)) return false;\n        return true;\n    }, []);\n    const loadRazorpayScript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isClient) return; // Don't execute this function if it's rendering on server side\n        return new Promise((resolve, reject)=>{\n            const scriptTag = document.createElement(\"script\");\n            scriptTag.src = RAZORPAY_SCRIPT;\n            scriptTag.onload = (ev)=>resolve(ev);\n            scriptTag.onerror = (err)=>reject(err);\n            document.body.appendChild(scriptTag);\n        });\n    }, []);\n    return {\n        checkScriptLoaded,\n        loadRazorpayScript\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useRazorpay);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/use-razorpay.ts\n");

/***/ })

};
;