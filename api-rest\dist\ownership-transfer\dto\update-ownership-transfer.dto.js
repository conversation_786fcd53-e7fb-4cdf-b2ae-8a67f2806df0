"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOwnershipTransferDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const create_ownership_transfer_dto_1 = require("./create-ownership-transfer.dto");
class UpdateOwnershipTransferDto extends (0, swagger_1.PartialType)(create_ownership_transfer_dto_1.CreateOwnershipTransferDto) {
    static _OPENAPI_METADATA_FACTORY() {
        return {};
    }
}
exports.UpdateOwnershipTransferDto = UpdateOwnershipTransferDto;
//# sourceMappingURL=update-ownership-transfer.dto.js.map