import { CreateShopDto } from './dto/create-shop.dto';
import { UpdateShopDto } from './dto/update-shop.dto';
import { Shop } from './entities/shop.entity';
import { GetShopsDto, ShopPaginator } from './dto/get-shops.dto';
import { GetStaffsDto } from './dto/get-staffs.dto';
export declare class ShopsService {
    private shopModel;
    constructor(shopModel: typeof Shop);
    create(createShopDto: CreateShopDto): Promise<Shop>;
    getShops({ search, limit, page }: GetShopsDto): Promise<ShopPaginator>;
    getNewShops({ search, limit, page, }: GetShopsDto): Promise<ShopPaginator>;
    getStaffs({ shop_id, limit, page }: GetStaffsDto): {
        count: number;
        current_page: number;
        firstItem: number;
        lastItem: number;
        last_page: number;
        per_page: number;
        total: number;
        first_page_url: string;
        last_page_url: string;
        next_page_url: string;
        prev_page_url: string;
        data: import("../users/entities/user.entity").User[];
    };
    getShop(slug: string): Promise<Shop | null>;
    getNearByShop(lat: string, lng: string): any;
    update(id: number, updateShopDto: UpdateShopDto): Promise<Shop | null>;
    approve(id: number): string;
    remove(id: number): string;
    disapproveShop(id: number): any;
    approveShop(id: number): any;
}
