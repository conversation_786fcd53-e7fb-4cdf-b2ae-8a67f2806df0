"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuccessResponse = void 0;
const openapi = require("@nestjs/swagger");
const core_mutation_output_dto_1 = require("./core-mutation-output.dto");
class SuccessResponse extends core_mutation_output_dto_1.CoreMutationOutput {
    static _OPENAPI_METADATA_FACTORY() {
        return {};
    }
}
exports.SuccessResponse = SuccessResponse;
//# sourceMappingURL=success-response.dto.js.map