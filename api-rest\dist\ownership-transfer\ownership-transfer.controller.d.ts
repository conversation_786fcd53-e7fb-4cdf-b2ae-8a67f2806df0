import { OwnershipTransferService } from './ownership-transfer.service';
import { CreateOwnershipTransferDto } from './dto/create-ownership-transfer.dto';
import { GetOwnershipTransferDto } from './dto/get-ownership-transfer.dto';
import { UpdateOwnershipTransferDto } from './dto/update-ownership-transfer.dto';
export declare class OwnershipTransferController {
    private ownershipTransferService;
    constructor(ownershipTransferService: OwnershipTransferService);
    createOwnershipTransfer(createOwnershipTransferDto: CreateOwnershipTransferDto): import("./entities/ownership-transfer.entity").OwnershipTransfer;
    findAll(query: GetOwnershipTransferDto): {
        count: number;
        current_page: number;
        firstItem: number;
        lastItem: number;
        last_page: number;
        per_page: number;
        total: number;
        first_page_url: string;
        last_page_url: string;
        next_page_url: string;
        prev_page_url: string;
        data: import("./entities/ownership-transfer.entity").OwnershipTransfer[];
    };
    getOwnershipTransfer(param: string, language: string): import("./entities/ownership-transfer.entity").OwnershipTransfer;
    update(id: string, language: string, updateRefundDto: UpdateOwnershipTransferDto): import("./entities/ownership-transfer.entity").OwnershipTransfer;
    deleteOwnershipTransfer(id: string): string;
}
