const http = require('http');

// Test data for creating a new student
const newStudent = {
  firstName: 'Alice',
  lastName: '<PERSON>',
  email: '<EMAIL>',
  age: 21,
  course: 'Engineering',
  status: 'active'
};

// Function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testStudentEndpoints() {
  console.log('🧪 Testing Student Management System\n');

  try {
    // Test 1: List students via direct service
    console.log('1️⃣ Testing List Students (Direct Service)');
    const directListResponse = await makeRequest({
      hostname: 'localhost',
      port: 3005,
      path: '/api/students',
      method: 'GET'
    });
    console.log(`   Status: ${directListResponse.statusCode}`);
    console.log(`   Students found: ${directListResponse.body.data?.length || 0}`);
    console.log(`   ✅ Direct service working\n`);

    // Test 2: List students via API Gateway
    console.log('2️⃣ Testing List Students (API Gateway)');
    const gatewayListResponse = await makeRequest({
      hostname: 'localhost',
      port: 3004,
      path: '/api/students',
      method: 'GET'
    });
    console.log(`   Status: ${gatewayListResponse.statusCode}`);
    console.log(`   Students found: ${gatewayListResponse.body.data?.length || 0}`);
    console.log(`   ✅ API Gateway working\n`);

    // Test 3: Create student via direct service
    console.log('3️⃣ Testing Create Student (Direct Service)');
    const directCreateResponse = await makeRequest({
      hostname: 'localhost',
      port: 3005,
      path: '/api/students',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, newStudent);
    console.log(`   Status: ${directCreateResponse.statusCode}`);
    if (directCreateResponse.statusCode === 201) {
      console.log(`   Created student: ${directCreateResponse.body.data?.firstName} ${directCreateResponse.body.data?.lastName}`);
      console.log(`   Student ID: ${directCreateResponse.body.data?.id}`);
      console.log(`   ✅ Student creation via direct service working\n`);
    } else {
      console.log(`   ❌ Failed to create student via direct service`);
      console.log(`   Error: ${JSON.stringify(directCreateResponse.body, null, 2)}\n`);
    }

    // Test 4: Create another student via API Gateway
    const anotherStudent = {
      firstName: 'Bob',
      lastName: 'Wilson',
      email: '<EMAIL>',
      age: 22,
      course: 'Physics',
      status: 'active'
    };

    console.log('4️⃣ Testing Create Student (API Gateway)');
    const gatewayCreateResponse = await makeRequest({
      hostname: 'localhost',
      port: 3004,
      path: '/api/students',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, anotherStudent);
    console.log(`   Status: ${gatewayCreateResponse.statusCode}`);
    if (gatewayCreateResponse.statusCode === 201) {
      console.log(`   Created student: ${gatewayCreateResponse.body.data?.firstName} ${gatewayCreateResponse.body.data?.lastName}`);
      console.log(`   Student ID: ${gatewayCreateResponse.body.data?.id}`);
      console.log(`   ✅ Student creation via API Gateway working\n`);
    } else {
      console.log(`   ❌ Failed to create student via API Gateway`);
      console.log(`   Error: ${JSON.stringify(gatewayCreateResponse.body, null, 2)}\n`);
    }

    // Test 5: List students again to verify creation
    console.log('5️⃣ Testing List Students After Creation');
    const finalListResponse = await makeRequest({
      hostname: 'localhost',
      port: 3004,
      path: '/api/students',
      method: 'GET'
    });
    console.log(`   Status: ${finalListResponse.statusCode}`);
    console.log(`   Total students now: ${finalListResponse.body.data?.length || 0}`);
    if (finalListResponse.body.data) {
      finalListResponse.body.data.forEach((student, index) => {
        console.log(`   ${index + 1}. ${student.firstName} ${student.lastName} - ${student.course}`);
      });
    }
    console.log(`   ✅ Student list updated successfully\n`);

    // Test 6: Test statistics endpoint
    console.log('6️⃣ Testing Statistics Endpoint');
    const statsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3004,
      path: '/api/students/stats/overview',
      method: 'GET'
    });
    console.log(`   Status: ${statsResponse.statusCode}`);
    if (statsResponse.statusCode === 200) {
      console.log(`   Total Students: ${statsResponse.body.data?.totalStudents}`);
      console.log(`   Active Students: ${statsResponse.body.data?.activeStudents}`);
      console.log(`   Course Distribution:`, statsResponse.body.data?.courseDistribution);
      console.log(`   ✅ Statistics endpoint working\n`);
    }

    console.log('🎉 All tests completed successfully!');
    console.log('\n📊 Summary:');
    console.log('✅ Student Service running on port 3005');
    console.log('✅ API Gateway running on port 3004');
    console.log('✅ List students endpoint working');
    console.log('✅ Create student endpoint working');
    console.log('✅ Statistics endpoint working');
    console.log('✅ API Gateway proxy working correctly');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the tests
testStudentEndpoints();
