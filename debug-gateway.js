const http = require('http');

// Test the API Gateway with detailed debugging
async function testGateway() {
  console.log('🔍 Debugging API Gateway...\n');

  // Test data
  const testStudent = {
    firstName: 'Debug',
    lastName: 'Test',
    email: '<EMAIL>',
    age: 23,
    course: 'Debugging'
  };

  const postData = JSON.stringify(testStudent);

  const options = {
    hostname: 'localhost',
    port: 3004,
    path: '/api/students',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    },
    timeout: 5000
  };

  console.log('📤 Sending POST request to API Gateway...');
  console.log('URL:', `http://${options.hostname}:${options.port}${options.path}`);
  console.log('Data:', postData);
  console.log('Headers:', options.headers);

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      console.log(`\n📥 Response received:`);
      console.log(`Status: ${res.statusCode}`);
      console.log(`Headers:`, res.headers);

      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });

      res.on('end', () => {
        console.log(`Body: ${body}`);
        
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            body: jsonBody
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            body: body
          });
        }
      });
    });

    req.on('error', (e) => {
      console.error(`❌ Request error: ${e.message}`);
      reject(e);
    });

    req.on('timeout', () => {
      console.error('❌ Request timeout');
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

// Also test a simple GET request
async function testGetRequest() {
  console.log('\n🔍 Testing GET request through gateway...\n');

  const options = {
    hostname: 'localhost',
    port: 3004,
    path: '/api/students',
    method: 'GET',
    timeout: 5000
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      console.log(`📥 GET Response: ${res.statusCode}`);

      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });

      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          console.log(`Students count: ${jsonBody.data?.length || 0}`);
          resolve(jsonBody);
        } catch (e) {
          console.log(`Raw body: ${body}`);
          resolve(body);
        }
      });
    });

    req.on('error', (e) => {
      console.error(`❌ GET Request error: ${e.message}`);
      reject(e);
    });

    req.on('timeout', () => {
      console.error('❌ GET Request timeout');
      req.destroy();
      reject(new Error('GET Request timeout'));
    });

    req.end();
  });
}

async function runTests() {
  try {
    // Test GET first
    await testGetRequest();
    
    // Then test POST
    await testGateway();
    
    console.log('\n✅ Debug tests completed');
  } catch (error) {
    console.error('❌ Debug test failed:', error.message);
  }
}

runTests();
