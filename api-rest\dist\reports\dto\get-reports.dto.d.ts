import { SortOrder } from 'src/common/dto/generic-conditions.dto';
import { PaginationArgs } from 'src/common/dto/pagination-args.dto';
import { Paginator } from 'src/common/dto/paginator.dto';
import { MyReports } from '../entities/report.entity';
export declare class MyReportPaginator extends Paginator<MyReports> {
    data: MyReports[];
}
export declare class GetMyReportDto extends PaginationArgs {
    orderBy?: QueryReviewsOrderByColumn;
    sortedBy?: SortOrder;
    search?: string;
}
export declare enum QueryReviewsOrderByColumn {
    NAME = "NAME",
    CREATED_AT = "CREATED_AT",
    UPDATED_AT = "UPDATED_AT"
}
