"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WithdrawsService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const withdraw_entity_1 = require("./entities/withdraw.entity");
const paginate_1 = require("../common/pagination/paginate");
let WithdrawsService = class WithdrawsService {
    constructor(withdrawModel) {
        this.withdrawModel = withdrawModel;
    }
    async create(createWithdrawDto) {
        return this.withdrawModel.create(createWithdrawDto);
    }
    async getWithdraws({ limit, page, status, shop_id, }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 15;
        const offset = (page - 1) * limit;
        const { count, rows: data } = await this.withdrawModel.findAndCountAll({
            limit,
            offset,
        });
        const url = `/withdraws?limit=${limit}`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
    findOne(id) {
        return `This action returns a #${id} withdraw`;
    }
    async update(id, updateWithdrawDto) {
        await this.withdrawModel.update(updateWithdrawDto, {
            where: { id },
        });
        return this.withdrawModel.findByPk(id);
    }
    remove(id) {
        return `This action removes a #${id} withdraw`;
    }
};
WithdrawsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(withdraw_entity_1.Withdraw)),
    __metadata("design:paramtypes", [Object])
], WithdrawsService);
exports.WithdrawsService = WithdrawsService;
//# sourceMappingURL=withdraws.service.js.map