import { CreateAttributeDto } from './dto/create-attribute.dto';
import { UpdateAttributeDto } from './dto/update-attribute.dto';
import { Attribute } from './entities/attribute.entity';
export declare class AttributesService {
    private attributeModel;
    constructor(attributeModel: typeof Attribute);
    create(createAttributeDto: CreateAttributeDto): Promise<Attribute>;
    findAll(): Promise<Attribute[]>;
    findOne(param: string): Promise<Attribute | null>;
    update(id: number, updateAttributeDto: UpdateAttributeDto): Promise<Attribute | null>;
    remove(id: number): string;
}
