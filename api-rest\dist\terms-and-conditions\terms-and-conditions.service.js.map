{"version": 3, "file": "terms-and-conditions.service.js", "sourceRoot": "", "sources": ["../../src/terms-and-conditions/terms-and-conditions.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,0GAAmE;AACnE,wFAA4E;AAC5E,yDAAiD;AACjD,sDAA2B;AAC3B,4DAA0D;AAK1D,MAAM,kBAAkB,GAAG,IAAA,gCAAY,EACrC,gDAAkB,EAClB,mCAAsB,CACvB,CAAC;AACF,MAAM,OAAO,GAAG;IACd,IAAI,EAAE,CAAC,OAAO,CAAC;IACf,SAAS,EAAE,GAAG;CACf,CAAC;AACF,MAAM,IAAI,GAAG,IAAI,iBAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;AAGnD,IAAa,yBAAyB,GAAtC,MAAa,yBAAyB;IAAtC;QACU,uBAAkB,GAAyB,kBAAkB,CAAC;IAkExE,CAAC;IAhEC,MAAM,CAAC,2BAAwD;QAC7D,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IAED,yBAAyB,CAAC,EACxB,MAAM,EACN,KAAK,EACL,IAAI,EACJ,WAAW,GACc;;QACzB,IAAI,CAAC,IAAI;YAAE,IAAI,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,KAAK;YAAE,KAAK,GAAG,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;QAC9B,IAAI,IAAI,GAAyB,IAAI,CAAC,kBAAkB,CAAC;QAEzD,IAAI,MAAM,EAAE;YACV,MAAM,iBAAiB,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5C,KAAK,MAAM,WAAW,IAAI,iBAAiB,EAAE;gBAC3C,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC5C,IAAI,GAAG,MAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;aACpD;SACF;QAED,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC/C,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;YACvB,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;SACnD;QACD,MAAM,GAAG,GAAG,gCAAgC,MAAM,UAAU,KAAK,EAAE,CAAC;QACpE,uBACE,IAAI,EAAE,OAAO,IACV,IAAA,mBAAQ,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAC1D;IACJ,CAAC;IAED,OAAO,CAAC,KAAa,EAAE,QAAgB;QACrC,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,CAAC,EAAU,EAAE,2BAAwD;QACzE,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,EAAU;QACf,OAAO,0BAA0B,EAAE,uBAAuB,CAAC;IAC7D,CAAC;IAED,2BAA2B,CAAC,EAAU;QACpC,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CACpD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,CAC3B,CAAC;QACF,iBAAiB,CAAC,WAAW,GAAG,KAAK,CAAC;QAEtC,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,wBAAwB,CAAC,EAAU;QACjC,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CACpD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,CAC3B,CAAC;QACF,iBAAiB,CAAC,WAAW,GAAG,IAAI,CAAC;QAErC,OAAO,iBAAiB,CAAC;IAC3B,CAAC;CACF,CAAA;AAnEY,yBAAyB;IADrC,IAAA,mBAAU,GAAE;GACA,yBAAyB,CAmErC;AAnEY,8DAAyB"}