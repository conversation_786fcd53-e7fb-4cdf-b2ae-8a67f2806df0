import {
  Column,
  Model,
  Table,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  BelongsToMany,
} from 'sequelize-typescript';
import { AttributeValue } from 'src/attributes/entities/attribute-value.entity';
import {
  Category,
  ProductCategory,
} from 'src/categories/entities/category.entity';
import { Order } from 'src/orders/entities/order.entity';
import { Shop } from 'src/shops/entities/shop.entity';
import { Tag } from 'src/tags/entities/tag.entity';
import { Type } from 'src/types/entities/type.entity';
import { Review } from '../../reviews/entities/review.entity';

enum ProductStatus {
  PUBLISH = 'publish',
  DRAFT = 'draft',
}

enum ProductType {
  SIMPLE = 'simple',
  VARIABLE = 'variable',
}

@Table({
  tableName: 'products',
  timestamps: true,
})
export class Product extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  slug: string;

  @ForeignKey(() => Type)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  type_id: number;

  @BelongsTo(() => Type)
  type: Type;

  @Column({
    type: DataType.ENUM('simple', 'variable'),
    allowNull: false,
    defaultValue: 'simple',
  })
  product_type: ProductType;

  @BelongsToMany(() => Category, () => ProductCategory)
  categories: Category[];

  @BelongsToMany(() => Tag, () => ProductTag)
  tags?: Tag[];

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  variations?: AttributeValue[];

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  variation_options?: any[];

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  pivot?: any; // OrderProductPivot

  // @BelongsToMany(() => Order, () => OrderProduct)
  // orders?: Order[];

  @ForeignKey(() => Shop)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  shop_id: number;

  @BelongsTo(() => Shop)
  shop: Shop;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  related_products?: Product[];

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
  })
  in_stock: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  is_taxable: boolean;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true,
  })
  sale_price?: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true,
  })
  max_price?: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true,
  })
  min_price?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  sku?: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  gallery?: any[]; // Attachment[]

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  image?: any; // Attachment

  @Column({
    type: DataType.ENUM('publish', 'draft'),
    allowNull: false,
    defaultValue: 'draft',
  })
  status: ProductStatus;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  height?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  length?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  width?: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true,
  })
  price?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
  })
  quantity: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'piece',
  })
  unit: string;

  @Column({
    type: DataType.DECIMAL(3, 2),
    allowNull: false,
    defaultValue: 0,
  })
  ratings: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  in_wishlist: boolean;

  @HasMany(() => Review, 'product_id')
  my_review?: Review[];

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'en',
  })
  language?: string;

  @Column({
    type: DataType.ARRAY(DataType.STRING),
    allowNull: true,
  })
  translated_languages?: string[];

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  visibility?: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}

// Junction table for many-to-many relationship between Product and Tag
@Table({
  tableName: 'product_tags',
  timestamps: false,
})
export class ProductTag extends Model {
  @ForeignKey(() => Product)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  product_id: number;

  @ForeignKey(() => Tag)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  tag_id: number;
}

export class OrderProductPivot {
  variation_option_id?: number;
  order_quantity: number;
  unit_price: number;
  subtotal: number;
}

export class Variation {
  id: number;
  title: string;
  price: number;
  sku: string;
  is_disable: boolean;
  sale_price?: number;
  quantity: number;
  options: VariationOption[];
}

export class VariationOption {
  name: string;
  value: string;
}

export class File extends CoreEntity {
  attachment_id: number;
  url: string;
  fileable_id: number;
}
