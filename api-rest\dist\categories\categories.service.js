"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoriesService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const category_entity_1 = require("./entities/category.entity");
const type_entity_1 = require("../types/entities/type.entity");
const sequelize_2 = require("sequelize");
let CategoriesService = class CategoriesService {
    constructor(categoryModel) {
        this.categoryModel = categoryModel;
    }
    async create(createCategoryDto) {
        return this.categoryModel.create(Object.assign({}, createCategoryDto));
    }
    async getCategories({ limit, page, search, parent }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const whereClause = {};
        if (search) {
            const parseSearchParams = search.split(';');
            for (const searchParam of parseSearchParams) {
                const [key, value] = searchParam.split(':');
                if (key !== 'slug' && key in this.categoryModel.rawAttributes) {
                    whereClause[key] = {
                        [sequelize_2.Op.iLike]: `%${value}%`,
                    };
                }
            }
        }
        if (parent === 'null') {
            whereClause.parent_id = null;
        }
        const { count, rows } = await this.categoryModel.findAndCountAll({
            where: whereClause,
            include: [
                { model: type_entity_1.Type, as: 'type' },
                { model: category_entity_1.Category, as: 'parent' },
                { model: category_entity_1.Category, as: 'children' },
            ],
            limit,
            offset,
            order: [['created_at', 'DESC']],
        });
        const url = `/categories?search=${search}&limit=${limit}&parent=${parent}`;
        const totalPages = Math.ceil(count / limit);
        return {
            data: rows,
            count,
            current_page: page,
            firstItem: offset + 1,
            lastItem: Math.min(offset + limit, count),
            last_page: totalPages,
            per_page: limit,
            total: count,
            first_page_url: `${url}&page=1`,
            last_page_url: `${url}&page=${totalPages}`,
            next_page_url: page < totalPages ? `${url}&page=${page + 1}` : null,
            prev_page_url: page > 1 ? `${url}&page=${page - 1}` : null,
        };
    }
    async getCategory(param, language) {
        return this.categoryModel.findOne({
            where: {
                [sequelize_2.Op.or]: [{ id: Number(param) || 0 }, { slug: param }],
            },
            include: [
                { model: type_entity_1.Type, as: 'type' },
                { model: category_entity_1.Category, as: 'parent' },
                { model: category_entity_1.Category, as: 'children' },
            ],
        });
    }
    async update(id, updateCategoryDto) {
        const [affectedCount, updatedCategories] = await this.categoryModel.update(Object.assign({}, updateCategoryDto), { where: { id }, returning: true });
        return [affectedCount, updatedCategories];
    }
    async remove(id) {
        return this.categoryModel.destroy({ where: { id } });
    }
};
CategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(category_entity_1.Category)),
    __metadata("design:paramtypes", [Object])
], CategoriesService);
exports.CategoriesService = CategoriesService;
//# sourceMappingURL=categories.service.js.map