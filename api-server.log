
> @onekart/api-rest@11.10.0 start:dev
> nest start --watch

c[[90m6:23:38 AM[0m] Starting compilation in watch mode...

[[90m6:23:40 AM[0m] Found 0 errors. Watching for file changes.

[32m[Nest] 1792  - [39m07/15/2025, 6:23:41 AM [32m    LOG[39m [38;5;3m[NestFactory] [39m[32mStarting Nest application...[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAppModule dependencies initialized[39m[38;5;3m +63ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mStripeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mStripeCoreModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mCommonModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mPaymentModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mConfigHostModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSettingsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mImportsModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mRefundsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAuthorsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mNewslettersModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mQuestionModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mReportsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mFeedbackModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mPaymentIntentModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mWebHookModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mConversationsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mMessagesModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mFaqsModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mNotifyLogsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mTermsAndConditionsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mRefundPoliciesModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mRefundReasonModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mOwnershipTransferModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mConfigModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mConfigModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:42 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mUploadsModule dependencies initialized[39m[38;5;3m +0ms[39m
Executing (default): SELECT 1+1 AS result
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeCoreModule dependencies initialized[39m[38;5;3m +1067ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mSequelizeModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mStoreNoticesModule dependencies initialized[39m[38;5;3m +2ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mUsersModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mProductsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAuthModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mCategoriesModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAnalyticsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAttributesModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mShippingsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mTaxesModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mTagsModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mShopsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mTypesModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mWithdrawsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mCouponsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAddressesModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mManufacturersModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mReviewModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mWishlistsModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAiModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mFlashSaleModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mBecomeSellerModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mPaymentMethodModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mOrdersModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mUsersController {/api/users}:[39m[38;5;3m +149ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/users, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/users, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/users/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/users/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/users/:id, DELETE} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/users/unblock-user, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/users/block-user, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/users/make-admin, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mProfilesController {/api/profiles}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/profiles, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/profiles/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/profiles/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAdminController {/api/admin/list}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/admin/list, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mVendorController {/api/vendors/list}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/vendors/list, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mMyStaffsController {/api/my-staffs}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-staffs, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAllStaffsController {/api/all-staffs}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/all-staffs, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAllCustomerController {/api/customers/list}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/customers/list, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mProductsController {/api/products}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/products, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/products, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/products/:slug, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/products/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/products/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mPopularProductsController {/api/popular-products}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/popular-products, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mBestSellingProductsController {/api/best-selling-products}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/best-selling-products, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mProductsStockController {/api/products-stock}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/products-stock, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mDraftProductsController {/api/draft-products}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/draft-products, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mOrdersController {/api/orders}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/orders, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/orders, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/orders/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/orders/tracking-number/:tracking_id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/orders/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/orders/:id, DELETE} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/orders/checkout/verify, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/orders/payment, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mOrderStatusController {/api/order-status}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/order-status, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/order-status, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/order-status/:param, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/order-status/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/order-status/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mOrderFilesController {/api/downloads}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/downloads, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/downloads/digital_file, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mOrderExportController {/api/export-order-url}:[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/export-order-url, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mDownloadInvoiceController {/api/download-invoice-url}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/download-invoice-url, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAuthController {/api}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/register, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/token, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/social-login-token, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/otp-login, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/send-otp-code, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/verify-otp-code, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/forget-password, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/reset-password, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/change-password, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/logout, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/verify-forget-password-token, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/me, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/add-points, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/contact-us, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mCategoriesController {/api/categories}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/categories, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/categories, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/categories/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/categories/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/categories/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAnalyticsController {/api/analytics}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/analytics, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mCategoryWiseProductController {/api/category-wise-product}:[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/category-wise-product, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mLowStockProductsController {/api/low-stock-products}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/low-stock-products, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mTopRateProductController {/api/top-rate-product}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/top-rate-product, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAttributesController {/api/attributes}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/attributes, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/attributes, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/attributes/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/attributes/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/attributes/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mShippingsController {/api/shippings}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shippings, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shippings, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shippings/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shippings/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shippings/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mTaxesController {/api/taxes}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/taxes, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/taxes, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/taxes/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/taxes/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/taxes/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mTagsController {/api/tags}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/tags, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/tags, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/tags/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/tags/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/tags/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mShopsController {/api/shops}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shops, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shops, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shops/:slug, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shops/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shops/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shops/approve, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/shops/disapprove, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mStaffsController {/api/staffs}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/staffs, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/staffs, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/staffs/:slug, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/staffs/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/staffs/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mDisapproveShopController {/api/disapprove-shop}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/disapprove-shop, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mApproveShopController {/api/approve-shop}:[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/approve-shop, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mNearByShopController {/api/near-by-shop}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/near-by-shop/:lat/:lng, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mNewShopsController {/api/new-shops}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/new-shops, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mTypesController {/api/types}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/types, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/types, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/types/:slug, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/types/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/types/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mWithdrawsController {/api/withdraws}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/withdraws, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/withdraws, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/withdraws/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/withdraws/:id/approve, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/withdraws/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mUploadsController {/api/attachments}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/attachments, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/attachments/:fileName, DELETE} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/attachments/:fileName/url, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mSettingsController {/api/settings}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/settings, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/settings, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mCouponsController {/api/coupons}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/coupons, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/coupons, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/coupons/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/coupons/:id/verify, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/coupons/verify, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/coupons/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/coupons/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mApproveCouponController {/api/approve-coupon}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/approve-coupon, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mDisapproveCouponController {/api/disapprove-coupon}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/disapprove-coupon, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAddressesController {/api/address}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/address, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/address, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/address/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/address/:id, PUT} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/address/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mImportsController {/api}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/import-attributes, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/import-products, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/import-variation-options, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mRefundsController {/api/refunds}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refunds, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refunds, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refunds/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refunds/:id, PATCH} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refunds/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAuthorsController {/api/authors}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/authors, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/authors, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/authors/:slug, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/authors/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/authors/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mTopAuthors {/api/top-authors}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/top-authors, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mManufacturersController {/api/manufacturers}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/manufacturers, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/manufacturers, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/manufacturers/:slug, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/manufacturers/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/manufacturers/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mTopManufacturersController {/api/top-manufacturers}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/top-manufacturers, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mNewslettersController {/api/subscribe-to-newsletter}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/subscribe-to-newsletter, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mReviewController {/api/reviews}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/reviews, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/reviews/:id, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/reviews, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/reviews/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/reviews/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAbusiveReportsController {/api/abusive_reports}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/abusive_reports, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/abusive_reports/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/abusive_reports, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/abusive_reports/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/abusive_reports/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mQuestionController {/api/questions}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/questions, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/questions/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/questions, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/questions/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/questions/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mMyQuestionsController {/api/my-questions}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-questions, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-questions/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-questions, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-questions/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-questions/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mWishlistsController {/api/wishlists}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/wishlists, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/wishlists/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/wishlists, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/wishlists/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/wishlists/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/wishlists/toggle, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/wishlists/in_wishlist/:product_id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mMyWishlistsController {/api/my-wishlists}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-wishlists, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-wishlists/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-wishlists, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-wishlists/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-wishlists/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mReportsController {/api/my-reports}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/my-reports, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mFeedbackController {/api/feedbacks}:[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/feedbacks, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/feedbacks/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/feedbacks, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/feedbacks/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/feedbacks/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mPaymentMethodController {/api/cards}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/cards, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/cards, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/cards/:id, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/cards/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/cards/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mSetDefaultCartController {/api/set-default-card}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/set-default-card, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mSavePaymentMethodController {/api/save-payment-method}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/save-payment-method, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mPaymentIntentController {/api/payment-intent}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/payment-intent, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mWebHookController {/api/web-hook}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/web-hook/razorpay, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/web-hook/stripe, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/web-hook/paypal, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mStoreNoticesController {/api/store-notices}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/store-notices, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/store-notices, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/store-notices/getUsersToNotify, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/store-notices/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/store-notices/:id, PUT} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/store-notices/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mConversationsController {/api/conversations}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/conversations, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/conversations, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/conversations/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mMessagesController {/api/messages/conversations}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/messages/conversations/:id, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/messages/conversations/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAiController {/api}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/generate-descriptions, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mFaqsController {/api/faqs}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/faqs, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/faqs, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/faqs/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/faqs/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/faqs/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mNotifyLogsController {/api/notify-logs}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/notify-logs, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/notify-logs/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/notify-logs/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/notify-logs/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/notify-logs/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mTermsAndConditionsController {/api/terms-and-conditions}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/terms-and-conditions, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/terms-and-conditions, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/terms-and-conditions/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/terms-and-conditions/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/terms-and-conditions/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mDisapproveTermsAndConditionController {/api/disapprove-terms-and-conditions}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/disapprove-terms-and-conditions, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mApproveTermsAndConditionController {/api/approve-terms-and-conditions}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/approve-terms-and-conditions, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mFlashSaleController {/api/flash-sale}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/flash-sale, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/flash-sale, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/flash-sale/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/flash-sale/:id, PUT} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/flash-sale/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mProductsByFlashSaleController {/api/products-by-flash-sale}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/products-by-flash-sale, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mRefundPoliciesController {/api/refund-policies}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refund-policies, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refund-policies, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refund-policies/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refund-policies/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refund-policies/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mRefundReasonsController {/api/refund-reasons}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refund-reasons, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refund-reasons, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refund-reasons/:param, GET} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refund-reasons/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/refund-reasons/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mBecomeSellerController {/api/became-seller}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/became-seller, POST} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/became-seller, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mOwnershipTransferController {/api/ownership-transfer}:[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/ownership-transfer, POST} route[39m[38;5;3m +1ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/ownership-transfer, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/ownership-transfer/:param, GET} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/ownership-transfer/:id, PUT} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api/ownership-transfer/:id, DELETE} route[39m[38;5;3m +0ms[39m
[32m[Nest] 1792  - [39m07/15/2025, 6:23:43 AM [32m    LOG[39m [38;5;3m[NestApplication] [39m[32mNest application successfully started[39m[38;5;3m +7ms[39m
Application is running on: http://[::1]:9000/api
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT count(*) AS "count" FROM "shops" AS "Shop";
Executing (default): SELECT "id", "owner_id", "is_active", "orders_count", "products_count", "name", "slug", "description", "cover_image", "logo", "address", "settings", "distance", "lat", "lng", "created_at", "updated_at", "createdAt", "updatedAt" FROM "shops" AS "Shop" OFFSET NaN;
[31m[Nest] 1792  - [39m07/15/2025, 6:25:25 AM [31m  ERROR[39m [38;5;3m[ExceptionsHandler] [39m[31mcolumn "nan" does not exist[39m
Error: 
    at Query.run (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\api-rest\node_modules\sequelize\src\dialects\postgres\query.js:76:25)
    at E:\Projects\BB\Projects\e-commerce\logorithm-e-site\api-rest\node_modules\sequelize\src\sequelize.js:650:28
    at PostgresQueryInterface.select (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\api-rest\node_modules\sequelize\src\dialects\abstract\query-interface.js:1001:12)
    at Function.findAll (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\api-rest\node_modules\sequelize\src\model.js:1824:21)
    at async Promise.all (index 1)
    at Function.findAndCountAll (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\api-rest\node_modules\sequelize\src\model.js:2183:27)
    at ShopsService.getShops (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\api-rest\src\shops\shops.service.ts:26:35)
    at E:\Projects\BB\Projects\e-commerce\logorithm-e-site\api-rest\node_modules\@nestjs\core\router\router-execution-context.js:46:28
    at E:\Projects\BB\Projects\e-commerce\logorithm-e-site\api-rest\node_modules\@nestjs\core\router\router-proxy.js:9:17
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type";
Executing (default): SELECT "id", "name", "slug", "image", "icon", "banners", "promotional_sliders", "settings", "language", "translated_languages", "created_at", "updated_at", "createdAt", "updatedAt" FROM "types" AS "Type" WHERE "Type"."slug" = 'general';
Executing (default): SELECT count("Product"."id") AS "count" FROM "products" AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public';
Executing (default): SELECT "Product".*, "categories"."id" AS "categories.id", "categories"."name" AS "categories.name", "categories"."slug" AS "categories.slug", "categories"."parent_id" AS "categories.parent_id", "categories"."details" AS "categories.details", "categories"."image" AS "categories.image", "categories"."icon" AS "categories.icon", "categories"."type_id" AS "categories.type_id", "categories"."language" AS "categories.language", "categories"."translated_languages" AS "categories.translated_languages", "categories"."created_at" AS "categories.created_at", "categories"."updated_at" AS "categories.updated_at", "categories"."createdAt" AS "categories.createdAt", "categories"."updatedAt" AS "categories.updatedAt", "categories->ProductCategory"."product_id" AS "categories.ProductCategory.product_id", "categories->ProductCategory"."category_id" AS "categories.ProductCategory.category_id", "tags"."id" AS "tags.id", "tags"."name" AS "tags.name", "tags"."slug" AS "tags.slug", "tags"."parent" AS "tags.parent", "tags"."details" AS "tags.details", "tags"."image" AS "tags.image", "tags"."icon" AS "tags.icon", "tags"."type_id" AS "tags.type_id", "tags"."language" AS "tags.language", "tags"."translated_languages" AS "tags.translated_languages", "tags"."created_at" AS "tags.created_at", "tags"."updated_at" AS "tags.updated_at", "tags"."createdAt" AS "tags.createdAt", "tags"."updatedAt" AS "tags.updatedAt", "tags->ProductTag"."product_id" AS "tags.ProductTag.product_id", "tags->ProductTag"."tag_id" AS "tags.ProductTag.tag_id", "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "shop"."id" AS "shop.id", "shop"."owner_id" AS "shop.owner_id", "shop"."is_active" AS "shop.is_active", "shop"."orders_count" AS "shop.orders_count", "shop"."products_count" AS "shop.products_count", "shop"."name" AS "shop.name", "shop"."slug" AS "shop.slug", "shop"."description" AS "shop.description", "shop"."cover_image" AS "shop.cover_image", "shop"."logo" AS "shop.logo", "shop"."address" AS "shop.address", "shop"."settings" AS "shop.settings", "shop"."distance" AS "shop.distance", "shop"."lat" AS "shop.lat", "shop"."lng" AS "shop.lng", "shop"."created_at" AS "shop.created_at", "shop"."updated_at" AS "shop.updated_at", "shop"."createdAt" AS "shop.createdAt", "shop"."updatedAt" AS "shop.updatedAt" FROM (SELECT "Product"."id", "Product"."name", "Product"."slug", "Product"."type_id", "Product"."product_type", "Product"."variations", "Product"."variation_options", "Product"."pivot", "Product"."shop_id", "Product"."related_products", "Product"."description", "Product"."in_stock", "Product"."is_taxable", "Product"."sale_price", "Product"."max_price", "Product"."min_price", "Product"."sku", "Product"."gallery", "Product"."image", "Product"."status", "Product"."height", "Product"."length", "Product"."width", "Product"."price", "Product"."quantity", "Product"."unit", "Product"."ratings", "Product"."in_wishlist", "Product"."language", "Product"."translated_languages", "Product"."visibility", "Product"."created_at", "Product"."updated_at", "Product"."flash_sale_id", "Product"."createdAt", "Product"."updatedAt" FROM "products" AS "Product" WHERE "Product"."status" = 'publish' AND "Product"."visibility" = 'visibility_public' ORDER BY "Product"."created_at" DESC LIMIT '30' OFFSET 0) AS "Product" LEFT OUTER JOIN ( "product_categories" AS "categories->ProductCategory" INNER JOIN "categories" AS "categories" ON "categories"."id" = "categories->ProductCategory"."category_id") ON "Product"."id" = "categories->ProductCategory"."product_id" LEFT OUTER JOIN ( "product_tags" AS "tags->ProductTag" INNER JOIN "tags" AS "tags" ON "tags"."id" = "tags->ProductTag"."tag_id") ON "Product"."id" = "tags->ProductTag"."product_id" LEFT OUTER JOIN "types" AS "type" ON "Product"."type_id" = "type"."id" LEFT OUTER JOIN "shops" AS "shop" ON "Product"."shop_id" = "shop"."id" ORDER BY "Product"."created_at" DESC;
Executing (default): SELECT count("Category"."id") AS "count" FROM "categories" AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" WHERE "Category"."parent_id" IS NULL;
Executing (default): SELECT "Category".*, "type"."id" AS "type.id", "type"."name" AS "type.name", "type"."slug" AS "type.slug", "type"."image" AS "type.image", "type"."icon" AS "type.icon", "type"."banners" AS "type.banners", "type"."promotional_sliders" AS "type.promotional_sliders", "type"."settings" AS "type.settings", "type"."language" AS "type.language", "type"."translated_languages" AS "type.translated_languages", "type"."created_at" AS "type.created_at", "type"."updated_at" AS "type.updated_at", "type"."createdAt" AS "type.createdAt", "type"."updatedAt" AS "type.updatedAt", "parent"."id" AS "parent.id", "parent"."name" AS "parent.name", "parent"."slug" AS "parent.slug", "parent"."parent_id" AS "parent.parent_id", "parent"."details" AS "parent.details", "parent"."image" AS "parent.image", "parent"."icon" AS "parent.icon", "parent"."type_id" AS "parent.type_id", "parent"."language" AS "parent.language", "parent"."translated_languages" AS "parent.translated_languages", "parent"."created_at" AS "parent.created_at", "parent"."updated_at" AS "parent.updated_at", "parent"."createdAt" AS "parent.createdAt", "parent"."updatedAt" AS "parent.updatedAt", "children"."id" AS "children.id", "children"."name" AS "children.name", "children"."slug" AS "children.slug", "children"."parent_id" AS "children.parent_id", "children"."details" AS "children.details", "children"."image" AS "children.image", "children"."icon" AS "children.icon", "children"."type_id" AS "children.type_id", "children"."language" AS "children.language", "children"."translated_languages" AS "children.translated_languages", "children"."created_at" AS "children.created_at", "children"."updated_at" AS "children.updated_at", "children"."createdAt" AS "children.createdAt", "children"."updatedAt" AS "children.updatedAt" FROM (SELECT "Category"."id", "Category"."name", "Category"."slug", "Category"."parent_id", "Category"."details", "Category"."image", "Category"."icon", "Category"."type_id", "Category"."language", "Category"."translated_languages", "Category"."created_at", "Category"."updated_at", "Category"."createdAt", "Category"."updatedAt" FROM "categories" AS "Category" WHERE "Category"."parent_id" IS NULL ORDER BY "Category"."created_at" DESC LIMIT '1000' OFFSET 0) AS "Category" LEFT OUTER JOIN "types" AS "type" ON "Category"."type_id" = "type"."id" LEFT OUTER JOIN "categories" AS "parent" ON "Category"."parent_id" = "parent"."id" LEFT OUTER JOIN "categories" AS "children" ON "Category"."id" = "children"."parent_id" ORDER BY "Category"."created_at" DESC;
Executing (default): SELECT count(*) AS "count" FROM "shops" AS "Shop";
Executing (default): SELECT "id", "owner_id", "is_active", "orders_count", "products_count", "name", "slug", "description", "cover_image", "logo", "address", "settings", "distance", "lat", "lng", "created_at", "updated_at", "createdAt", "updatedAt" FROM "shops" AS "Shop" LIMIT '10' OFFSET 0;
