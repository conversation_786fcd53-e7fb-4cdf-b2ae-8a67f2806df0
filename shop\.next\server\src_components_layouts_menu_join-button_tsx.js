"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_layouts_menu_join-button_tsx";
exports.ids = ["src_components_layouts_menu_join-button_tsx"];
exports.modules = {

/***/ "./src/components/layouts/menu/join-button.tsx":
/*!*****************************************************!*\
  !*** ./src/components/layouts/menu/join-button.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JoinButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction JoinButton({ title = \"join-button\", size = \"small\", className = \"font-semibold\" }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const { openModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleJoin() {\n        return openModal(\"LOGIN_VIEW\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        className: className,\n        size: size,\n        onClick: handleJoin,\n        children: t(title)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\join-button.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9sYXlvdXRzL21lbnUvam9pbi1idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ3lCO0FBRXZCO0FBRS9CLFNBQVNHLFdBQVcsRUFDakNDLFFBQVEsYUFBYSxFQUNyQkMsT0FBTyxPQUFPLEVBQ2RDLFlBQVksZUFBZSxFQUN1QjtJQUNsRCxNQUFNLEVBQUVDLENBQUMsRUFBRSxHQUFHTCw0REFBY0EsQ0FBQztJQUM3QixNQUFNLEVBQUVNLFNBQVMsRUFBRSxHQUFHUCxrRkFBY0E7SUFDcEMsU0FBU1E7UUFDUCxPQUFPRCxVQUFVO0lBQ25CO0lBQ0EscUJBQ0UsOERBQUNSLDZEQUFNQTtRQUFDTSxXQUFXQTtRQUFXRCxNQUFNQTtRQUFNSyxTQUFTRDtrQkFDaERGLEVBQUVIOzs7Ozs7QUFHVCIsInNvdXJjZXMiOlsid2VicGFjazovL0BvbmVrYXJ0L3Nob3AvLi9zcmMvY29tcG9uZW50cy9sYXlvdXRzL21lbnUvam9pbi1idXR0b24udHN4PzEwNWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEJ1dHRvbiBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IHVzZU1vZGFsQWN0aW9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL21vZGFsL21vZGFsLmNvbnRleHQnO1xuaW1wb3J0IHsgQnV0dG9uUHJvcHMgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnbmV4dC1pMThuZXh0JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSm9pbkJ1dHRvbih7XG4gIHRpdGxlID0gJ2pvaW4tYnV0dG9uJyxcbiAgc2l6ZSA9ICdzbWFsbCcsXG4gIGNsYXNzTmFtZSA9ICdmb250LXNlbWlib2xkJyxcbn06IFBpY2s8QnV0dG9uUHJvcHMsICd0aXRsZScgfCAnc2l6ZScgfCAnY2xhc3NOYW1lJz4pIHtcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbignY29tbW9uJyk7XG4gIGNvbnN0IHsgb3Blbk1vZGFsIH0gPSB1c2VNb2RhbEFjdGlvbigpO1xuICBmdW5jdGlvbiBoYW5kbGVKb2luKCkge1xuICAgIHJldHVybiBvcGVuTW9kYWwoJ0xPR0lOX1ZJRVcnKTtcbiAgfVxuICByZXR1cm4gKFxuICAgIDxCdXR0b24gY2xhc3NOYW1lPXtjbGFzc05hbWV9IHNpemU9e3NpemV9IG9uQ2xpY2s9e2hhbmRsZUpvaW59PlxuICAgICAge3QodGl0bGUpfVxuICAgIDwvQnV0dG9uPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkJ1dHRvbiIsInVzZU1vZGFsQWN0aW9uIiwidXNlVHJhbnNsYXRpb24iLCJKb2luQnV0dG9uIiwidGl0bGUiLCJzaXplIiwiY2xhc3NOYW1lIiwidCIsIm9wZW5Nb2RhbCIsImhhbmRsZUpvaW4iLCJvbkNsaWNrIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/layouts/menu/join-button.tsx\n");

/***/ })

};
;