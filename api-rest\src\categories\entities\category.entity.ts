import {
  Column,
  Model,
  Table,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  BelongsToMany,
} from 'sequelize-typescript';
import { Product } from 'src/products/entities/product.entity';
import { Type } from 'src/types/entities/type.entity';

@Table({
  tableName: 'categories',
  timestamps: true,
})
export class Category extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  slug: string;

  @ForeignKey(() => Category)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  parent_id?: number;

  @BelongsTo(() => Category, 'parent_id')
  parent?: Category;

  @HasMany(() => Category, 'parent_id')
  children?: Category[];

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  details?: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  image?: any; // Attachment object

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  icon?: string;

  @ForeignKey(() => Type)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  type_id?: number;

  @BelongsTo(() => Type)
  type?: Type;

  @BelongsToMany(() => Product, () => ProductCategory)
  products?: Product[];

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'en',
  })
  language: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  translated_languages: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}

// Junction table for many-to-many relationship between Product and Category
@Table({
  tableName: 'product_categories',
  timestamps: false,
})
export class ProductCategory extends Model {
  @ForeignKey(() => Product)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  product_id: number;

  @ForeignKey(() => Category)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  category_id: number;
}
