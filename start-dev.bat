@echo off
setlocal enabledelayedexpansion

REM oneKart E-Commerce Development Startup Script for Windows
REM This script starts all services in development mode

title oneKart Development Server

echo ================================
echo   oneKart E-Commerce Platform
echo      Development Mode
echo ================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18 or higher.
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed.
    pause
    exit /b 1
)

echo [oneKart] Prerequisites check passed
echo.

REM Install dependencies if --install flag is provided
if "%1"=="--install" (
    echo [oneKart] Installing dependencies...
    
    echo [oneKart] Installing API dependencies...
    cd api-rest
    npm install --legacy-peer-deps
    cd ..
    
    echo [oneKart] Installing Admin dependencies...
    cd admin-rest
    if exist yarn.lock (
        yarn install --frozen-lockfile
    ) else (
        npm install
    )
    cd ..
    
    echo [oneKart] Installing Shop dependencies...
    cd shop
    if exist yarn.lock (
        yarn install --frozen-lockfile
    ) else (
        npm install
    )
    cd ..
    
    echo [SUCCESS] Dependencies installed successfully
    echo.
)

REM Function to kill processes on specific ports
echo [oneKart] Checking for existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :9000') do taskkill /PID %%a /F >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3002') do taskkill /PID %%a /F >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3005') do taskkill /PID %%a /F >nul 2>&1

echo [oneKart] Starting services...
echo.

REM Start API server
echo [oneKart] Starting API server...
cd api-rest
start "oneKart API Server" /min cmd /c "npm run start:dev"
cd ..
echo [oneKart] API server starting in background...
timeout /t 20 /nobreak >nul

REM Start Admin dashboard
echo [oneKart] Starting Admin dashboard...
cd admin-rest
start "oneKart Admin Dashboard" /min cmd /c "npm run dev"
cd ..
echo [oneKart] Admin dashboard starting in background...
timeout /t 25 /nobreak >nul

REM Start Shop frontend
echo [oneKart] Starting Shop frontend...
cd shop
start "oneKart Shop Frontend" /min cmd /c "npx next dev -p 3005"
cd ..
echo [oneKart] Shop frontend starting in background...
timeout /t 25 /nobreak >nul

echo.
echo ================================
echo   oneKart E-Commerce Platform
echo      Development Mode
echo ================================
echo.
echo [SUCCESS] oneKart E-Commerce Platform is running in development mode!
echo.
echo Admin Dashboard: http://localhost:3002
echo Shop Frontend:   http://localhost:3005
echo API Server:      http://localhost:9000/api
echo.
echo Features:
echo   • Purple Nebula Dark Theme
echo   • oneKart Branding
echo   • Hot Reload Enabled
echo   • Development Tools Active
echo.
echo Press any key to open the applications in your browser...
pause >nul

REM Open applications in browser
start http://localhost:3002
start http://localhost:3005

echo.
echo Applications opened in browser.
echo Close this window or press Ctrl+C to stop monitoring.
echo Individual service windows can be closed separately.
echo.
pause
