"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src_components_layouts_owner_index_tsx",{

/***/ "./src/components/layouts/navigation/top-navbar.tsx":
/*!**********************************************************!*\
  !*** ./src/components/layouts/navigation/top-navbar.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isInArray: function() { return /* binding */ isInArray; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_icons_search_icon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/icons/search-icon */ \"./src/components/icons/search-icon.tsx\");\n/* harmony import */ var _components_layouts_navigation_language_switcher__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layouts/navigation/language-switcher */ \"./src/components/layouts/navigation/language-switcher.tsx\");\n/* harmony import */ var _components_layouts_topbar_message_bar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layouts/topbar/message-bar */ \"./src/components/layouts/topbar/message-bar.tsx\");\n/* harmony import */ var _components_layouts_topbar_recent_order_bar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layouts/topbar/recent-order-bar */ \"./src/components/layouts/topbar/recent-order-bar.tsx\");\n/* harmony import */ var _components_layouts_topbar_search_bar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layouts/topbar/search-bar */ \"./src/components/layouts/topbar/search-bar.tsx\");\n/* harmony import */ var _components_layouts_topbar_store_notice_bar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layouts/topbar/store-notice-bar */ \"./src/components/layouts/topbar/store-notice-bar.tsx\");\n/* harmony import */ var _components_layouts_topbar_visit_store__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layouts/topbar/visit-store */ \"./src/components/layouts/topbar/visit-store.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_countdown_timer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/countdown-timer */ \"./src/components/ui/countdown-timer/index.tsx\");\n/* harmony import */ var _components_ui_link_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/link-button */ \"./src/components/ui/link-button.tsx\");\n/* harmony import */ var _components_ui_loader_loader__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/loader/loader */ \"./src/components/ui/loader/loader.tsx\");\n/* harmony import */ var _components_ui_logo__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/logo */ \"./src/components/ui/logo.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _contexts_ui_context__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/contexts/ui.context */ \"./src/contexts/ui.context.tsx\");\n/* harmony import */ var _data_settings__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/data/settings */ \"./src/data/settings.ts\");\n/* harmony import */ var _data_shop__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/data/shop */ \"./src/data/shop.ts\");\n/* harmony import */ var _data_user__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/data/user */ \"./src/data/user.ts\");\n/* harmony import */ var _utils_auth_utils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/utils/auth-utils */ \"./src/utils/auth-utils.ts\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/utils/constants */ \"./src/utils/constants.ts\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var date_fns_isBefore__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! date-fns/isBefore */ \"./node_modules/date-fns/esm/isBefore/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! jotai */ \"./node_modules/jotai/esm/index.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var _barrel_optimize_names_useWindowSize_react_use__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=useWindowSize!=!react-use */ \"__barrel_optimize__?names=useWindowSize!=!./node_modules/react-use/esm/index.js\");\n/* harmony import */ var _authorized_menu__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./authorized-menu */ \"./src/components/layouts/navigation/authorized-menu.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst isInArray = (array, value)=>{\n    return !!(array === null || array === void 0 ? void 0 : array.find((item)=>{\n        return (item === null || item === void 0 ? void 0 : item.getDate()) == (value === null || value === void 0 ? void 0 : value.getDate());\n    }));\n};\nconst Navbar = ()=>{\n    var _settings_options_maintenance, _settings_options, _settings_options_maintenance1, _settings_options1, _settings_options2, _shop_settings_shopMaintenance, _shop_settings, _shop_settings_shopMaintenance1, _shop_settings1, _shop_settings2, _settings_options3, _shop_settings3, _settings_options4, _options_maintenance, _shop_settings4, _shop_settings_shopMaintenance2, _shop_settings5, _options_pushNotification_all, _options_pushNotification, _options_pushNotification_all1, _options_pushNotification1, _options_pushNotification_all2, _options_pushNotification2, _options_pushNotification_all3, _options_pushNotification3, _options_pushNotification_all4, _options_pushNotification4, _options_pushNotification_all5, _options_pushNotification5;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_23__.useTranslation)();\n    const { toggleSidebar } = (0,_contexts_ui_context__WEBPACK_IMPORTED_MODULE_16__.useUI)();\n    const { permissions } = (0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_20__.getAuthCredentials)();\n    const { enableMultiLang } = _config__WEBPACK_IMPORTED_MODULE_14__.Config;\n    const { locale, query } = (0,next_router__WEBPACK_IMPORTED_MODULE_24__.useRouter)();\n    const { data } = (0,_data_user__WEBPACK_IMPORTED_MODULE_19__.useMeQuery)();\n    const { openModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_13__.useModalAction)();\n    const [searchModal, setSearchModal] = (0,jotai__WEBPACK_IMPORTED_MODULE_27__.useAtom)(_utils_constants__WEBPACK_IMPORTED_MODULE_21__.searchModalInitialValues);\n    const [miniSidebar, setMiniSidebar] = (0,jotai__WEBPACK_IMPORTED_MODULE_27__.useAtom)(_utils_constants__WEBPACK_IMPORTED_MODULE_21__.miniSidebarInitialValue);\n    const [isMaintenanceMode, setUnderMaintenance] = (0,jotai__WEBPACK_IMPORTED_MODULE_27__.useAtom)(_utils_constants__WEBPACK_IMPORTED_MODULE_21__.checkIsMaintenanceModeComing);\n    const [isMaintenanceModeStart, setUnderMaintenanceStart] = (0,jotai__WEBPACK_IMPORTED_MODULE_27__.useAtom)(_utils_constants__WEBPACK_IMPORTED_MODULE_21__.checkIsMaintenanceModeStart);\n    const { width } = (0,_barrel_optimize_names_useWindowSize_react_use__WEBPACK_IMPORTED_MODULE_28__.useWindowSize)();\n    const { settings, loading } = (0,_data_settings__WEBPACK_IMPORTED_MODULE_17__.useSettingsQuery)({\n        language: locale\n    });\n    const { data: shop, isLoading: shopLoading, error } = (0,_data_shop__WEBPACK_IMPORTED_MODULE_18__.useShopQuery)({\n        slug: query === null || query === void 0 ? void 0 : query.shop\n    }, {\n        enabled: Boolean(query === null || query === void 0 ? void 0 : query.shop)\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_25__.useEffect)(()=>{\n        var _settings_options_maintenance, _settings_options, _settings_options_maintenance1, _settings_options1, _settings_options2;\n        if ((settings === null || settings === void 0 ? void 0 : (_settings_options = settings.options) === null || _settings_options === void 0 ? void 0 : (_settings_options_maintenance = _settings_options.maintenance) === null || _settings_options_maintenance === void 0 ? void 0 : _settings_options_maintenance.start) && (settings === null || settings === void 0 ? void 0 : (_settings_options1 = settings.options) === null || _settings_options1 === void 0 ? void 0 : (_settings_options_maintenance1 = _settings_options1.maintenance) === null || _settings_options_maintenance1 === void 0 ? void 0 : _settings_options_maintenance1.until) && (settings === null || settings === void 0 ? void 0 : (_settings_options2 = settings.options) === null || _settings_options2 === void 0 ? void 0 : _settings_options2.isUnderMaintenance)) {\n            var _settings_options_maintenance2, _settings_options3, _settings_options_maintenance3, _settings_options4, _settings_options_maintenance4, _settings_options5, _settings_options6, _settings_options7;\n            const beforeDay = (0,date_fns_isBefore__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(new Date(), new Date(settings === null || settings === void 0 ? void 0 : (_settings_options3 = settings.options) === null || _settings_options3 === void 0 ? void 0 : (_settings_options_maintenance2 = _settings_options3.maintenance) === null || _settings_options_maintenance2 === void 0 ? void 0 : _settings_options_maintenance2.start));\n            // Calculate maintenance start time\n            const maintenanceStartTime = new Date(settings === null || settings === void 0 ? void 0 : (_settings_options4 = settings.options) === null || _settings_options4 === void 0 ? void 0 : (_settings_options_maintenance3 = _settings_options4.maintenance) === null || _settings_options_maintenance3 === void 0 ? void 0 : _settings_options_maintenance3.start);\n            const maintenanceEndTime = new Date(settings === null || settings === void 0 ? void 0 : (_settings_options5 = settings.options) === null || _settings_options5 === void 0 ? void 0 : (_settings_options_maintenance4 = _settings_options5.maintenance) === null || _settings_options_maintenance4 === void 0 ? void 0 : _settings_options_maintenance4.until);\n            maintenanceStartTime.setMinutes(maintenanceStartTime.getMinutes());\n            // Check if the current time has passed the maintenance start time\n            const currentTime = new Date();\n            const checkIsMaintenanceStart = currentTime >= maintenanceStartTime && currentTime < maintenanceEndTime && (settings === null || settings === void 0 ? void 0 : (_settings_options6 = settings.options) === null || _settings_options6 === void 0 ? void 0 : _settings_options6.isUnderMaintenance);\n            const checkIsMaintenance = beforeDay && (settings === null || settings === void 0 ? void 0 : (_settings_options7 = settings.options) === null || _settings_options7 === void 0 ? void 0 : _settings_options7.isUnderMaintenance);\n            setUnderMaintenance(checkIsMaintenance);\n            setUnderMaintenanceStart(checkIsMaintenanceStart);\n        }\n    }, [\n        settings === null || settings === void 0 ? void 0 : (_settings_options = settings.options) === null || _settings_options === void 0 ? void 0 : (_settings_options_maintenance = _settings_options.maintenance) === null || _settings_options_maintenance === void 0 ? void 0 : _settings_options_maintenance.start,\n        settings === null || settings === void 0 ? void 0 : (_settings_options1 = settings.options) === null || _settings_options1 === void 0 ? void 0 : (_settings_options_maintenance1 = _settings_options1.maintenance) === null || _settings_options_maintenance1 === void 0 ? void 0 : _settings_options_maintenance1.until,\n        settings === null || settings === void 0 ? void 0 : (_settings_options2 = settings.options) === null || _settings_options2 === void 0 ? void 0 : _settings_options2.isUnderMaintenance\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_25__.useEffect)(()=>{\n        var _shop_settings_shopMaintenance, _shop_settings, _shop_settings_shopMaintenance1, _shop_settings1, _shop_settings2;\n        if ((query === null || query === void 0 ? void 0 : query.shop) && (shop === null || shop === void 0 ? void 0 : (_shop_settings = shop.settings) === null || _shop_settings === void 0 ? void 0 : (_shop_settings_shopMaintenance = _shop_settings.shopMaintenance) === null || _shop_settings_shopMaintenance === void 0 ? void 0 : _shop_settings_shopMaintenance.start) && (shop === null || shop === void 0 ? void 0 : (_shop_settings1 = shop.settings) === null || _shop_settings1 === void 0 ? void 0 : (_shop_settings_shopMaintenance1 = _shop_settings1.shopMaintenance) === null || _shop_settings_shopMaintenance1 === void 0 ? void 0 : _shop_settings_shopMaintenance1.until) && (shop === null || shop === void 0 ? void 0 : (_shop_settings2 = shop.settings) === null || _shop_settings2 === void 0 ? void 0 : _shop_settings2.isShopUnderMaintenance)) {\n            var _shop_settings_shopMaintenance2, _shop_settings3, _shop_settings_shopMaintenance3, _shop_settings4, _shop_settings_shopMaintenance4, _shop_settings5, _shop_settings6, _shop_settings7;\n            const beforeDay = (0,date_fns_isBefore__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(new Date(), new Date(shop === null || shop === void 0 ? void 0 : (_shop_settings3 = shop.settings) === null || _shop_settings3 === void 0 ? void 0 : (_shop_settings_shopMaintenance2 = _shop_settings3.shopMaintenance) === null || _shop_settings_shopMaintenance2 === void 0 ? void 0 : _shop_settings_shopMaintenance2.start));\n            // Calculate maintenance start time\n            const maintenanceStartTime = new Date(shop === null || shop === void 0 ? void 0 : (_shop_settings4 = shop.settings) === null || _shop_settings4 === void 0 ? void 0 : (_shop_settings_shopMaintenance3 = _shop_settings4.shopMaintenance) === null || _shop_settings_shopMaintenance3 === void 0 ? void 0 : _shop_settings_shopMaintenance3.start);\n            const maintenanceEndTime = new Date(shop === null || shop === void 0 ? void 0 : (_shop_settings5 = shop.settings) === null || _shop_settings5 === void 0 ? void 0 : (_shop_settings_shopMaintenance4 = _shop_settings5.shopMaintenance) === null || _shop_settings_shopMaintenance4 === void 0 ? void 0 : _shop_settings_shopMaintenance4.until);\n            maintenanceStartTime.setMinutes(maintenanceStartTime.getMinutes());\n            // Check if the current time has passed the maintenance start time\n            const currentTime = new Date();\n            const checkIsMaintenanceStart = currentTime >= maintenanceStartTime && currentTime < maintenanceEndTime && (shop === null || shop === void 0 ? void 0 : (_shop_settings6 = shop.settings) === null || _shop_settings6 === void 0 ? void 0 : _shop_settings6.isShopUnderMaintenance);\n            const checkIsMaintenance = beforeDay && (shop === null || shop === void 0 ? void 0 : (_shop_settings7 = shop.settings) === null || _shop_settings7 === void 0 ? void 0 : _shop_settings7.isShopUnderMaintenance);\n            setUnderMaintenance(checkIsMaintenance);\n            setUnderMaintenanceStart(checkIsMaintenanceStart);\n        }\n    }, [\n        query === null || query === void 0 ? void 0 : query.shop,\n        shop === null || shop === void 0 ? void 0 : (_shop_settings = shop.settings) === null || _shop_settings === void 0 ? void 0 : (_shop_settings_shopMaintenance = _shop_settings.shopMaintenance) === null || _shop_settings_shopMaintenance === void 0 ? void 0 : _shop_settings_shopMaintenance.start,\n        shop === null || shop === void 0 ? void 0 : (_shop_settings1 = shop.settings) === null || _shop_settings1 === void 0 ? void 0 : (_shop_settings_shopMaintenance1 = _shop_settings1.shopMaintenance) === null || _shop_settings_shopMaintenance1 === void 0 ? void 0 : _shop_settings_shopMaintenance1.until,\n        shop === null || shop === void 0 ? void 0 : (_shop_settings2 = shop.settings) === null || _shop_settings2 === void 0 ? void 0 : _shop_settings2.isShopUnderMaintenance\n    ]);\n    if (loading || shopLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loader_loader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            showText: false\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n            lineNumber: 154,\n            columnNumber: 12\n        }, undefined);\n    }\n    const { options } = settings;\n    function handleClick() {\n        openModal(\"SEARCH_VIEW\");\n        setSearchModal(true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 z-40 w-full bg-primary-bg shadow\",\n        children: [\n            width >= _utils_constants__WEBPACK_IMPORTED_MODULE_21__.RESPONSIVE_WIDTH && isMaintenanceMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: (settings === null || settings === void 0 ? void 0 : (_settings_options3 = settings.options) === null || _settings_options3 === void 0 ? void 0 : _settings_options3.isUnderMaintenance) && \"Site \".concat(t(\"text-maintenance-mode-title\")) || (shop === null || shop === void 0 ? void 0 : (_shop_settings3 = shop.settings) === null || _shop_settings3 === void 0 ? void 0 : _shop_settings3.isShopUnderMaintenance) && \"\".concat(shop === null || shop === void 0 ? void 0 : shop.name, \" \").concat(t(\"text-maintenance-mode-title\")),\n                variant: \"info\",\n                className: \"sticky top-0 left-0 z-50\",\n                childClassName: \"flex justify-center items-center w-full gap-4 font-bold\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_countdown_timer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    date: (settings === null || settings === void 0 ? void 0 : (_settings_options4 = settings.options) === null || _settings_options4 === void 0 ? void 0 : _settings_options4.isUnderMaintenance) && new Date(options === null || options === void 0 ? void 0 : (_options_maintenance = options.maintenance) === null || _options_maintenance === void 0 ? void 0 : _options_maintenance.start) || (shop === null || shop === void 0 ? void 0 : (_shop_settings4 = shop.settings) === null || _shop_settings4 === void 0 ? void 0 : _shop_settings4.isShopUnderMaintenance) && new Date(shop === null || shop === void 0 ? void 0 : (_shop_settings5 = shop.settings) === null || _shop_settings5 === void 0 ? void 0 : (_shop_settings_shopMaintenance2 = _shop_settings5.shopMaintenance) === null || _shop_settings_shopMaintenance2 === void 0 ? void 0 : _shop_settings_shopMaintenance2.start),\n                    className: \"text-blue-600 [&>p]:bg-blue-200 [&>p]:p-2 [&>p]:text-xs [&>p]:text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                lineNumber: 167,\n                columnNumber: 9\n            }, undefined) : \"\",\n            width >= _utils_constants__WEBPACK_IMPORTED_MODULE_21__.RESPONSIVE_WIDTH && isMaintenanceModeStart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: t(\"text-maintenance-mode-start-title\"),\n                className: \"py-[1.375rem]\",\n                childClassName: \"text-center w-full font-bold\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                lineNumber: 192,\n                columnNumber: 9\n            }, undefined) : \"\",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex items-center px-5 md:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex w-full flex-1 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_30__.motion.button, {\n                                    whileTap: {\n                                        scale: 0.88\n                                    },\n                                    onClick: toggleSidebar,\n                                    className: \"group flex h-5 w-5 shrink-0 cursor-pointer flex-col justify-center space-y-1 me-4 focus:text-accent focus:outline-none lg:hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: classnames__WEBPACK_IMPORTED_MODULE_22___default()(\"h-0.5 rounded-full bg-gray-600 transition-[width] group-hover:bg-accent\", miniSidebar ? \"w-full\" : \"w-2/4\")\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"h-0.5 w-full rounded-full bg-gray-600 group-hover:bg-accent\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"h-0.5 w-3/4 rounded-full bg-gray-600 transition-[width] group-hover:bg-accent\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_22___default()(\"flex h-16 shrink-0 transition-[width] duration-300 me-4 lg:h-[76px] lg:border-solid lg:border-gray-200/80 lg:me-8 lg:border-e\", miniSidebar ? \"lg:w-[65px]\" : \"lg:w-[257px]\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_logo__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"group hidden h-5 w-5 shrink-0 cursor-pointer flex-col justify-center space-y-1 me-6 lg:flex\",\n                                    onClick: ()=>setMiniSidebar(!miniSidebar),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: classnames__WEBPACK_IMPORTED_MODULE_22___default()(\"h-0.5 rounded-full bg-gray-600 transition-[width] group-hover:bg-accent\", miniSidebar ? \"w-full\" : \"w-2/4\")\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"h-0.5 w-full rounded-full bg-gray-600 group-hover:bg-accent\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: classnames__WEBPACK_IMPORTED_MODULE_22___default()(\"h-0.5 rounded-full bg-gray-600 transition-[width] group-hover:bg-accent\", miniSidebar ? \"w-full\" : \"w-3/4\")\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative ml-auto mr-1.5 flex h-9 w-9 shrink-0 cursor-pointer items-center justify-center rounded-full border border-gray-200 bg-gray-50 py-4 text-gray-600 hover:border-transparent hover:border-gray-200 hover:bg-white hover:text-accent sm:mr-6 lg:hidden xl:hidden\",\n                            onClick: handleClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_search_icon__WEBPACK_IMPORTED_MODULE_1__.SearchIcon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative hidden w-full max-w-[710px] py-4 me-6 lg:block 2xl:me-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_topbar_search_bar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex shrink-0 grow-0 basis-auto items-center\",\n                            children: (0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_20__.hasAccess)(_utils_auth_utils__WEBPACK_IMPORTED_MODULE_20__.adminAndOwnerOnly, permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden border-gray-200/80 px-6 py-5 border-e 2xl:block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link_button__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            href: _config_routes__WEBPACK_IMPORTED_MODULE_15__.Routes.shop.create,\n                                            size: \"small\",\n                                            className: \"px-3.5\",\n                                            children: t(\"common:text-create-shop\")\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden px-6 py-5 2xl:block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_topbar_visit_store__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    (options === null || options === void 0 ? void 0 : (_options_pushNotification = options.pushNotification) === null || _options_pushNotification === void 0 ? void 0 : (_options_pushNotification_all = _options_pushNotification.all) === null || _options_pushNotification_all === void 0 ? void 0 : _options_pushNotification_all.order) || (options === null || options === void 0 ? void 0 : (_options_pushNotification1 = options.pushNotification) === null || _options_pushNotification1 === void 0 ? void 0 : (_options_pushNotification_all1 = _options_pushNotification1.all) === null || _options_pushNotification_all1 === void 0 ? void 0 : _options_pushNotification_all1.message) || (options === null || options === void 0 ? void 0 : (_options_pushNotification2 = options.pushNotification) === null || _options_pushNotification2 === void 0 ? void 0 : (_options_pushNotification_all2 = _options_pushNotification2.all) === null || _options_pushNotification_all2 === void 0 ? void 0 : _options_pushNotification_all2.storeNotice) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 px-0.5 py-3 sm:relative sm:border-gray-200/80 sm:py-3.5 sm:px-6 sm:border-s lg:py-5\",\n                                        children: [\n                                            (options === null || options === void 0 ? void 0 : (_options_pushNotification3 = options.pushNotification) === null || _options_pushNotification3 === void 0 ? void 0 : (_options_pushNotification_all3 = _options_pushNotification3.all) === null || _options_pushNotification_all3 === void 0 ? void 0 : _options_pushNotification_all3.order) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_topbar_recent_order_bar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                user: data\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 23\n                                            }, undefined) : \"\",\n                                            (options === null || options === void 0 ? void 0 : (_options_pushNotification4 = options.pushNotification) === null || _options_pushNotification4 === void 0 ? void 0 : (_options_pushNotification_all4 = _options_pushNotification4.all) === null || _options_pushNotification_all4 === void 0 ? void 0 : _options_pushNotification_all4.message) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_topbar_message_bar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                user: data\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 23\n                                            }, undefined) : \"\",\n                                            !(0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_20__.hasAccess)(_utils_auth_utils__WEBPACK_IMPORTED_MODULE_20__.adminOnly, permissions) ? (options === null || options === void 0 ? void 0 : (_options_pushNotification5 = options.pushNotification) === null || _options_pushNotification5 === void 0 ? void 0 : (_options_pushNotification_all5 = _options_pushNotification5.all) === null || _options_pushNotification_all5 === void 0 ? void 0 : _options_pushNotification_all5.storeNotice) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_topbar_store_notice_bar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                user: data\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 25\n                                            }, undefined) : \"\" : null\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, undefined) : null\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined),\n                        enableMultiLang ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_navigation_language_switcher__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 30\n                        }, undefined) : null,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_authorized_menu__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\top-navbar.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"xfoLDd8MQWXpxDETtvHIRwsu0n4=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_23__.useTranslation,\n        _contexts_ui_context__WEBPACK_IMPORTED_MODULE_16__.useUI,\n        next_router__WEBPACK_IMPORTED_MODULE_24__.useRouter,\n        _data_user__WEBPACK_IMPORTED_MODULE_19__.useMeQuery,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_13__.useModalAction,\n        jotai__WEBPACK_IMPORTED_MODULE_27__.useAtom,\n        jotai__WEBPACK_IMPORTED_MODULE_27__.useAtom,\n        jotai__WEBPACK_IMPORTED_MODULE_27__.useAtom,\n        jotai__WEBPACK_IMPORTED_MODULE_27__.useAtom,\n        _barrel_optimize_names_useWindowSize_react_use__WEBPACK_IMPORTED_MODULE_28__.useWindowSize,\n        _data_settings__WEBPACK_IMPORTED_MODULE_17__.useSettingsQuery,\n        _data_shop__WEBPACK_IMPORTED_MODULE_18__.useShopQuery\n    ];\n});\n_c = Navbar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layouts/navigation/top-navbar.tsx\n"));

/***/ })

});