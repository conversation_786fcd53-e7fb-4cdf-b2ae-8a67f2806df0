"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_ui_search_search_tsx"],{

/***/ "./src/components/ui/search/search.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/search/search.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_search_search_box__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/search/search-box */ \"./src/components/ui/search/search-box.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _search_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./search.context */ \"./src/components/ui/search/search.context.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst Search = (param)=>{\n    let { label, variant, className, inputClassName, ...props } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { searchTerm, updateSearchTerm } = (0,_search_context__WEBPACK_IMPORTED_MODULE_4__.useSearch)();\n    const handleOnChange = (e)=>{\n        const { value } = e.target;\n        updateSearchTerm(value);\n    };\n    const onSearch = (e)=>{\n        e.preventDefault();\n        if (!searchTerm) return;\n        const { pathname, query } = router;\n        router.push({\n            pathname,\n            query: {\n                ...query,\n                text: searchTerm\n            }\n        }, undefined, {\n            scroll: false\n        });\n    };\n    function clearSearch() {\n        updateSearchTerm(\"\");\n        const { pathname, query } = router;\n        const { text, ...rest } = query;\n        if (text) {\n            router.push({\n                pathname,\n                query: {\n                    ...rest\n                }\n            }, undefined, {\n                scroll: false\n            });\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_search_search_box__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        label: label,\n        onSubmit: onSearch,\n        onClearSearch: clearSearch,\n        onChange: handleOnChange,\n        value: searchTerm,\n        name: \"search\",\n        placeholder: t(\"common:text-search-placeholder\"),\n        variant: variant,\n        className: className,\n        inputClassName: inputClassName,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\search\\\\search.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Search, \"hIB3C7FFe5bsgBoeRycsXVgYE4E=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _search_context__WEBPACK_IMPORTED_MODULE_4__.useSearch\n    ];\n});\n_c = Search;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Search);\nvar _c;\n$RefreshReg$(_c, \"Search\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/search/search.tsx\n"));

/***/ })

}]);