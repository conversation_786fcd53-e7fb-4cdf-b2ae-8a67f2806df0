"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_manufacturer_manufacturer-delete-view_tsx";
exports.ids = ["src_components_manufacturer_manufacturer-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/manufacturer/manufacturer-delete-view.tsx":
/*!******************************************************************!*\
  !*** ./src/components/manufacturer/manufacturer-delete-view.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_manufacturer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/manufacturer */ \"./src/data/manufacturer.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_manufacturer__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_manufacturer__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst ManufacturerDeleteView = ()=>{\n    const { mutate: deleteManufacturerMutation, isLoading: loading } = (0,_data_manufacturer__WEBPACK_IMPORTED_MODULE_3__.useDeleteManufacturerMutation)();\n    const { data: modalData } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteManufacturerMutation({\n            id: modalData\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\manufacturer\\\\manufacturer-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ManufacturerDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/manufacturer/manufacturer-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/manufacturer.ts":
/*!*****************************************!*\
  !*** ./src/data/client/manufacturer.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   manufacturerClient: () => (/* binding */ manufacturerClient)\n/* harmony export */ });\n/* harmony import */ var _data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/client/curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst manufacturerClient = {\n    ...(0,_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__.crudFactory)(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.MANUFACTURERS),\n    paginated: ({ name, shop_id, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.MANUFACTURERS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name,\n                shop_id\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/manufacturer.ts\n");

/***/ }),

/***/ "./src/data/manufacturer.ts":
/*!**********************************!*\
  !*** ./src/data/manufacturer.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateManufacturerMutation: () => (/* binding */ useCreateManufacturerMutation),\n/* harmony export */   useDeleteManufacturerMutation: () => (/* binding */ useDeleteManufacturerMutation),\n/* harmony export */   useManufacturerQuery: () => (/* binding */ useManufacturerQuery),\n/* harmony export */   useManufacturersQuery: () => (/* binding */ useManufacturersQuery),\n/* harmony export */   useUpdateManufacturerMutation: () => (/* binding */ useUpdateManufacturerMutation),\n/* harmony export */   useUpdateManufacturerMutationInList: () => (/* binding */ useUpdateManufacturerMutationInList)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/data/client/manufacturer */ \"./src/data/client/manufacturer.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst useCreateManufacturerMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.manufacturer.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.manufacturer.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS);\n        }\n    });\n};\nconst useDeleteManufacturerMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS);\n        }\n    });\n};\nconst useUpdateManufacturerMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.manufacturer.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.manufacturer.list;\n            await router.push(`${generateRedirectUrl}/${data?.slug}/edit`, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // onSuccess: () => {\n        //   toast.success(t('common:successfully-updated'));\n        // },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS);\n        }\n    });\n};\nconst useUpdateManufacturerMutationInList = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.update, {\n        onSuccess: async ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS);\n        }\n    });\n};\nconst useManufacturerQuery = ({ slug, language })=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS,\n        {\n            slug,\n            language\n        }\n    ], ()=>_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.get({\n            slug,\n            language\n        }));\n    return {\n        manufacturer: data,\n        error,\n        loading: isLoading\n    };\n};\nconst useManufacturersQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        manufacturers: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/manufacturer.ts\n");

/***/ })

};
;