"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShopsService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const shop_entity_1 = require("./entities/shop.entity");
const paginate_1 = require("../common/pagination/paginate");
let ShopsService = class ShopsService {
    constructor(shopModel) {
        this.shopModel = shopModel;
    }
    async create(createShopDto) {
        return this.shopModel.create(createShopDto);
    }
    async getShops({ search, limit, page }) {
        if (!page)
            page = 1;
        const offset = (page - 1) * limit;
        const { count, rows: data } = await this.shopModel.findAndCountAll({
            limit,
            offset,
        });
        const url = `/shops?search=${search}&limit=${limit}`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
    async getNewShops({ search, limit, page, }) {
        if (!page)
            page = 1;
        const offset = (page - 1) * limit;
        const { count, rows: data } = await this.shopModel.findAndCountAll({
            where: { is_active: false },
            limit,
            offset,
        });
        const url = `/new-shops?search=${search}&limit=${limit}`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
    getStaffs({ shop_id, limit, page }) {
        var _a, _b;
        const startIndex = (page - 1) * limit;
        const endIndex = page * limit;
        let staffs = [];
        if (shop_id) {
            staffs = (_b = (_a = this.shops.find((p) => p.id === Number(shop_id))) === null || _a === void 0 ? void 0 : _a.staffs) !== null && _b !== void 0 ? _b : [];
        }
        const results = staffs === null || staffs === void 0 ? void 0 : staffs.slice(startIndex, endIndex);
        const url = `/staffs?limit=${limit}`;
        return Object.assign({ data: results }, (0, paginate_1.paginate)(staffs === null || staffs === void 0 ? void 0 : staffs.length, page, limit, results === null || results === void 0 ? void 0 : results.length, url));
    }
    async getShop(slug) {
        return this.shopModel.findOne({ where: { slug } });
    }
    getNearByShop(lat, lng) {
        return nearShops;
    }
    async update(id, updateShopDto) {
        await this.shopModel.update(updateShopDto, { where: { id } });
        return this.shopModel.findByPk(id);
    }
    approve(id) {
        return `This action removes a #${id} shop`;
    }
    remove(id) {
        return `This action removes a #${id} shop`;
    }
    disapproveShop(id) {
        const shop = this.shops.find((s) => s.id === Number(id));
        shop.is_active = false;
        return shop;
    }
    approveShop(id) {
        const shop = this.shops.find((s) => s.id === Number(id));
        shop.is_active = true;
        return shop;
    }
};
ShopsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(shop_entity_1.Shop)),
    __metadata("design:paramtypes", [Object])
], ShopsService);
exports.ShopsService = ShopsService;
//# sourceMappingURL=shops.service.js.map