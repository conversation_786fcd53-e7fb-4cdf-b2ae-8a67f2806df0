"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_reviews_review-delete-view_tsx"],{

/***/ "./src/components/reviews/review-delete-view.tsx":
/*!*******************************************************!*\
  !*** ./src/components/reviews/review-delete-view.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_review__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/review */ \"./src/data/review.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst ReviewDeleteView = ()=>{\n    _s();\n    const { mutate: deleteReview, isLoading: loading } = (0,_data_review__WEBPACK_IMPORTED_MODULE_3__.useDeleteReviewMutation)();\n    const { data: modalData } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteReview({\n            id: modalData\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ReviewDeleteView, \"DaVIA2Dz0nmMfHHjLtsDZt3FhZU=\", false, function() {\n    return [\n        _data_review__WEBPACK_IMPORTED_MODULE_3__.useDeleteReviewMutation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction\n    ];\n});\n_c = ReviewDeleteView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ReviewDeleteView);\nvar _c;\n$RefreshReg$(_c, \"ReviewDeleteView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/reviews/review-delete-view.tsx\n"));

/***/ }),

/***/ "./src/data/client/review.ts":
/*!***********************************!*\
  !*** ./src/data/client/review.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reviewClient: function() { return /* binding */ reviewClient; }\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\n\n\n\nconst reviewClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REVIEWS),\n    reportAbuse: (data)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.ABUSIVE_REPORTS, data);\n    },\n    decline: (data)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.ABUSIVE_REPORTS_DECLINE, data);\n    },\n    get (param) {\n        let { id } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(\"\".concat(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REVIEWS, \"/\").concat(id), {\n            with: \"abusive_reports.user;product;user\"\n        });\n    },\n    paginated: (param)=>{\n        let { type, shop_id, ...params } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REVIEWS, {\n            searchJoin: \"and\",\n            with: \"product;user\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                shop_id\n            })\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/review.ts\n"));

/***/ }),

/***/ "./src/data/review.ts":
/*!****************************!*\
  !*** ./src/data/review.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAbuseReportMutation: function() { return /* binding */ useAbuseReportMutation; },\n/* harmony export */   useDeclineReviewMutation: function() { return /* binding */ useDeclineReviewMutation; },\n/* harmony export */   useDeleteReviewMutation: function() { return /* binding */ useDeleteReviewMutation; },\n/* harmony export */   useReviewQuery: function() { return /* binding */ useReviewQuery; },\n/* harmony export */   useReviewsQuery: function() { return /* binding */ useReviewsQuery; }\n/* harmony export */ });\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_client_review__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/client/review */ \"./src/data/client/review.ts\");\n\n\n\n\n\n\n\nconst useAbuseReportMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__.useModalAction)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.reportAbuse, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"text-abuse-report-submitted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.refetchQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS);\n            closeModal();\n        }\n    });\n};\nconst useDeclineReviewMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.decline, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"successfully-decline\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.refetchQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS);\n        }\n    });\n};\nconst useDeleteReviewMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS);\n        }\n    });\n};\nconst useReviewQuery = (id)=>{\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS,\n        id\n    ], ()=>_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.get({\n            id\n        }));\n};\nconst useReviewsQuery = function(params) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS,\n        params\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.paginated(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        keepPreviousData: true,\n        ...options\n    });\n    var _data_data;\n    return {\n        reviews: (_data_data = data === null || data === void 0 ? void 0 : data.data) !== null && _data_data !== void 0 ? _data_data : [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_3__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/review.ts\n"));

/***/ })

}]);