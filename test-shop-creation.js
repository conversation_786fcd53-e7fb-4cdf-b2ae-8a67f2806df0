const http = require('http');

// Test shop creation
const shopData = JSON.stringify({
  name: 'Test Shop',
  description: 'A test shop for oneKart',
  slug: 'test-shop',
  is_active: true,
  address: {
    street_address: '123 Test Street',
    city: 'Test City',
    state: 'Test State',
    zip: '12345',
    country: 'Test Country'
  },
  settings: {
    contact: '<EMAIL>',
    website: 'https://testshop.com'
  }
});

const options = {
  hostname: 'localhost',
  port: 9000,
  path: '/api/shops',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': shopData.length
  }
};

console.log('Testing shop creation...');
console.log('Request data:', shopData);

const req = http.request(options, (res) => {
  console.log(`\nStatus: ${res.statusCode}`);
  console.log(`Headers: ${JSON.stringify(res.headers, null, 2)}`);
  
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });
  
  res.on('end', () => {
    console.log('\nResponse body:', body);
    
    if (res.statusCode >= 400) {
      console.log('\n❌ Shop creation failed!');
      try {
        const errorData = JSON.parse(body);
        console.log('Error details:', JSON.stringify(errorData, null, 2));
      } catch (e) {
        console.log('Raw error response:', body);
      }
    } else {
      console.log('\n✅ Shop creation successful!');
    }
  });
});

req.on('error', (e) => {
  console.error(`❌ Request error: ${e.message}`);
});

req.write(shopData);
req.end();
