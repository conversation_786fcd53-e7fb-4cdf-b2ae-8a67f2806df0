"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/facebook":
/*!***********************************************!*\
  !*** external "next-auth/providers/facebook" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/facebook");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].ts */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/config/get-env.ts":
/*!*******************************!*\
  !*** ./src/config/get-env.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnv: () => (/* binding */ getEnv),\n/* harmony export */   getEnvOptional: () => (/* binding */ getEnvOptional)\n/* harmony export */ });\nfunction getEnv(name) {\n    const val = process.env[name];\n    if (!val) {\n        throw new Error(`Cannot find environmental variable: ${name}`);\n    }\n    return val;\n}\nfunction getEnvOptional(name, defaultValue = \"\") {\n    const val = process.env[name];\n    return val || defaultValue;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/config/get-env.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _config_get_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/get-env */ \"(api)/./src/config/get-env.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_providers_facebook__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/facebook */ \"next-auth/providers/facebook\");\n/* harmony import */ var next_auth_providers_facebook__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_facebook__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// For more information on each option (and a full list of options) go to\n// https://next-auth.js.org/configuration/options\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_1___default()({\n    // https://next-auth.js.org/configuration/providers\n    providers: [\n        // Only enable OAuth providers if credentials are properly configured\n        ...(0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"GOOGLE_CLIENT_ID\") && (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"GOOGLE_CLIENT_SECRET\") && (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"GOOGLE_CLIENT_ID\") !== \"google_client_id_placeholder\" ? [\n            next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3___default()({\n                clientId: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"GOOGLE_CLIENT_ID\"),\n                clientSecret: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"GOOGLE_CLIENT_SECRET\")\n            })\n        ] : [],\n        ...(0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"FACEBOOK_CLIENT_ID\") && (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"FACEBOOK_CLIENT_SECRET\") && (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"FACEBOOK_CLIENT_ID\") !== \"facebook_client_id_placeholder\" ? [\n            next_auth_providers_facebook__WEBPACK_IMPORTED_MODULE_2___default()({\n                clientId: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"FACEBOOK_CLIENT_ID\"),\n                clientSecret: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"FACEBOOK_CLIENT_SECRET\")\n            })\n        ] : []\n    ],\n    // The secret should be set to a reasonably long random string.\n    // It is used to sign cookies and to sign and encrypt JSON Web Tokens, unless\n    // a separate secret is defined explicitly for encrypting the JWT.\n    secret: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"SECRET\"),\n    session: {\n        // Use JSON Web Tokens for session instead of database sessions.\n        // This option can be used with or without a database for users/accounts.\n        // Note: `jwt` is automatically set to `true` if no database is specified.\n        strategy: \"jwt\"\n    },\n    // JSON Web tokens are only used for sessions if the `jwt: true` session\n    // option is set - or by default if no database is specified.\n    // https://next-auth.js.org/configuration/options#jwt\n    jwt: {\n    },\n    // You can define custom pages to override the built-in ones. These will be regular Next.js pages\n    // so ensure that they are placed outside of the '/api' folder, e.g. signIn: '/auth/mycustom-signin'\n    // The Routes shown here are the default URLs that will be used when a custom\n    // pages is not specified for that route.\n    // https://next-auth.js.org/configuration/pages\n    pages: {\n    },\n    // Callbacks are asynchronous functions you can use to control what happens\n    // when an action is performed.\n    // https://next-auth.js.org/configuration/callbacks\n    callbacks: {\n        // async signIn({ account, profile, user}) {\n        // \tif (account.provider === \"google\") {\n        //     return profile.email_verified && profile?.email?.endsWith(\"@gmail.com\")\n        //   }\n        //   return true // Return true to allow sign in\n        // },\n        async jwt ({ token, account }) {\n            if (account) {\n                const { access_token, provider } = account;\n                token.provider = provider;\n                // reform the `token` object from the access token we appended to the `user` object\n                token.access_token = access_token;\n            }\n            return token;\n        },\n        async session ({ session, token, user }) {\n            const { access_token, provider } = token;\n            //@ts-ignore\n            session.provider = provider;\n            //@ts-ignore\n            session.access_token = access_token;\n            return session;\n        }\n    },\n    // Events are useful for logging\n    // https://next-auth.js.org/configuration/events\n    events: {}\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();