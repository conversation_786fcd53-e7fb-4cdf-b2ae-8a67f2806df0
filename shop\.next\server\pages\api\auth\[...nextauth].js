"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].ts */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/config/get-env.ts":
/*!*******************************!*\
  !*** ./src/config/get-env.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnv: () => (/* binding */ getEnv)\n/* harmony export */ });\nfunction getEnv(name) {\n    const val = process.env[name];\n    if (!val) {\n        throw new Error(`Cannot find environmental variable: ${name}`);\n    }\n    return val;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvY29uZmlnL2dldC1lbnYudHMiLCJtYXBwaW5ncyI6Ijs7OztBQW1CTyxTQUFTQSxPQUNkQyxJQUF3QjtJQUV4QixNQUFNQyxNQUFNQyxRQUFRQyxHQUFHLENBQUNILEtBQUs7SUFDN0IsSUFBSSxDQUFDQyxLQUFLO1FBQ1IsTUFBTSxJQUFJRyxNQUFNLENBQUMsb0NBQW9DLEVBQUVKLEtBQUssQ0FBQztJQUMvRDtJQUNBLE9BQU9DO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AcGljay1iYXphci9zaG9wLy4vc3JjL2NvbmZpZy9nZXQtZW52LnRzP2RkNTMiXSwic291cmNlc0NvbnRlbnQiOlsidHlwZSBFbnZWYXJpYWJsZXMgPSB7XG4gIHJlYWRvbmx5IEVOVjogJ3Byb2R1Y3Rpb24nIHwgJ3N0YWdpbmcnIHwgJ2RldmVsb3BtZW50JyB8ICd0ZXN0JztcbiAgcmVhZG9ubHkgTk9ERV9FTlY6ICdwcm9kdWN0aW9uJyB8ICdkZXZlbG9wbWVudCc7XG4gIHJlYWRvbmx5IEZSQU1FV09SS19QUk9WSURFUjogJ2dyYXBocWwnIHwgJ3Jlc3QnO1xuICByZWFkb25seSBORVhUX1BVQkxJQ19SRVNUX0FQSV9FTkRQT0lOVDogc3RyaW5nO1xuICByZWFkb25seSBORVhUX1BVQkxJQ19HUkFQSFFMX0FQSV9FTkRQT0lOVDogc3RyaW5nO1xuICByZWFkb25seSBORVhUX1BVQkxJQ19ERUZBVUxUX0xBTkdVQUdFOiBzdHJpbmc7XG4gIC8vIHJlYWRvbmx5IE5FWFRfUFVCTElDX1NUUklQRV9QVUJMSVNIQUJMRV9LRVk6IHN0cmluZztcbiAgLy8gcmVhZG9ubHkgTkVYVF9QVUJMSUNfUkFaT1JQQVlfUFVCTElTSEFCTEVfS0VZOiBzdHJpbmc7XG4gIHJlYWRvbmx5IE5FWFRfUFVCTElDX1NUUklQRV9QQVlNRU5UX0VMRU1FTlRfUkVESVJFQ1RfVVJMOiBzdHJpbmc7XG4gIHJlYWRvbmx5IE5FWFRfUFVCTElDX1NJVEVfVVJMOiBzdHJpbmc7XG4gIHJlYWRvbmx5IE5FWFRfUFVCTElDX0FETUlOX1VSTDogc3RyaW5nO1xuICByZWFkb25seSBORVhUQVVUSF9VUkw6IHN0cmluZztcbiAgcmVhZG9ubHkgU0VDUkVUOiBzdHJpbmc7XG4gIHJlYWRvbmx5IEdPT0dMRV9DTElFTlRfSUQ6IHN0cmluZztcbiAgcmVhZG9ubHkgR09PR0xFX0NMSUVOVF9TRUNSRVQ6IHN0cmluZztcbiAgcmVhZG9ubHkgRkFDRUJPT0tfQ0xJRU5UX0lEOiBzdHJpbmc7XG4gIHJlYWRvbmx5IEZBQ0VCT09LX0NMSUVOVF9TRUNSRVQ6IHN0cmluZztcbn07XG5leHBvcnQgZnVuY3Rpb24gZ2V0RW52KFxuICBuYW1lOiBrZXlvZiBFbnZWYXJpYWJsZXNcbik6IEVudlZhcmlhYmxlc1trZXlvZiBFbnZWYXJpYWJsZXNdIHtcbiAgY29uc3QgdmFsID0gcHJvY2Vzcy5lbnZbbmFtZV07XG4gIGlmICghdmFsKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBDYW5ub3QgZmluZCBlbnZpcm9ubWVudGFsIHZhcmlhYmxlOiAke25hbWV9YCk7XG4gIH1cbiAgcmV0dXJuIHZhbDtcbn1cbiJdLCJuYW1lcyI6WyJnZXRFbnYiLCJuYW1lIiwidmFsIiwicHJvY2VzcyIsImVudiIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./src/config/get-env.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _config_get_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/get-env */ \"(api)/./src/config/get-env.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// For more information on each option (and a full list of options) go to\n// https://next-auth.js.org/configuration/options\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_1___default()({\n    // https://next-auth.js.org/configuration/providers\n    providers: [\n        // FacebookProvider({\n        //   clientId: getEnv('FACEBOOK_CLIENT_ID'),\n        //   clientSecret: getEnv('FACEBOOK_CLIENT_SECRET'),\n        // }),\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2___default()({\n            clientId: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"GOOGLE_CLIENT_ID\"),\n            clientSecret: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"GOOGLE_CLIENT_SECRET\")\n        })\n    ],\n    // The secret should be set to a reasonably long random string.\n    // It is used to sign cookies and to sign and encrypt JSON Web Tokens, unless\n    // a separate secret is defined explicitly for encrypting the JWT.\n    secret: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"SECRET\"),\n    session: {\n        // Use JSON Web Tokens for session instead of database sessions.\n        // This option can be used with or without a database for users/accounts.\n        // Note: `jwt` is automatically set to `true` if no database is specified.\n        strategy: \"jwt\"\n    },\n    // JSON Web tokens are only used for sessions if the `jwt: true` session\n    // option is set - or by default if no database is specified.\n    // https://next-auth.js.org/configuration/options#jwt\n    jwt: {\n    },\n    // You can define custom pages to override the built-in ones. These will be regular Next.js pages\n    // so ensure that they are placed outside of the '/api' folder, e.g. signIn: '/auth/mycustom-signin'\n    // The Routes shown here are the default URLs that will be used when a custom\n    // pages is not specified for that route.\n    // https://next-auth.js.org/configuration/pages\n    pages: {\n    },\n    // Callbacks are asynchronous functions you can use to control what happens\n    // when an action is performed.\n    // https://next-auth.js.org/configuration/callbacks\n    callbacks: {\n        // async signIn({ account, profile, user}) {\n        // \tif (account.provider === \"google\") {\n        //     return profile.email_verified && profile?.email?.endsWith(\"@gmail.com\")\n        //   }\n        //   return true // Return true to allow sign in\n        // },\n        async jwt ({ token, account }) {\n            if (account) {\n                const { access_token, provider } = account;\n                token.provider = provider;\n                // reform the `token` object from the access token we appended to the `user` object\n                token.access_token = access_token;\n            }\n            return token;\n        },\n        async session ({ session, token, user }) {\n            const { access_token, provider } = token;\n            //@ts-ignore\n            session.provider = provider;\n            //@ts-ignore\n            session.access_token = access_token;\n            return session;\n        }\n    },\n    // Events are useful for logging\n    // https://next-auth.js.org/configuration/events\n    events: {}\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();