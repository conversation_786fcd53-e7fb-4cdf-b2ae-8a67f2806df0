{"c": ["webpack"], "r": ["pages/[shop]/products/create", "src_components_ui_wysiwyg-editor_index_tsx"], "m": ["./node_modules/lodash/_baseMap.js", "./node_modules/lodash/_baseOrderBy.js", "./node_modules/lodash/_baseSortBy.js", "./node_modules/lodash/_baseSum.js", "./node_modules/lodash/_compareAscending.js", "./node_modules/lodash/_compareMultiple.js", "./node_modules/lodash/cloneDeep.js", "./node_modules/lodash/orderBy.js", "./node_modules/lodash/sum.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CProjects%5CBB%5CProjects%5Ce-commerce%5Clogorithm-e-site%5Cadmin-rest%5Csrc%5Cpages%5C%5Bshop%5D%5Cproducts%5Ccreate.tsx&page=%2F%5Bshop%5D%2Fproducts%2Fcreate!", "./src/components/icons/ios-arrow-left.tsx", "./src/components/icons/long-arrow-prev.tsx", "./src/components/icons/update.tsx", "./src/components/product/form-utils.ts", "./src/components/product/product-ai-prompt.ts", "./src/components/product/product-author-input.tsx", "./src/components/product/product-category-input.tsx", "./src/components/product/product-flash-sale-box.tsx", "./src/components/product/product-form.tsx", "./src/components/product/product-group-input.tsx", "./src/components/product/product-manufacturer-input.tsx", "./src/components/product/product-simple-form.tsx", "./src/components/product/product-tag-input.tsx", "./src/components/product/product-type-input.tsx", "./src/components/product/product-validation-schema.ts", "./src/components/product/product-variable-form.tsx", "./src/components/ui/checkbox/checkbox.tsx", "./src/components/ui/wysiwyg-editor/editor.tsx", "./src/pages/[shop]/products/create.tsx", "./src/utils/cartesian.ts", "./src/utils/omit-typename.ts", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[11].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[11].use[2]!./node_modules/react-quill-emoji/dist/quill-emoji.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[11].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[11].use[2]!./node_modules/react-quill/dist/quill.snow.css", "./node_modules/quill-color-picker-enhance/dist/index.esm.js", "./node_modules/quill/dist/quill.js", "./node_modules/react-quill-emoji/dist/quill-emoji.css", "./node_modules/react-quill-emoji/dist/quill-emoji.js", "./node_modules/react-quill/dist/quill.snow.css", "./node_modules/react-quill/lib/index.js", "./src/components/ui/wysiwyg-editor/index.tsx"]}