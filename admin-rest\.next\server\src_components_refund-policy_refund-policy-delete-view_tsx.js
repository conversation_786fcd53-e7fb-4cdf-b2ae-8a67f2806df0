"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_refund-policy_refund-policy-delete-view_tsx";
exports.ids = ["src_components_refund-policy_refund-policy-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/refund-policy/refund-policy-delete-view.tsx":
/*!********************************************************************!*\
  !*** ./src/components/refund-policy/refund-policy-delete-view.tsx ***!
  \********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _utils_form_error__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/form-error */ \"./src/utils/form-error.tsx\");\n/* harmony import */ var _data_refund_policy__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/refund-policy */ \"./src/data/refund-policy.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _utils_form_error__WEBPACK_IMPORTED_MODULE_3__, _data_refund_policy__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _utils_form_error__WEBPACK_IMPORTED_MODULE_3__, _data_refund_policy__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst RefundPolicyDeleteView = ()=>{\n    const { mutate: deleteRefundPolicy, isLoading: loading } = (0,_data_refund_policy__WEBPACK_IMPORTED_MODULE_4__.useDeleteRefundPolicyMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        try {\n            deleteRefundPolicy({\n                id: data\n            });\n            closeModal();\n        } catch (error) {\n            closeModal();\n            (0,_utils_form_error__WEBPACK_IMPORTED_MODULE_3__.getErrorMessage)(error);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\refund-policy\\\\refund-policy-delete-view.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RefundPolicyDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9yZWZ1bmQtcG9saWN5L3JlZnVuZC1wb2xpY3ktZGVsZXRlLXZpZXcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQXFFO0FBSXhCO0FBQ1E7QUFDZ0I7QUFFckUsTUFBTUsseUJBQXlCO0lBQzdCLE1BQU0sRUFBRUMsUUFBUUMsa0JBQWtCLEVBQUVDLFdBQVdDLE9BQU8sRUFBRSxHQUN4REwsa0ZBQTZCQTtJQUU3QixNQUFNLEVBQUVNLElBQUksRUFBRSxHQUFHUixpRkFBYUE7SUFDOUIsTUFBTSxFQUFFUyxVQUFVLEVBQUUsR0FBR1Ysa0ZBQWNBO0lBRXJDLFNBQVNXO1FBQ1AsSUFBSTtZQUNGTCxtQkFBbUI7Z0JBQ2pCTSxJQUFJSDtZQUNOO1lBQ0FDO1FBQ0YsRUFBRSxPQUFPRyxPQUFPO1lBQ2RIO1lBQ0FSLGtFQUFlQSxDQUFDVztRQUNsQjtJQUNGO0lBRUEscUJBQ0UsOERBQUNkLDRFQUFnQkE7UUFDZmUsVUFBVUo7UUFDVkssVUFBVUo7UUFDVkssa0JBQWtCUjs7Ozs7O0FBR3hCO0FBRUEsaUVBQWVKLHNCQUFzQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BtYXJ2ZWwvYWRtaW4tcmVzdC8uL3NyYy9jb21wb25lbnRzL3JlZnVuZC1wb2xpY3kvcmVmdW5kLXBvbGljeS1kZWxldGUtdmlldy50c3g/OWE1YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ29uZmlybWF0aW9uQ2FyZCBmcm9tICdAL2NvbXBvbmVudHMvY29tbW9uL2NvbmZpcm1hdGlvbi1jYXJkJztcclxuaW1wb3J0IHtcclxuICB1c2VNb2RhbEFjdGlvbixcclxuICB1c2VNb2RhbFN0YXRlLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9tb2RhbC9tb2RhbC5jb250ZXh0JztcclxuaW1wb3J0IHsgZ2V0RXJyb3JNZXNzYWdlIH0gZnJvbSAnQC91dGlscy9mb3JtLWVycm9yJztcclxuaW1wb3J0IHsgdXNlRGVsZXRlUmVmdW5kUG9saWN5TXV0YXRpb24gfSBmcm9tICdAL2RhdGEvcmVmdW5kLXBvbGljeSc7XHJcblxyXG5jb25zdCBSZWZ1bmRQb2xpY3lEZWxldGVWaWV3ID0gKCkgPT4ge1xyXG4gIGNvbnN0IHsgbXV0YXRlOiBkZWxldGVSZWZ1bmRQb2xpY3ksIGlzTG9hZGluZzogbG9hZGluZyB9ID1cclxuICB1c2VEZWxldGVSZWZ1bmRQb2xpY3lNdXRhdGlvbigpO1xyXG5cclxuICBjb25zdCB7IGRhdGEgfSA9IHVzZU1vZGFsU3RhdGUoKTtcclxuICBjb25zdCB7IGNsb3NlTW9kYWwgfSA9IHVzZU1vZGFsQWN0aW9uKCk7XHJcblxyXG4gIGZ1bmN0aW9uIGhhbmRsZURlbGV0ZSgpIHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGRlbGV0ZVJlZnVuZFBvbGljeSh7XHJcbiAgICAgICAgaWQ6IGRhdGEsXHJcbiAgICAgIH0pO1xyXG4gICAgICBjbG9zZU1vZGFsKCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjbG9zZU1vZGFsKCk7XHJcbiAgICAgIGdldEVycm9yTWVzc2FnZShlcnJvcik7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPENvbmZpcm1hdGlvbkNhcmRcclxuICAgICAgb25DYW5jZWw9e2Nsb3NlTW9kYWx9XHJcbiAgICAgIG9uRGVsZXRlPXtoYW5kbGVEZWxldGV9XHJcbiAgICAgIGRlbGV0ZUJ0bkxvYWRpbmc9e2xvYWRpbmd9XHJcbiAgICAvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBSZWZ1bmRQb2xpY3lEZWxldGVWaWV3O1xyXG4iXSwibmFtZXMiOlsiQ29uZmlybWF0aW9uQ2FyZCIsInVzZU1vZGFsQWN0aW9uIiwidXNlTW9kYWxTdGF0ZSIsImdldEVycm9yTWVzc2FnZSIsInVzZURlbGV0ZVJlZnVuZFBvbGljeU11dGF0aW9uIiwiUmVmdW5kUG9saWN5RGVsZXRlVmlldyIsIm11dGF0ZSIsImRlbGV0ZVJlZnVuZFBvbGljeSIsImlzTG9hZGluZyIsImxvYWRpbmciLCJkYXRhIiwiY2xvc2VNb2RhbCIsImhhbmRsZURlbGV0ZSIsImlkIiwiZXJyb3IiLCJvbkNhbmNlbCIsIm9uRGVsZXRlIiwiZGVsZXRlQnRuTG9hZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/refund-policy/refund-policy-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/refund-policy.ts":
/*!******************************************!*\
  !*** ./src/data/client/refund-policy.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RefundPolicyClient: () => (/* binding */ RefundPolicyClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst RefundPolicyClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REFUND_POLICIES),\n    paginated: ({ target, title, status, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REFUND_POLICIES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                title,\n                target,\n                status\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/refund-policy.ts\n");

/***/ }),

/***/ "./src/data/refund-policy.ts":
/*!***********************************!*\
  !*** ./src/data/refund-policy.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateRefundPolicyMutation: () => (/* binding */ useCreateRefundPolicyMutation),\n/* harmony export */   useDeleteRefundPolicyMutation: () => (/* binding */ useDeleteRefundPolicyMutation),\n/* harmony export */   useRefundPoliciesQuery: () => (/* binding */ useRefundPoliciesQuery),\n/* harmony export */   useRefundPolicyQuery: () => (/* binding */ useRefundPolicyQuery),\n/* harmony export */   useUpdateRefundPolicyMutation: () => (/* binding */ useUpdateRefundPolicyMutation)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/data/client/refund-policy */ \"./src/data/client/refund-policy.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst useCreateRefundPolicyMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__.RefundPolicyClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.refundPolicies.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.refundPolicies.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.REFUND_POLICIES);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\nconst useDeleteRefundPolicyMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__.RefundPolicyClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.REFUND_POLICIES);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\nconst useUpdateRefundPolicyMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__.RefundPolicyClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.refundPolicies.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.refundPolicies.list;\n            await router.push(`${generateRedirectUrl}/${data?.slug}/edit`, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.REFUND_POLICIES);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\nconst useRefundPolicyQuery = ({ slug, language })=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.REFUND_POLICIES,\n        {\n            slug,\n            language\n        }\n    ], ()=>_data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__.RefundPolicyClient.get({\n            slug,\n            language\n        }));\n    return {\n        refundPolicy: data,\n        error,\n        loading: isLoading\n    };\n};\nconst useRefundPoliciesQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.REFUND_POLICIES,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__.RefundPolicyClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        refundPolicies: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/refund-policy.ts\n");

/***/ }),

/***/ "./src/utils/form-error.tsx":
/*!**********************************!*\
  !*** ./src/utils/form-error.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_1__]);\njs_cookie__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction getErrorMessage(error) {\n    let processedError = {\n        message: \"\",\n        validation: []\n    };\n    if (error.graphQLErrors) {\n        for (const graphQLError of error.graphQLErrors){\n            if (graphQLError.extensions && graphQLError.extensions.category === \"validation\") {\n                processedError[\"message\"] = graphQLError.message;\n                processedError[\"validation\"] = graphQLError.extensions.validation;\n                return processedError;\n            } else if (graphQLError.extensions && graphQLError.extensions.category === \"authorization\") {\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_token\");\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_permissions\");\n                next_router__WEBPACK_IMPORTED_MODULE_0___default().push(\"/\");\n            }\n        }\n    }\n    processedError[\"message\"] = error.message;\n    return processedError;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/form-error.tsx\n");

/***/ })

};
;