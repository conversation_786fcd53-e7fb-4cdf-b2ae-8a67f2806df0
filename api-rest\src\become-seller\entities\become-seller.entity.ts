import { Type } from 'class-transformer';
import { Column, Model, Table, DataType } from 'sequelize-typescript';
import { Attachment } from 'src/common/entities/attachment.entity';

@Table({
  tableName: 'become_sellers',
  timestamps: true,
})
export class BecomeSeller extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  page_options: {
    page_options: BecomeSellerOptions;
  };

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  language: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  missions: CommissionItem[];
}

export class BecomeSellerOptions {
  banner: Attachment;
  sellingStepsTitle: string;
  sellingStepsDescription: string;
  sellingStepsItem: SellingStepItem[];
  purposeTitle: string;
  purposeDescription: string;
  purposeItems: BusinessPurposeItem[];
  commissionTitle: string;
  commissionDescription: string;
  faqTitle: string;
  faqDescription: string;
  faqItems: FaqItems[];
}
export class SellingStepItem {
  id?: string;
  description: string;
  title: string;
  image?: Attachment;
}
export class BusinessPurposeItem {
  id?: string;
  description: string;
  title: string;
  icon: {
    value: string;
  };
}
export class FaqItems {
  description: string;
  title: string;
}

export interface CommissionItem {
  id?: string;
  level: string;
  sub_level: string;
  description: string;
  min_balance: number;
  max_balance: number;
  commission: number;
  image: Attachment;
}
