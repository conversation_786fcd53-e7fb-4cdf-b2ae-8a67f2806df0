"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BecomeSellerModule = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const become_seller_controller_1 = require("./become-seller.controller");
const become_seller_service_1 = require("./become-seller.service");
const become_seller_entity_1 = require("./entities/become-seller.entity");
let BecomeSellerModule = class BecomeSellerModule {
};
BecomeSellerModule = __decorate([
    (0, common_1.Module)({
        imports: [sequelize_1.SequelizeModule.forFeature([become_seller_entity_1.BecomeSeller])],
        controllers: [become_seller_controller_1.BecomeSellerController],
        providers: [become_seller_service_1.BecomeSellerService],
        exports: [become_seller_service_1.BecomeSellerService],
    })
], BecomeSellerModule);
exports.BecomeSellerModule = BecomeSellerModule;
//# sourceMappingURL=become-seller.module.js.map