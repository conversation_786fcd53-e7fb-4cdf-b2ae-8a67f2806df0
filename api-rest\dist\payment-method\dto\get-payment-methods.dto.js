"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetPaymentMethodsDto = void 0;
const openapi = require("@nestjs/swagger");
class GetPaymentMethodsDto {
    static _OPENAPI_METADATA_FACTORY() {
        return { text: { required: false, type: () => String } };
    }
}
exports.GetPaymentMethodsDto = GetPaymentMethodsDto;
//# sourceMappingURL=get-payment-methods.dto.js.map