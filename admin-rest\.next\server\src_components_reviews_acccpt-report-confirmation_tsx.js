"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_reviews_acccpt-report-confirmation_tsx";
exports.ids = ["src_components_reviews_acccpt-report-confirmation_tsx"];
exports.modules = {

/***/ "./src/components/reviews/acccpt-report-confirmation.tsx":
/*!***************************************************************!*\
  !*** ./src/components/reviews/acccpt-report-confirmation.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_review__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/review */ \"./src/data/review.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_review__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_review__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AcceptAbuseReportView = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { mutate: deleteReview, isLoading: loading } = (0,_data_review__WEBPACK_IMPORTED_MODULE_3__.useDeleteReviewMutation)();\n    const { data: modalData } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteReview({\n            id: modalData\n        });\n        closeModal();\n        router.push(`/reviews`);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        title: \"text-accept\",\n        description: \"text-accept-report-modal-description\",\n        onCancel: closeModal,\n        deleteBtnText: \"text-accept\",\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\acccpt-report-confirmation.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AcceptAbuseReportView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/reviews/acccpt-report-confirmation.tsx\n");

/***/ }),

/***/ "./src/data/client/review.ts":
/*!***********************************!*\
  !*** ./src/data/client/review.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reviewClient: () => (/* binding */ reviewClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst reviewClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REVIEWS),\n    reportAbuse: (data)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.ABUSIVE_REPORTS, data);\n    },\n    decline: (data)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.ABUSIVE_REPORTS_DECLINE, data);\n    },\n    get ({ id }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REVIEWS}/${id}`, {\n            with: \"abusive_reports.user;product;user\"\n        });\n    },\n    paginated: ({ type, shop_id, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REVIEWS, {\n            searchJoin: \"and\",\n            with: \"product;user\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                shop_id\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/review.ts\n");

/***/ }),

/***/ "./src/data/review.ts":
/*!****************************!*\
  !*** ./src/data/review.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAbuseReportMutation: () => (/* binding */ useAbuseReportMutation),\n/* harmony export */   useDeclineReviewMutation: () => (/* binding */ useDeclineReviewMutation),\n/* harmony export */   useDeleteReviewMutation: () => (/* binding */ useDeleteReviewMutation),\n/* harmony export */   useReviewQuery: () => (/* binding */ useReviewQuery),\n/* harmony export */   useReviewsQuery: () => (/* binding */ useReviewsQuery)\n/* harmony export */ });\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_client_review__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/client/review */ \"./src/data/client/review.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_1__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_3__, _data_client_review__WEBPACK_IMPORTED_MODULE_6__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_1__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_3__, _data_client_review__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst useAbuseReportMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__.useModalAction)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.reportAbuse, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"text-abuse-report-submitted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.refetchQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS);\n            closeModal();\n        }\n    });\n};\nconst useDeclineReviewMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.decline, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"successfully-decline\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.refetchQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS);\n        }\n    });\n};\nconst useDeleteReviewMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS);\n        }\n    });\n};\nconst useReviewQuery = (id)=>{\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS,\n        id\n    ], ()=>_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.get({\n            id\n        }));\n};\nconst useReviewsQuery = (params, options = {})=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS,\n        params\n    ], ({ queryKey, pageParam })=>_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true,\n        ...options\n    });\n    return {\n        reviews: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_3__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/review.ts\n");

/***/ })

};
;