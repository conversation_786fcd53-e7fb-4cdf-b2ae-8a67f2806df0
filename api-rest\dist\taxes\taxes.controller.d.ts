import { TaxesService } from './taxes.service';
import { CreateTaxDto } from './dto/create-tax.dto';
import { UpdateTaxDto } from './dto/update-tax.dto';
import { GetTaxesDto } from './dto/get-taxes.dto';
export declare class TaxesController {
    private readonly taxesService;
    constructor(taxesService: TaxesService);
    create(createTaxDto: CreateTaxDto): Promise<import("./entities/tax.entity").Tax>;
    findAll(getTaxesDto: GetTaxesDto): Promise<import("./entities/tax.entity").Tax[]>;
    findOne(id: string): Promise<import("./entities/tax.entity").Tax>;
    update(id: string, updateTaxDto: UpdateTaxDto): Promise<import("./entities/tax.entity").Tax>;
    remove(id: string): string;
}
