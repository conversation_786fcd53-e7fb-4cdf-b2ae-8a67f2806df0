{"c": ["webpack"], "r": ["pages/categories"], "m": ["./node_modules/@headlessui/react/dist/components/disclosure/disclosure.js", "./node_modules/@headlessui/react/dist/components/popover/popover.js", "./node_modules/@headlessui/react/dist/utils/start-transition.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CProjects%5CBB%5CProjects%5Ce-commerce%5Clogorithm-e-site%5Cadmin-rest%5Csrc%5Cpages%5Ccategories%5Cindex.tsx&page=%2Fcategories!", "./src/components/category/category-list.tsx", "./src/components/category/type-filter.tsx", "./src/components/icons/category/accessories.tsx", "./src/components/icons/category/baby-care.tsx", "./src/components/icons/category/bath-oil.tsx", "./src/components/icons/category/beauty-care.tsx", "./src/components/icons/category/beauty-health.tsx", "./src/components/icons/category/bed.tsx", "./src/components/icons/category/beverage.tsx", "./src/components/icons/category/blender.tsx", "./src/components/icons/category/book-shelf.tsx", "./src/components/icons/category/breakfast.tsx", "./src/components/icons/category/camera.tsx", "./src/components/icons/category/center-table.tsx", "./src/components/icons/category/chair.tsx", "./src/components/icons/category/console.tsx", "./src/components/icons/category/contraceptive.tsx", "./src/components/icons/category/cooking.tsx", "./src/components/icons/category/dairy.tsx", "./src/components/icons/category/deodorant.tsx", "./src/components/icons/category/diapers.tsx", "./src/components/icons/category/dressing-table.tsx", "./src/components/icons/category/eyes.tsx", "./src/components/icons/category/face-skin-care.tsx", "./src/components/icons/category/face.tsx", "./src/components/icons/category/facial-care.tsx", "./src/components/icons/category/feeders.tsx", "./src/components/icons/category/feminine-hygiene.tsx", "./src/components/icons/category/first-aid-kit.tsx", "./src/components/icons/category/fruits-vegetable.tsx", "./src/components/icons/category/gadget-accessories.tsx", "./src/components/icons/category/hand-bag.tsx", "./src/components/icons/category/headphone.tsx", "./src/components/icons/category/health-protein.tsx", "./src/components/icons/category/health-wellness.tsx", "./src/components/icons/category/herb.tsx", "./src/components/icons/category/home-cleaning.tsx", "./src/components/icons/category/index.tsx", "./src/components/icons/category/indoor-plants.tsx", "./src/components/icons/category/laptop-bag.tsx", "./src/components/icons/category/laptop.tsx", "./src/components/icons/category/lips.tsx", "./src/components/icons/category/meat-fish.tsx", "./src/components/icons/category/microwave.tsx", "./src/components/icons/category/mobile.tsx", "./src/components/icons/category/monitor.tsx", "./src/components/icons/category/oral-care.tsx", "./src/components/icons/category/oral.tsx", "./src/components/icons/category/orchid.tsx", "./src/components/icons/category/outer-wear.tsx", "./src/components/icons/category/pants.tsx", "./src/components/icons/category/pet-care.tsx", "./src/components/icons/category/pregnancy.tsx", "./src/components/icons/category/purse.tsx", "./src/components/icons/category/reading-table.tsx", "./src/components/icons/category/relax-chair.tsx", "./src/components/icons/category/router.tsx", "./src/components/icons/category/seeds.tsx", "./src/components/icons/category/sexual-wellbeing.tsx", "./src/components/icons/category/shaving-needs.tsx", "./src/components/icons/category/shirts.tsx", "./src/components/icons/category/shoulder-bag.tsx", "./src/components/icons/category/skirts.tsx", "./src/components/icons/category/smart-watch.tsx", "./src/components/icons/category/snacks.tsx", "./src/components/icons/category/sofa.tsx", "./src/components/icons/category/sound-box.tsx", "./src/components/icons/category/storage.tsx", "./src/components/icons/category/succulent.tsx", "./src/components/icons/category/table.tsx", "./src/components/icons/category/tiny-veg.tsx", "./src/components/icons/category/tools.tsx", "./src/components/icons/category/tops.tsx", "./src/components/icons/category/wallet.tsx", "./src/components/icons/category/washing-machine.tsx", "./src/components/icons/category/women-dress.tsx", "./src/components/icons/chevronDownIcon.tsx", "./src/components/icons/toggle-icon.tsx", "./src/components/ui/lang-action/action.tsx", "./src/components/ui/lang-action/lang-list-box.tsx", "./src/components/ui/lang-action/language-switcher.tsx", "./src/components/ui/popover.tsx", "./src/pages/categories/index.tsx", "__barrel_optimize__?names=Disclosure!=!./node_modules/@headlessui/react/dist/headlessui.esm.js", "__barrel_optimize__?names=Popover!=!./node_modules/@headlessui/react/dist/headlessui.esm.js"]}