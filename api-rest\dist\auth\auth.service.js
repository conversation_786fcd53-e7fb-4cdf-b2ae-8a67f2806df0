"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const user_entity_1 = require("../users/entities/user.entity");
let AuthService = class AuthService {
    constructor(userModel) {
        this.userModel = userModel;
    }
    async register(createUserInput) {
        return {
            token: 'jwt token',
            permissions: ['super_admin', 'customer'],
        };
    }
    async login(loginInput) {
        console.log(loginInput);
        if (loginInput.email === '<EMAIL>') {
            return {
                token: 'jwt token',
                permissions: ['store_owner', 'super_admin'],
                role: 'super_admin',
            };
        }
        else if (['<EMAIL>', '<EMAIL>'].includes(loginInput.email)) {
            return {
                token: 'jwt token',
                permissions: ['store_owner', 'customer'],
                role: 'store_owner',
            };
        }
        else {
            return {
                token: 'jwt token',
                permissions: ['customer'],
                role: 'customer',
            };
        }
    }
    async changePassword(changePasswordInput) {
        console.log(changePasswordInput);
        return {
            success: true,
            message: 'Password change successful',
        };
    }
    async forgetPassword(forgetPasswordInput) {
        console.log(forgetPasswordInput);
        return {
            success: true,
            message: 'Password change successful',
        };
    }
    async verifyForgetPasswordToken(verifyForgetPasswordTokenInput) {
        console.log(verifyForgetPasswordTokenInput);
        return {
            success: true,
            message: 'Password change successful',
        };
    }
    async resetPassword(resetPasswordInput) {
        console.log(resetPasswordInput);
        return {
            success: true,
            message: 'Password change successful',
        };
    }
    async socialLogin(socialLoginDto) {
        console.log(socialLoginDto);
        return {
            token: 'jwt token',
            permissions: ['super_admin', 'customer'],
            role: 'customer',
        };
    }
    async otpLogin(otpLoginDto) {
        console.log(otpLoginDto);
        return {
            token: 'jwt token',
            permissions: ['super_admin', 'customer'],
            role: 'customer',
        };
    }
    async verifyOtpCode(verifyOtpInput) {
        console.log(verifyOtpInput);
        return {
            message: 'success',
            success: true,
        };
    }
    async sendOtpCode(otpInput) {
        console.log(otpInput);
        return {
            message: 'success',
            success: true,
            id: '1',
            provider: 'google',
            phone_number: '+919494949494',
            is_contact_exist: true,
        };
    }
    async me() {
        return null;
    }
    async updateEmail(updateEmailDto) {
        try {
            console.log('Updating email to:', updateEmailDto.email);
            return {
                success: true,
                message: 'Email updated successfully',
            };
        }
        catch (error) {
            return {
                success: false,
                message: 'Failed to update email',
            };
        }
    }
};
AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(user_entity_1.User)),
    __metadata("design:paramtypes", [Object])
], AuthService);
exports.AuthService = AuthService;
//# sourceMappingURL=auth.service.js.map