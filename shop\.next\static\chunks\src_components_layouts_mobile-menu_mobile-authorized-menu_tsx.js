"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_layouts_mobile-menu_mobile-authorized-menu_tsx"],{

/***/ "./src/components/layouts/mobile-menu/mobile-authorized-menu.tsx":
/*!***********************************************************************!*\
  !*** ./src/components/layouts/mobile-menu/mobile-authorized-menu.tsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MobileAuthorizedMenu; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _config_site__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/site */ \"./src/config/site.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/drawer/drawer-wrapper */ \"./src/components/ui/drawer/drawer-wrapper.tsx\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! jotai */ \"./node_modules/jotai/esm/index.mjs\");\n/* harmony import */ var _store_drawer_atom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/drawer-atom */ \"./src/store/drawer-atom.ts\");\n/* harmony import */ var _framework_user__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/framework/user */ \"./src/framework/rest/user.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MobileAuthorizedMenu() {\n    var _me_wallet, _me_wallet1, _me_wallet2;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { me } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_6__.useUser)();\n    const [_, closeSidebar] = (0,jotai__WEBPACK_IMPORTED_MODULE_7__.useAtom)(_store_drawer_atom__WEBPACK_IMPORTED_MODULE_5__.drawerAtom);\n    const { mutate: logout } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_6__.useLogout)();\n    function handleClick(path) {\n        router.push(path);\n        closeSidebar({\n            display: false,\n            view: \"\"\n        });\n    }\n    var _me_wallet_total_points, _me_wallet_points_used, _me_wallet_available_points;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            className: \"grow\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex w-full items-center justify-between border-t border-dashed border-border-200 bg-gray-100 px-5 pt-3 text-sm font-semibold capitalize text-body focus:outline-none ltr:text-left rtl:text-right md:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"text-total-points\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: (_me_wallet_total_points = me === null || me === void 0 ? void 0 : (_me_wallet = me.wallet) === null || _me_wallet === void 0 ? void 0 : _me_wallet.total_points) !== null && _me_wallet_total_points !== void 0 ? _me_wallet_total_points : 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex w-full items-center justify-between bg-gray-100 px-5 pt-3 text-sm font-semibold capitalize text-body focus:outline-none ltr:text-left rtl:text-right md:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"text-points-used\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: (_me_wallet_points_used = me === null || me === void 0 ? void 0 : (_me_wallet1 = me.wallet) === null || _me_wallet1 === void 0 ? void 0 : _me_wallet1.points_used) !== null && _me_wallet_points_used !== void 0 ? _me_wallet_points_used : 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex w-full items-center justify-between border-b border-dashed border-border-200 bg-gray-100 px-5 py-3 text-sm font-semibold capitalize text-body focus:outline-none ltr:text-left rtl:text-right md:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"text-available-points\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: (_me_wallet_available_points = me === null || me === void 0 ? void 0 : (_me_wallet2 = me.wallet) === null || _me_wallet2 === void 0 ? void 0 : _me_wallet2.available_points) !== null && _me_wallet_available_points !== void 0 ? _me_wallet_available_points : 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                _config_site__WEBPACK_IMPORTED_MODULE_1__.siteSettings.authorizedLinksMobile.map((param)=>/*#__PURE__*/ {\n                    let { href, label } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"block cursor-pointer px-5 py-3 text-sm font-semibold capitalize text-heading transition duration-200 hover:text-accent md:px-8\",\n                            onClick: ()=>handleClick(href),\n                            children: t(label)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this)\n                    }, \"\".concat(href).concat(label), false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this);\n                }),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"block cursor-pointer px-5 py-3 text-sm font-semibold capitalize text-heading transition duration-200 hover:text-accent md:px-8\",\n                        onClick: ()=>logout(),\n                        children: t(\"auth-menu-logout\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_s(MobileAuthorizedMenu, \"GZrZYBcjO5zthGs9WKIPKKdxa4M=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _framework_user__WEBPACK_IMPORTED_MODULE_6__.useUser,\n        jotai__WEBPACK_IMPORTED_MODULE_7__.useAtom,\n        _framework_user__WEBPACK_IMPORTED_MODULE_6__.useLogout\n    ];\n});\n_c = MobileAuthorizedMenu;\nvar _c;\n$RefreshReg$(_c, \"MobileAuthorizedMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layouts/mobile-menu/mobile-authorized-menu.tsx\n"));

/***/ }),

/***/ "./src/config/site.ts":
/*!****************************!*\
  !*** ./src/config/site.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siteSettings: function() { return /* binding */ siteSettings; }\n/* harmony export */ });\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"./src/types/index.ts\");\n\n\nconst siteSettings = {\n    name: \"PickBazar\",\n    description: \"\",\n    logo: {\n        url: \"/logo.svg\",\n        alt: \"PickBazar\",\n        href: \"/grocery\",\n        width: 128,\n        height: 40\n    },\n    defaultLanguage: \"en\",\n    currencyCode: \"USD\",\n    product: {\n        placeholderImage: \"/product-placeholder.svg\",\n        cardMaps: {\n            grocery: \"Krypton\",\n            furniture: \"Radon\",\n            bag: \"Oganesson\",\n            makeup: \"Neon\",\n            book: \"Xenon\",\n            medicine: \"Helium\",\n            default: \"Argon\"\n        }\n    },\n    authorizedLinks: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"auth-menu-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"auth-menu-my-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.checkout,\n            label: \"auth-menu-checkout\"\n        }\n    ],\n    authorizedLinksMobile: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"auth-menu-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.notifyLogs,\n            label: \"profile-sidebar-notifications\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"auth-menu-my-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.cards,\n            label: \"profile-sidebar-my-cards\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.questions,\n            label: \"profile-sidebar-my-questions\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.refunds,\n            label: \"text-my-refunds\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.reports,\n            label: \"profile-sidebar-my-reports\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.checkout,\n            label: \"auth-menu-checkout\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.changePassword,\n            label: \"profile-sidebar-password\"\n        }\n    ],\n    dashboardSidebarMenu: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"profile-sidebar-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.changePassword,\n            label: \"profile-sidebar-password\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.notifyLogs,\n            label: \"profile-sidebar-notifications\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.cards,\n            label: \"profile-sidebar-my-cards\",\n            // MultiPayment: Make it dynamic or from mapper\n            cardsPayment: [\n                _types__WEBPACK_IMPORTED_MODULE_1__.PaymentGateway.STRIPE\n            ]\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"profile-sidebar-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.downloads,\n            label: \"profile-sidebar-downloads\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.questions,\n            label: \"profile-sidebar-my-questions\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.refunds,\n            label: \"text-my-refunds\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.reports,\n            label: \"profile-sidebar-my-reports\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help,\n            label: \"profile-sidebar-help\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.logout,\n            label: \"profile-sidebar-logout\"\n        }\n    ],\n    sellingAdvertisement: {\n        image: {\n            src: \"/selling.png\",\n            alt: \"Selling Advertisement\"\n        }\n    },\n    cta: {\n        mockup_img_src: \"/mockup-img.png\",\n        play_store_link: \"/\",\n        app_store_link: \"/\"\n    },\n    headerLinks: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shops,\n            icon: null,\n            label: \"nav-menu-shops\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.coupons,\n            icon: null,\n            label: \"nav-menu-offer\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.contactUs,\n            label: \"nav-menu-contact\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.becomeSeller,\n            label: \"Become a seller\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.flashSale,\n            label: \"nav-menu-flash-sale\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.manufacturers,\n            label: \"text-manufacturers\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.authors,\n            label: \"text-authors\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help,\n            label: \"nav-menu-faq\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.terms,\n            label: \"nav-menu-terms\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.customerRefundPolicies,\n            label: \"nav-menu-refund-policy\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.vendorRefundPolicies,\n            label: \"nav-menu-vendor-refund-policy\"\n        }\n    ],\n    footer: {\n        // copyright: {\n        //   name: 'RedQ, Inc',\n        //   href: 'https://redq.io/',\n        // },\n        // address: '2429 River Drive, Suite 35 Cottonhall, CA 2296 United Kingdom',\n        // email: '<EMAIL>',\n        // phone: '******-698-0694',\n        menus: [\n            {\n                title: \"text-explore\",\n                links: [\n                    {\n                        name: \"Shops\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shops\n                    },\n                    {\n                        name: \"Authors\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.authors\n                    },\n                    {\n                        name: \"Flash Deals\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes === null || _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes === void 0 ? void 0 : _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.flashSale\n                    },\n                    {\n                        name: \"Coupon\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.coupons\n                    }\n                ]\n            },\n            {\n                title: \"text-customer-service\",\n                links: [\n                    {\n                        name: \"text-faq-help\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help\n                    },\n                    {\n                        name: \"Vendor Refund Policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.vendorRefundPolicies\n                    },\n                    {\n                        name: \"Customer Refund Policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.customerRefundPolicies\n                    }\n                ]\n            },\n            {\n                title: \"text-our-information\",\n                links: [\n                    {\n                        name: \"Manufacturers\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes === null || _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes === void 0 ? void 0 : _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.manufacturers\n                    },\n                    {\n                        name: \"Privacy policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.privacy\n                    },\n                    {\n                        name: \"text-terms-condition\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.terms\n                    },\n                    {\n                        name: \"text-contact-us\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.contactUs\n                    }\n                ]\n            }\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/config/site.ts\n"));

/***/ })

}]);