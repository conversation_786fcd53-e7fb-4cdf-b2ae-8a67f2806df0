import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreateTypeDto } from './dto/create-type.dto';
import { UpdateTypeDto } from './dto/update-type.dto';
import { Type } from './entities/type.entity';
import { paginate } from 'src/common/pagination/paginate';
import Fuse from 'fuse.js';
import { GetTypesDto } from './dto/get-types.dto';
@Injectable()
export class TypesService {
  constructor(
    @InjectModel(Type)
    private typeModel: typeof Type,
  ) {}

  async getTypes({ text, search }: GetTypesDto): Promise<Type[]> {
    // TODO: Implement proper Sequelize query with search
    let data: Type[] = await this.typeModel.findAll();
    // TODO: Implement search functionality with Sequelize

    return data;
  }

  async getTypeBySlug(slug: string): Promise<Type | null> {
    return this.typeModel.findOne({ where: { slug } });
  }

  async create(createTypeDto: CreateTypeDto): Promise<Type> {
    return this.typeModel.create(createTypeDto as any);
  }

  findAll() {
    return `This action returns all types`;
  }

  findOne(id: number) {
    return `This action returns a #${id} type`;
  }

  async update(id: number, updateTypeDto: UpdateTypeDto): Promise<Type | null> {
    await this.typeModel.update(updateTypeDto as any, { where: { id } });
    return this.typeModel.findByPk(id);
  }

  remove(id: number) {
    return `This action removes a #${id} type`;
  }
}
