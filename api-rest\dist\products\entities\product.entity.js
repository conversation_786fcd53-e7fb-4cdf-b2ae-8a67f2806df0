"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.File = exports.VariationOption = exports.Variation = exports.OrderProductPivot = exports.ProductTag = exports.Product = void 0;
const openapi = require("@nestjs/swagger");
const sequelize_typescript_1 = require("sequelize-typescript");
const category_entity_1 = require("../../categories/entities/category.entity");
const shop_entity_1 = require("../../shops/entities/shop.entity");
const tag_entity_1 = require("../../tags/entities/tag.entity");
const type_entity_1 = require("../../types/entities/type.entity");
const review_entity_1 = require("../../reviews/entities/review.entity");
const core_entity_1 = require("../../common/entities/core.entity");
var ProductStatus;
(function (ProductStatus) {
    ProductStatus["PUBLISH"] = "publish";
    ProductStatus["DRAFT"] = "draft";
})(ProductStatus || (ProductStatus = {}));
var ProductType;
(function (ProductType) {
    ProductType["SIMPLE"] = "simple";
    ProductType["VARIABLE"] = "variable";
})(ProductType || (ProductType = {}));
let Product = class Product extends sequelize_typescript_1.Model {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => Number }, name: { required: true, type: () => String }, slug: { required: true, type: () => String }, type_id: { required: true, type: () => Number }, type: { required: true, type: () => require("../../types/entities/type.entity").Type }, product_type: { required: true, enum: ProductType }, categories: { required: true, type: () => [require("../../categories/entities/category.entity").Category] }, tags: { required: false, type: () => [require("../../tags/entities/tag.entity").Tag] }, variations: { required: false, type: () => [require("../../attributes/entities/attribute-value.entity").AttributeValue] }, variation_options: { required: false, type: () => [Object] }, pivot: { required: false, type: () => Object }, shop_id: { required: true, type: () => Number }, shop: { required: true, type: () => require("../../shops/entities/shop.entity").Shop }, related_products: { required: false, type: () => [require("./product.entity").Product] }, description: { required: true, type: () => String }, in_stock: { required: true, type: () => Boolean }, is_taxable: { required: true, type: () => Boolean }, sale_price: { required: false, type: () => Number }, max_price: { required: false, type: () => Number }, min_price: { required: false, type: () => Number }, sku: { required: false, type: () => String }, gallery: { required: false, type: () => [Object] }, image: { required: false, type: () => Object }, status: { required: true, enum: ProductStatus }, height: { required: false, type: () => String }, length: { required: false, type: () => String }, width: { required: false, type: () => String }, price: { required: false, type: () => Number }, quantity: { required: true, type: () => Number }, unit: { required: true, type: () => String }, ratings: { required: true, type: () => Number }, in_wishlist: { required: true, type: () => Boolean }, my_review: { required: false, type: () => [require("../../reviews/entities/review.entity").Review] }, language: { required: false, type: () => String }, translated_languages: { required: false, type: () => String }, visibility: { required: false, type: () => String }, created_at: { required: true, type: () => Date }, updated_at: { required: true, type: () => Date } };
    }
};
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    }),
    __metadata("design:type", Number)
], Product.prototype, "id", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: false,
    }),
    __metadata("design:type", String)
], Product.prototype, "name", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: false,
        unique: true,
    }),
    __metadata("design:type", String)
], Product.prototype, "slug", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => type_entity_1.Type),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        allowNull: false,
    }),
    __metadata("design:type", Number)
], Product.prototype, "type_id", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => type_entity_1.Type),
    __metadata("design:type", type_entity_1.Type)
], Product.prototype, "type", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.ENUM('simple', 'variable'),
        allowNull: false,
        defaultValue: 'simple',
    }),
    __metadata("design:type", String)
], Product.prototype, "product_type", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsToMany)(() => category_entity_1.Category, () => category_entity_1.ProductCategory),
    __metadata("design:type", Array)
], Product.prototype, "categories", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsToMany)(() => tag_entity_1.Tag, () => ProductTag),
    __metadata("design:type", Array)
], Product.prototype, "tags", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.JSONB,
        allowNull: true,
    }),
    __metadata("design:type", Array)
], Product.prototype, "variations", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.JSONB,
        allowNull: true,
    }),
    __metadata("design:type", Array)
], Product.prototype, "variation_options", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.JSONB,
        allowNull: true,
    }),
    __metadata("design:type", Object)
], Product.prototype, "pivot", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => shop_entity_1.Shop),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        allowNull: false,
    }),
    __metadata("design:type", Number)
], Product.prototype, "shop_id", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => shop_entity_1.Shop),
    __metadata("design:type", shop_entity_1.Shop)
], Product.prototype, "shop", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.JSONB,
        allowNull: true,
    }),
    __metadata("design:type", Array)
], Product.prototype, "related_products", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.TEXT,
        allowNull: true,
    }),
    __metadata("design:type", String)
], Product.prototype, "description", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.BOOLEAN,
        defaultValue: true,
    }),
    __metadata("design:type", Boolean)
], Product.prototype, "in_stock", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.BOOLEAN,
        defaultValue: false,
    }),
    __metadata("design:type", Boolean)
], Product.prototype, "is_taxable", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.DECIMAL(10, 2),
        allowNull: true,
    }),
    __metadata("design:type", Number)
], Product.prototype, "sale_price", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.DECIMAL(10, 2),
        allowNull: true,
    }),
    __metadata("design:type", Number)
], Product.prototype, "max_price", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.DECIMAL(10, 2),
        allowNull: true,
    }),
    __metadata("design:type", Number)
], Product.prototype, "min_price", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: true,
    }),
    __metadata("design:type", String)
], Product.prototype, "sku", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.JSONB,
        allowNull: true,
    }),
    __metadata("design:type", Array)
], Product.prototype, "gallery", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.JSONB,
        allowNull: true,
    }),
    __metadata("design:type", Object)
], Product.prototype, "image", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.ENUM('publish', 'draft'),
        allowNull: false,
        defaultValue: 'draft',
    }),
    __metadata("design:type", String)
], Product.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: true,
    }),
    __metadata("design:type", String)
], Product.prototype, "height", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: true,
    }),
    __metadata("design:type", String)
], Product.prototype, "length", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: true,
    }),
    __metadata("design:type", String)
], Product.prototype, "width", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.DECIMAL(10, 2),
        allowNull: true,
    }),
    __metadata("design:type", Number)
], Product.prototype, "price", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        allowNull: false,
        defaultValue: 0,
    }),
    __metadata("design:type", Number)
], Product.prototype, "quantity", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: false,
        defaultValue: 'piece',
    }),
    __metadata("design:type", String)
], Product.prototype, "unit", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.DECIMAL(3, 2),
        allowNull: false,
        defaultValue: 0,
    }),
    __metadata("design:type", Number)
], Product.prototype, "ratings", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.BOOLEAN,
        defaultValue: false,
    }),
    __metadata("design:type", Boolean)
], Product.prototype, "in_wishlist", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => review_entity_1.Review, 'product_id'),
    __metadata("design:type", Array)
], Product.prototype, "my_review", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: false,
        defaultValue: 'en',
    }),
    __metadata("design:type", String)
], Product.prototype, "language", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.TEXT,
        allowNull: true,
    }),
    __metadata("design:type", String)
], Product.prototype, "translated_languages", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: true,
    }),
    __metadata("design:type", String)
], Product.prototype, "visibility", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.DATE,
        allowNull: false,
        defaultValue: sequelize_typescript_1.DataType.NOW,
    }),
    __metadata("design:type", Date)
], Product.prototype, "created_at", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.DATE,
        allowNull: false,
        defaultValue: sequelize_typescript_1.DataType.NOW,
    }),
    __metadata("design:type", Date)
], Product.prototype, "updated_at", void 0);
Product = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: 'products',
        timestamps: true,
    })
], Product);
exports.Product = Product;
let ProductTag = class ProductTag extends sequelize_typescript_1.Model {
    static _OPENAPI_METADATA_FACTORY() {
        return { product_id: { required: true, type: () => Number }, tag_id: { required: true, type: () => Number } };
    }
};
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => Product),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        allowNull: false,
    }),
    __metadata("design:type", Number)
], ProductTag.prototype, "product_id", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => tag_entity_1.Tag),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        allowNull: false,
    }),
    __metadata("design:type", Number)
], ProductTag.prototype, "tag_id", void 0);
ProductTag = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: 'product_tags',
        timestamps: false,
    })
], ProductTag);
exports.ProductTag = ProductTag;
class OrderProductPivot {
    static _OPENAPI_METADATA_FACTORY() {
        return { variation_option_id: { required: false, type: () => Number }, order_quantity: { required: true, type: () => Number }, unit_price: { required: true, type: () => Number }, subtotal: { required: true, type: () => Number } };
    }
}
exports.OrderProductPivot = OrderProductPivot;
class Variation {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => Number }, title: { required: true, type: () => String }, price: { required: true, type: () => Number }, sku: { required: true, type: () => String }, is_disable: { required: true, type: () => Boolean }, sale_price: { required: false, type: () => Number }, quantity: { required: true, type: () => Number }, options: { required: true, type: () => [require("./product.entity").VariationOption] } };
    }
}
exports.Variation = Variation;
class VariationOption {
    static _OPENAPI_METADATA_FACTORY() {
        return { name: { required: true, type: () => String }, value: { required: true, type: () => String } };
    }
}
exports.VariationOption = VariationOption;
class File extends core_entity_1.CoreEntity {
    static _OPENAPI_METADATA_FACTORY() {
        return { attachment_id: { required: true, type: () => Number }, url: { required: true, type: () => String }, fileable_id: { required: true, type: () => Number } };
    }
}
exports.File = File;
//# sourceMappingURL=product.entity.js.map