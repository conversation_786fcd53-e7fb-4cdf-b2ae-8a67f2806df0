// 404 Not Found middleware
const notFound = (req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  res.status(404);
  next(error);
};

// Global error handler middleware
const errorHandler = (err, req, res, next) => {
  let statusCode = res.statusCode === 200 ? 500 : res.statusCode;
  let message = err.message;

  // Handle specific error types
  if (err.code === 'ECONNREFUSED') {
    statusCode = 503;
    message = 'Service unavailable - Unable to connect to backend service';
  }

  if (err.code === 'ETIMEDOUT') {
    statusCode = 504;
    message = 'Gateway timeout - Backend service took too long to respond';
  }

  res.status(statusCode).json({
    success: false,
    message,
    gateway: 'student-apigw',
    timestamp: new Date().toISOString(),
    stack: process.env.NODE_ENV === 'production' ? null : err.stack
  });
};

module.exports = {
  notFound,
  errorHandler
};
