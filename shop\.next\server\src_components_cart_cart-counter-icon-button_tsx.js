"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_cart_cart-counter-icon-button_tsx";
exports.ids = ["src_components_cart_cart-counter-icon-button_tsx"];
exports.modules = {

/***/ "./src/components/cart/cart-counter-icon-button.tsx":
/*!**********************************************************!*\
  !*** ./src/components/cart/cart-counter-icon-button.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_icons_cart_outlined__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/icons/cart-outlined */ \"./src/components/icons/cart-outlined.tsx\");\n/* harmony import */ var _store_drawer_atom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/drawer-atom */ \"./src/store/drawer-atom.ts\");\n/* harmony import */ var _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/quick-cart/cart.context */ \"./src/store/quick-cart/cart.context.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jotai */ \"jotai\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_store_drawer_atom__WEBPACK_IMPORTED_MODULE_2__, _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_5__, tailwind_merge__WEBPACK_IMPORTED_MODULE_6__]);\n([_store_drawer_atom__WEBPACK_IMPORTED_MODULE_2__, _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_5__, tailwind_merge__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst CartCounterIconButton = ({ className, ...rest })=>{\n    const { totalUniqueItems } = (0,_store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_3__.useCart)();\n    const [_, setDisplayCart] = (0,jotai__WEBPACK_IMPORTED_MODULE_5__.useAtom)(_store_drawer_atom__WEBPACK_IMPORTED_MODULE_2__.drawerAtom);\n    function handleCartSidebar() {\n        setDisplayCart({\n            display: true,\n            view: \"cart\"\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_6__.twMerge)(classnames__WEBPACK_IMPORTED_MODULE_4___default()(\"hidden product-cart lg:flex relative\", className)),\n        onClick: handleCartSidebar,\n        ...rest,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_cart_outlined__WEBPACK_IMPORTED_MODULE_1__.CartOutlinedIcon, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-icon-button.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            totalUniqueItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"min-w-[20px] h-5 flex items-center justify-center rounded-full bg-accent text-light text-[10px] absolute ltr:-right-1/2 rtl:-left-1/2 -top-1/2\",\n                children: totalUniqueItems\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-icon-button.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-icon-button.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CartCounterIconButton);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/cart/cart-counter-icon-button.tsx\n");

/***/ }),

/***/ "./src/components/icons/cart-outlined.tsx":
/*!************************************************!*\
  !*** ./src/components/icons/cart-outlined.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartOutlinedIcon: () => (/* binding */ CartOutlinedIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CartOutlinedIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 17.6 19.6\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            \"data-name\": \"Path 12\",\n            d: \"M12.8 8.8v-4a4 4 0 00-8 0v4m-3-2h14l1 12H.8z\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: \"1.6\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-outlined.tsx\",\n            lineNumber: 3,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-outlined.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9jYXJ0LW91dGxpbmVkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU8sTUFBTUEsbUJBQXNELENBQUNDLHNCQUNsRSw4REFBQ0M7UUFBSUMsT0FBTTtRQUE2QkMsU0FBUTtRQUFpQixHQUFHSCxLQUFLO2tCQUN2RSw0RUFBQ0k7WUFDQ0MsYUFBVTtZQUNWQyxHQUFFO1lBQ0ZDLE1BQUs7WUFDTEMsUUFBTztZQUNQQyxlQUFjO1lBQ2RDLGdCQUFlO1lBQ2ZDLGFBQVk7Ozs7Ozs7Ozs7a0JBR2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBpY2stYmF6YXIvc2hvcC8uL3NyYy9jb21wb25lbnRzL2ljb25zL2NhcnQtb3V0bGluZWQudHN4P2E5NmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IENhcnRPdXRsaW5lZEljb246IFJlYWN0LkZDPFJlYWN0LlNWR0F0dHJpYnV0ZXM8e30+PiA9IChwcm9wcykgPT4gKFxuICA8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiB2aWV3Qm94PVwiMCAwIDE3LjYgMTkuNlwiIHsuLi5wcm9wc30+XG4gICAgPHBhdGhcbiAgICAgIGRhdGEtbmFtZT1cIlBhdGggMTJcIlxuICAgICAgZD1cIk0xMi44IDguOHYtNGE0IDQgMCAwMC04IDB2NG0tMy0yaDE0bDEgMTJILjh6XCJcbiAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICBzdHJva2VXaWR0aD1cIjEuNlwiXG4gICAgLz5cbiAgPC9zdmc+XG4pO1xuIl0sIm5hbWVzIjpbIkNhcnRPdXRsaW5lZEljb24iLCJwcm9wcyIsInN2ZyIsInhtbG5zIiwidmlld0JveCIsInBhdGgiLCJkYXRhLW5hbWUiLCJkIiwiZmlsbCIsInN0cm9rZSIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/icons/cart-outlined.tsx\n");

/***/ })

};
;