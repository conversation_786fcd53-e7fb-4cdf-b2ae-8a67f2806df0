import { AttributesService } from './attributes.service';
import { CreateAttributeDto } from './dto/create-attribute.dto';
import { UpdateAttributeDto } from './dto/update-attribute.dto';
export declare class AttributesController {
    private readonly attributesService;
    constructor(attributesService: AttributesService);
    create(createAttributeDto: CreateAttributeDto): Promise<import("./entities/attribute.entity").Attribute>;
    findAll(): Promise<import("./entities/attribute.entity").Attribute[]>;
    findOne(param: string): Promise<import("./entities/attribute.entity").Attribute>;
    update(id: string, updateAttributeDto: UpdateAttributeDto): Promise<import("./entities/attribute.entity").Attribute>;
    remove(id: string): string;
}
