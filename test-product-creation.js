const http = require('http');

// Test product creation
const productData = JSON.stringify({
  name: 'Test Product',
  description: 'A test product for oneKart',
  slug: 'test-product',
  type_id: 1,
  shop_id: 3, // Ridwan's shop ID
  product_type: 'simple',
  status: 'publish',
  visibility: 'public',
  price: 99.99,
  sale_price: 79.99,
  sku: 'TEST-001',
  quantity: 100,
  in_stock: true,
  is_taxable: true,
  shipping_class_id: null,
  categories: [1],
  tags: [],
  image: {
    id: 'test-image-id',
    original: 'https://example.com/test-image.jpg',
    thumbnail: 'https://example.com/test-image-thumb.jpg'
  },
  gallery: [],
  variations: [],
  variation_options: []
});

const options = {
  hostname: 'localhost',
  port: 9000,
  path: '/api/products',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': productData.length
  }
};

console.log('Testing product creation...');
console.log('Request data:', productData);

const req = http.request(options, (res) => {
  console.log(`\nStatus: ${res.statusCode}`);
  console.log(`Headers: ${JSON.stringify(res.headers, null, 2)}`);
  
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });
  
  res.on('end', () => {
    console.log('\nResponse body:', body);
    
    if (res.statusCode >= 400) {
      console.log('\n❌ Product creation failed!');
      try {
        const errorData = JSON.parse(body);
        console.log('Error details:', JSON.stringify(errorData, null, 2));
      } catch (e) {
        console.log('Raw error response:', body);
      }
    } else {
      console.log('\n✅ Product creation successful!');
    }
  });
});

req.on('error', (e) => {
  console.error(`❌ Request error: ${e.message}`);
});

req.write(productData);
req.end();
