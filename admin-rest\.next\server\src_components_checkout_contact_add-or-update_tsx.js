"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_checkout_contact_add-or-update_tsx";
exports.ids = ["src_components_checkout_contact_add-or-update_tsx"];
exports.modules = {

/***/ "./src/components/checkout/contact/add-or-update.tsx":
/*!***********************************************************!*\
  !*** ./src/components/checkout/contact/add-or-update.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _contexts_checkout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/checkout */ \"./src/contexts/checkout.ts\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jotai */ \"jotai\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_phone_input_2__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-phone-input-2 */ \"react-phone-input-2\");\n/* harmony import */ var react_phone_input_2__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_phone_input_2__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react_phone_input_2_lib_bootstrap_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-phone-input-2/lib/bootstrap.css */ \"./node_modules/react-phone-input-2/lib/bootstrap.css\");\n/* harmony import */ var react_phone_input_2_lib_bootstrap_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react_phone_input_2_lib_bootstrap_css__WEBPACK_IMPORTED_MODULE_8__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _contexts_checkout__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _contexts_checkout__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst AddOrUpdateCheckoutContact = ()=>{\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"common\");\n    const [phone, setPhone] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [contactNumber, setContactNumber] = (0,jotai__WEBPACK_IMPORTED_MODULE_4__.useAtom)(_contexts_checkout__WEBPACK_IMPORTED_MODULE_3__.customerContactAtom);\n    function onContactUpdate() {\n        if (!phone) return;\n        setContactNumber(phone);\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen flex-col justify-center bg-light p-5 sm:p-8 md:min-h-0 md:rounded-xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"mb-5 text-center text-sm font-semibold text-heading sm:mb-6\",\n                children: [\n                    contactNumber ? t(\"text-update\") : t(\"text-add-new\"),\n                    \" \",\n                    t(\"text-contact-number\")\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_phone_input_2__WEBPACK_IMPORTED_MODULE_7___default()), {\n                        country: \"us\",\n                        value: phone,\n                        onChange: (phoneNumber)=>setPhone(`+${phoneNumber}`),\n                        inputClass: \"!p-0 !pe-4 !ps-14 !flex !items-center !w-full !appearance-none !transition !duration-300 !ease-in-out !text-heading !text-sm focus:!outline-none focus:!ring-0 !border !border-border-base !border-e-0 !rounded !rounded-e-none focus:!border-accent !h-12\",\n                        dropdownClass: \"focus:!ring-0 !border !border-border-base !shadow-350\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        className: \"!rounded-s-none\",\n                        onClick: onContactUpdate,\n                        children: t(\"text-save\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AddOrUpdateCheckoutContact);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/checkout/contact/add-or-update.tsx\n");

/***/ })

};
;