"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_checkout_contact_add-or-update_tsx";
exports.ids = ["src_components_checkout_contact_add-or-update_tsx"];
exports.modules = {

/***/ "./src/components/checkout/contact/add-or-update.tsx":
/*!***********************************************************!*\
  !*** ./src/components/checkout/contact/add-or-update.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _contexts_checkout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/checkout */ \"./src/contexts/checkout.ts\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jotai */ \"jotai\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_phone_input_2__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-phone-input-2 */ \"react-phone-input-2\");\n/* harmony import */ var react_phone_input_2__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_phone_input_2__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react_phone_input_2_lib_bootstrap_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-phone-input-2/lib/bootstrap.css */ \"./node_modules/react-phone-input-2/lib/bootstrap.css\");\n/* harmony import */ var react_phone_input_2_lib_bootstrap_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react_phone_input_2_lib_bootstrap_css__WEBPACK_IMPORTED_MODULE_8__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _contexts_checkout__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _contexts_checkout__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst AddOrUpdateCheckoutContact = ()=>{\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"common\");\n    const [phone, setPhone] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [contactNumber, setContactNumber] = (0,jotai__WEBPACK_IMPORTED_MODULE_4__.useAtom)(_contexts_checkout__WEBPACK_IMPORTED_MODULE_3__.customerContactAtom);\n    function onContactUpdate() {\n        if (!phone) return;\n        setContactNumber(phone);\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen flex-col justify-center bg-light p-5 sm:p-8 md:min-h-0 md:rounded-xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"mb-5 text-center text-sm font-semibold text-heading sm:mb-6\",\n                children: [\n                    contactNumber ? t(\"text-update\") : t(\"text-add-new\"),\n                    \" \",\n                    t(\"text-contact-number\")\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_phone_input_2__WEBPACK_IMPORTED_MODULE_7___default()), {\n                        country: \"us\",\n                        value: phone,\n                        onChange: (phoneNumber)=>setPhone(`+${phoneNumber}`),\n                        inputClass: \"!p-0 !pe-4 !ps-14 !flex !items-center !w-full !appearance-none !transition !duration-300 !ease-in-out !text-heading !text-sm focus:!outline-none focus:!ring-0 !border !border-border-base !border-e-0 !rounded !rounded-e-none focus:!border-accent !h-12\",\n                        dropdownClass: \"focus:!ring-0 !border !border-border-base !shadow-350\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        className: \"!rounded-s-none\",\n                        onClick: onContactUpdate,\n                        children: t(\"text-save\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\checkout\\\\contact\\\\add-or-update.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AddOrUpdateCheckoutContact);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/checkout/contact/add-or-update.tsx\n");

/***/ })

};
;