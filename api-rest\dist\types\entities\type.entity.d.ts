import { Model } from 'sequelize-typescript';
export declare class Banner {
    id: number;
    title?: string;
    description?: string;
    image: any;
}
export declare class TypeSettings {
    isHome: boolean;
    layoutType: string;
    productCard: string;
}
export declare class Type extends Model {
    id: number;
    name: string;
    slug: string;
    image: any;
    icon: string;
    banners?: Banner[];
    promotional_sliders?: any[];
    settings?: TypeSettings;
    language: string;
    translated_languages: string;
    created_at: Date;
    updated_at: Date;
}
