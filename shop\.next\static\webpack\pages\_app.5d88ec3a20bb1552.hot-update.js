"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/lib/constants/index.ts":
/*!************************************!*\
  !*** ./src/lib/constants/index.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_PERMISSIONS: function() { return /* binding */ AUTH_PERMISSIONS; },\n/* harmony export */   AUTH_TOKEN_KEY: function() { return /* binding */ AUTH_TOKEN_KEY; },\n/* harmony export */   CART_KEY: function() { return /* binding */ CART_KEY; },\n/* harmony export */   CHECKOUT: function() { return /* binding */ CHECKOUT; },\n/* harmony export */   CUSTOMER: function() { return /* binding */ CUSTOMER; },\n/* harmony export */   DEFAULT_LANGUAGE: function() { return /* binding */ DEFAULT_LANGUAGE; },\n/* harmony export */   EMAIL_VERIFIED: function() { return /* binding */ EMAIL_VERIFIED; },\n/* harmony export */   LIMIT: function() { return /* binding */ LIMIT; },\n/* harmony export */   LIMIT_HUNDRED: function() { return /* binding */ LIMIT_HUNDRED; },\n/* harmony export */   NEWSLETTER_POPUP_MODAL_KEY: function() { return /* binding */ NEWSLETTER_POPUP_MODAL_KEY; },\n/* harmony export */   PRODUCT_INITIAL_FETCH_LIMIT: function() { return /* binding */ PRODUCT_INITIAL_FETCH_LIMIT; },\n/* harmony export */   RESPONSIVE_WIDTH: function() { return /* binding */ RESPONSIVE_WIDTH; },\n/* harmony export */   REVIEW_POPUP_MODAL_KEY: function() { return /* binding */ REVIEW_POPUP_MODAL_KEY; },\n/* harmony export */   RTL_LANGUAGES: function() { return /* binding */ RTL_LANGUAGES; },\n/* harmony export */   SHOPS_LIMIT: function() { return /* binding */ SHOPS_LIMIT; },\n/* harmony export */   SUPER_ADMIN: function() { return /* binding */ SUPER_ADMIN; },\n/* harmony export */   TOKEN: function() { return /* binding */ TOKEN; },\n/* harmony export */   checkIsMaintenanceModeComing: function() { return /* binding */ checkIsMaintenanceModeComing; },\n/* harmony export */   checkIsMaintenanceModeStart: function() { return /* binding */ checkIsMaintenanceModeStart; },\n/* harmony export */   checkIsScrollingStart: function() { return /* binding */ checkIsScrollingStart; },\n/* harmony export */   checkIsShopMaintenanceModeComing: function() { return /* binding */ checkIsShopMaintenanceModeComing; },\n/* harmony export */   checkIsShopMaintenanceModeStart: function() { return /* binding */ checkIsShopMaintenanceModeStart; },\n/* harmony export */   getDirection: function() { return /* binding */ getDirection; },\n/* harmony export */   isMultiLangEnable: function() { return /* binding */ isMultiLangEnable; },\n/* harmony export */   setNewAddress: function() { return /* binding */ setNewAddress; }\n/* harmony export */ });\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jotai */ \"./node_modules/jotai/esm/index.mjs\");\n\nconst CART_KEY = \"onekart-cart\";\nconst TOKEN = \"token\";\nconst AUTH_TOKEN_KEY = \"auth_token\";\nconst AUTH_PERMISSIONS = \"auth_permissions\";\nconst LIMIT = 10;\nconst LIMIT_HUNDRED = 100;\nconst SUPER_ADMIN = \"super_admin\";\nconst CUSTOMER = \"customer\";\nconst CHECKOUT = \"onekart-checkout\";\nconst SHOPS_LIMIT = 20;\nconst RTL_LANGUAGES = [\n    \"ar\",\n    \"he\"\n];\nconst PRODUCT_INITIAL_FETCH_LIMIT = 30;\nvar _process_env_NEXT_PUBLIC_DEFAULT_LANGUAGE;\nconst DEFAULT_LANGUAGE = (_process_env_NEXT_PUBLIC_DEFAULT_LANGUAGE = \"en\") !== null && _process_env_NEXT_PUBLIC_DEFAULT_LANGUAGE !== void 0 ? _process_env_NEXT_PUBLIC_DEFAULT_LANGUAGE : \"en\";\nconst EMAIL_VERIFIED = \"emailVerified\";\nconst RESPONSIVE_WIDTH = 1024;\nfunction getDirection(language) {\n    if (!language) return \"ltr\";\n    return RTL_LANGUAGES.includes(language) ? \"rtl\" : \"ltr\";\n}\nconst checkIsMaintenanceModeComing = (0,jotai__WEBPACK_IMPORTED_MODULE_0__.atom)(false);\nconst checkIsMaintenanceModeStart = (0,jotai__WEBPACK_IMPORTED_MODULE_0__.atom)(false);\nconst checkIsShopMaintenanceModeComing = (0,jotai__WEBPACK_IMPORTED_MODULE_0__.atom)(false);\nconst checkIsShopMaintenanceModeStart = (0,jotai__WEBPACK_IMPORTED_MODULE_0__.atom)(false);\nconst checkIsScrollingStart = (0,jotai__WEBPACK_IMPORTED_MODULE_0__.atom)(false);\nconst setNewAddress = (0,jotai__WEBPACK_IMPORTED_MODULE_0__.atom)([]);\nconst isMultiLangEnable =  false && 0;\nconst NEWSLETTER_POPUP_MODAL_KEY = \"SEEN_POPUP\";\nconst REVIEW_POPUP_MODAL_KEY = \"SEEN_REVIEW_POPUP\";\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/constants/index.ts\n"));

/***/ })

});