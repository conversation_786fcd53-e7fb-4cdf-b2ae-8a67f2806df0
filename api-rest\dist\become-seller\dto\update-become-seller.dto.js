"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateBecomeSellerDto = void 0;
const openapi = require("@nestjs/swagger");
const mapped_types_1 = require("@nestjs/mapped-types");
const create_become_seller_dto_1 = require("./create-become-seller.dto");
class UpdateBecomeSellerDto extends (0, mapped_types_1.PartialType)(create_become_seller_dto_1.CreateBecomeSellerDto) {
    static _OPENAPI_METADATA_FACTORY() {
        return {};
    }
}
exports.UpdateBecomeSellerDto = UpdateBecomeSellerDto;
//# sourceMappingURL=update-become-seller.dto.js.map