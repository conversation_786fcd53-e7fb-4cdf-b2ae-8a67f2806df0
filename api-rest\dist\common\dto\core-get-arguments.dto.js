"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoreGetArguments = void 0;
const openapi = require("@nestjs/swagger");
class CoreGetArguments {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => Number }, slug: { required: false, type: () => String } };
    }
}
exports.CoreGetArguments = CoreGetArguments;
//# sourceMappingURL=core-get-arguments.dto.js.map