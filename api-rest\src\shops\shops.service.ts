import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreateShopDto } from './dto/create-shop.dto';
import { UpdateShopDto } from './dto/update-shop.dto';
import { Shop } from './entities/shop.entity';
import Fuse from 'fuse.js';
import { GetShopsDto, ShopPaginator } from './dto/get-shops.dto';
import { paginate } from 'src/common/pagination/paginate';
import { GetStaffsDto } from './dto/get-staffs.dto';
@Injectable()
export class ShopsService {
  constructor(
    @InjectModel(Shop)
    private shopModel: typeof Shop,
  ) {}

  async create(createShopDto: CreateShopDto): Promise<Shop> {
    try {
      // Ensure owner_id is provided, default to 1 for demo purposes
      const shopData = {
        ...createShopDto,
        owner_id: createShopDto.owner_id || 1, // Default to user ID 1 if not provided
        is_active:
          createShopDto.is_active !== undefined
            ? createShopDto.is_active
            : true,
        slug: createShopDto.slug || this.generateSlug(createShopDto.name),
      };

      console.log(
        'Creating shop with data:',
        JSON.stringify(shopData, null, 2),
      );

      const shop = await this.shopModel.create(shopData as any);
      console.log('Shop created successfully:', shop.id);

      return shop;
    } catch (error) {
      console.error('Error creating shop:', error);
      throw error;
    }
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }

  async getShops({ search, limit, page }: GetShopsDto): Promise<ShopPaginator> {
    if (!page) page = 1;
    const offset = (page - 1) * limit;

    // TODO: Implement proper Sequelize query
    const { count, rows: data } = await this.shopModel.findAndCountAll({
      limit,
      offset,
    });
    // TODO: Implement search functionality

    const url = `/shops?search=${search}&limit=${limit}`;
    return {
      data,
      ...paginate(count, page, limit, data.length, url),
    };
  }

  async getNewShops({
    search,
    limit,
    page,
  }: GetShopsDto): Promise<ShopPaginator> {
    if (!page) page = 1;
    const offset = (page - 1) * limit;

    // TODO: Implement proper Sequelize query for inactive shops
    const { count, rows: data } = await this.shopModel.findAndCountAll({
      where: { is_active: false },
      limit,
      offset,
    });

    // TODO: Implement search functionality

    const url = `/new-shops?search=${search}&limit=${limit}`;
    return {
      data,
      ...paginate(count, page, limit, data.length, url),
    };
  }

  async getStaffs({ shop_id, limit, page }: GetStaffsDto) {
    // TODO: Implement proper staff retrieval with Sequelize
    // This would require a separate Staff model and relationship
    const url = `/staffs?limit=${limit}`;
    return {
      data: [],
      ...paginate(0, page, limit, 0, url),
    };
  }

  async getShop(slug: string): Promise<Shop | null> {
    return this.shopModel.findOne({ where: { slug } });
  }

  async getNearByShop(lat: string, lng: string): Promise<Shop[]> {
    // TODO: Implement geolocation-based shop search
    return this.shopModel.findAll({ limit: 10 });
  }

  async update(id: number, updateShopDto: UpdateShopDto): Promise<Shop | null> {
    await this.shopModel.update(updateShopDto as any, { where: { id } });
    return this.shopModel.findByPk(id);
  }

  approve(id: number) {
    return `This action removes a #${id} shop`;
  }

  remove(id: number) {
    return `This action removes a #${id} shop`;
  }

  async disapproveShop(id: number): Promise<Shop | null> {
    await this.shopModel.update({ is_active: false }, { where: { id } });
    return this.shopModel.findByPk(id);
  }

  async approveShop(id: number): Promise<Shop | null> {
    await this.shopModel.update({ is_active: true }, { where: { id } });
    return this.shopModel.findByPk(id);
  }
}
