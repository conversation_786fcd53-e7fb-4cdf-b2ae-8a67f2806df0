import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreateShopDto } from './dto/create-shop.dto';
import { UpdateShopDto } from './dto/update-shop.dto';
import { Shop } from './entities/shop.entity';
import Fuse from 'fuse.js';
import { GetShopsDto, ShopPaginator } from './dto/get-shops.dto';
import { paginate } from 'src/common/pagination/paginate';
import { GetStaffsDto } from './dto/get-staffs.dto';
@Injectable()
export class ShopsService {
  constructor(
    @InjectModel(Shop)
    private shopModel: typeof Shop,
  ) {}

  async create(createShopDto: CreateShopDto): Promise<Shop> {
    return this.shopModel.create(createShopDto as any);
  }

  async getShops({ search, limit, page }: GetShopsDto): Promise<ShopPaginator> {
    if (!page) page = 1;
    const offset = (page - 1) * limit;

    // TODO: Implement proper Sequelize query
    const { count, rows: data } = await this.shopModel.findAndCountAll({
      limit,
      offset,
    });
    // TODO: Implement search functionality

    const url = `/shops?search=${search}&limit=${limit}`;
    return {
      data,
      ...paginate(count, page, limit, data.length, url),
    };
  }

  async getNewShops({
    search,
    limit,
    page,
  }: GetShopsDto): Promise<ShopPaginator> {
    if (!page) page = 1;
    const offset = (page - 1) * limit;

    // TODO: Implement proper Sequelize query for inactive shops
    const { count, rows: data } = await this.shopModel.findAndCountAll({
      where: { is_active: false },
      limit,
      offset,
    });

    // TODO: Implement search functionality

    const url = `/new-shops?search=${search}&limit=${limit}`;
    return {
      data,
      ...paginate(count, page, limit, data.length, url),
    };
  }

  getStaffs({ shop_id, limit, page }: GetStaffsDto) {
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    let staffs: Shop['staffs'] = [];
    if (shop_id) {
      staffs = this.shops.find((p) => p.id === Number(shop_id))?.staffs ?? [];
    }
    const results = staffs?.slice(startIndex, endIndex);
    const url = `/staffs?limit=${limit}`;

    return {
      data: results,
      ...paginate(staffs?.length, page, limit, results?.length, url),
    };
  }

  async getShop(slug: string): Promise<Shop | null> {
    return this.shopModel.findOne({ where: { slug } });
  }

  getNearByShop(lat: string, lng: string) {
    return nearShops;
  }

  async update(id: number, updateShopDto: UpdateShopDto): Promise<Shop | null> {
    await this.shopModel.update(updateShopDto as any, { where: { id } });
    return this.shopModel.findByPk(id);
  }

  approve(id: number) {
    return `This action removes a #${id} shop`;
  }

  remove(id: number) {
    return `This action removes a #${id} shop`;
  }

  disapproveShop(id: number) {
    const shop = this.shops.find((s) => s.id === Number(id));
    shop.is_active = false;

    return shop;
  }

  approveShop(id: number) {
    const shop = this.shops.find((s) => s.id === Number(id));
    shop.is_active = true;

    return shop;
  }
}
