import { SortOrder } from 'src/common/dto/generic-conditions.dto';
import { PaginationArgs } from 'src/common/dto/pagination-args.dto';
import { Paginator } from 'src/common/dto/paginator.dto';
import { OrderStatus } from '../entities/order-status.entity';
export declare class OrderStatusPaginator extends Paginator<OrderStatus> {
    data: OrderStatus[];
}
export declare class GetOrderStatusesDto extends PaginationArgs {
    orderBy?: QueryOrderStatusesOrderByColumn;
    sortedBy?: SortOrder;
    search?: string;
    language?: string;
}
export declare enum QueryOrderStatusesOrderByColumn {
    CREATED_AT = "CREATED_AT",
    NAME = "NAME",
    UPDATED_AT = "UPDATED_AT",
    SERIAL = "SERIAL"
}
