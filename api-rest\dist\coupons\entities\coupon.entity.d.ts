import { Model } from 'sequelize-typescript';
import { Shop } from 'src/shops/entities/shop.entity';
export declare enum CouponType {
    FIXED_COUPON = "fixed",
    PERCENTAGE_COUPON = "percentage",
    FREE_SHIPPING_COUPON = "free_shipping",
    DEFAULT_COUPON = "fixed"
}
export declare class Coupon extends Model {
    id: number;
    code: string;
    description?: string;
    minimum_cart_amount: number;
    orders?: any[];
    type: CouponType;
    image: any;
    is_valid: boolean;
    amount: number;
    active_from: Date;
    expire_at: Date;
    language: string;
    translated_languages: string[];
    target?: boolean;
    shop_id?: number;
    shop?: Shop;
    is_approve?: boolean;
    created_at: Date;
    updated_at: Date;
}
