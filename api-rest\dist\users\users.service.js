"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const user_entity_1 = require("./entities/user.entity");
const profile_entity_1 = require("./entities/profile.entity");
const sequelize_2 = require("sequelize");
const paginate_1 = require("../common/pagination/paginate");
let UsersService = class UsersService {
    constructor(userModel, permissionModel, profileModel) {
        this.userModel = userModel;
        this.permissionModel = permissionModel;
        this.profileModel = profileModel;
    }
    async create(createUserDto) {
        return this.userModel.create(Object.assign({}, createUserDto));
    }
    async getUsers({ text, limit, page, search, }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const whereClause = {};
        if (text === null || text === void 0 ? void 0 : text.replace(/%/g, '')) {
            whereClause.name = {
                [sequelize_2.Op.iLike]: `%${text}%`,
            };
        }
        if (search) {
            const parseSearchParams = search.split(';');
            for (const searchParam of parseSearchParams) {
                const [key, value] = searchParam.split(':');
                if (key !== 'slug' && key in this.userModel.rawAttributes) {
                    whereClause[key] = {
                        [sequelize_2.Op.iLike]: `%${value}%`,
                    };
                }
            }
        }
        const { count, rows } = await this.userModel.findAndCountAll({
            where: whereClause,
            include: [
                { model: profile_entity_1.Profile, as: 'profile' },
                { model: user_entity_1.Permission, as: 'permissions' },
            ],
            limit,
            offset,
            order: [['created_at', 'DESC']],
        });
        const url = `/users?limit=${limit}`;
        const totalPages = Math.ceil(count / limit);
        return {
            data: rows,
            count,
            current_page: page,
            firstItem: offset + 1,
            lastItem: Math.min(offset + limit, count),
            last_page: totalPages,
            per_page: limit,
            total: count,
            first_page_url: `${url}&page=1`,
            last_page_url: `${url}&page=${totalPages}`,
            next_page_url: page < totalPages ? `${url}&page=${page + 1}` : null,
            prev_page_url: page > 1 ? `${url}&page=${page - 1}` : null,
        };
    }
    async getUsersNotify({ limit }) {
        return this.userModel.findAll({
            limit,
            order: [['created_at', 'DESC']],
            include: [{ model: profile_entity_1.Profile, as: 'profile' }],
        });
    }
    async findOne(id) {
        return this.userModel.findByPk(id, {
            include: [
                { model: profile_entity_1.Profile, as: 'profile' },
                { model: user_entity_1.Permission, as: 'permissions' },
            ],
        });
    }
    async update(id, updateUserDto) {
        const [affectedCount, updatedUsers] = await this.userModel.update(Object.assign({}, updateUserDto), { where: { id }, returning: true });
        return [affectedCount, updatedUsers];
    }
    async remove(id) {
        return this.userModel.destroy({ where: { id } });
    }
    async makeAdmin(user_id) {
        return this.userModel.findByPk(Number(user_id));
    }
    async banUser(id) {
        const user = await this.userModel.findByPk(id);
        if (user) {
            user.is_active = !user.is_active;
            await user.save();
        }
        return user;
    }
    async activeUser(id) {
        const user = await this.userModel.findByPk(id);
        if (!user) {
            throw new Error('User not found');
        }
        user.is_active = !user.is_active;
        await user.save();
        return user;
    }
    async getAdmin({ text, limit, page, search, }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const whereClause = {};
        if (text === null || text === void 0 ? void 0 : text.replace(/%/g, '')) {
            whereClause[sequelize_2.Op.or] = [
                { name: { [sequelize_2.Op.iLike]: `%${text}%` } },
                { email: { [sequelize_2.Op.iLike]: `%${text}%` } },
            ];
        }
        const { count, rows: data } = await this.userModel.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: user_entity_1.Permission,
                    where: { name: 'super_admin' },
                    required: true,
                },
            ],
            limit,
            offset,
            order: [['created_at', 'DESC']],
        });
        const url = `/admin/list?limit=${limit}`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
    async getVendors({ text, limit, page, search, }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const whereClause = {};
        if (text === null || text === void 0 ? void 0 : text.replace(/%/g, '')) {
            whereClause[sequelize_2.Op.or] = [
                { name: { [sequelize_2.Op.iLike]: `%${text}%` } },
                { email: { [sequelize_2.Op.iLike]: `%${text}%` } },
            ];
        }
        const { count, rows: data } = await this.userModel.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: user_entity_1.Permission,
                    where: { name: 'store_owner' },
                    required: true,
                },
            ],
            limit,
            offset,
            order: [['created_at', 'DESC']],
        });
        const url = `/vendors/list?limit=${limit}`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
    async getAllCustomers({ text, limit, page, search, }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const whereClause = {};
        if (text === null || text === void 0 ? void 0 : text.replace(/%/g, '')) {
            whereClause[sequelize_2.Op.or] = [
                { name: { [sequelize_2.Op.iLike]: `%${text}%` } },
                { email: { [sequelize_2.Op.iLike]: `%${text}%` } },
            ];
        }
        const { count, rows: data } = await this.userModel.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: user_entity_1.Permission,
                    where: { name: 'customer' },
                    required: true,
                },
            ],
            limit,
            offset,
            order: [['created_at', 'DESC']],
        });
        const url = `/customers/list?limit=${limit}`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
    async getMyStaffs({ text, limit, page, search, }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const whereClause = {};
        if (text === null || text === void 0 ? void 0 : text.replace(/%/g, '')) {
            whereClause[sequelize_2.Op.or] = [
                { name: { [sequelize_2.Op.iLike]: `%${text}%` } },
                { email: { [sequelize_2.Op.iLike]: `%${text}%` } },
            ];
        }
        const { count, rows: data } = await this.userModel.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: user_entity_1.Permission,
                    where: { name: 'staff' },
                    required: true,
                },
            ],
            limit,
            offset,
            order: [['created_at', 'DESC']],
        });
        const url = `/my-staffs/list?limit=${limit}`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
    async getAllStaffs({ text, limit, page, search, }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const whereClause = {};
        if (text === null || text === void 0 ? void 0 : text.replace(/%/g, '')) {
            whereClause[sequelize_2.Op.or] = [
                { name: { [sequelize_2.Op.iLike]: `%${text}%` } },
                { email: { [sequelize_2.Op.iLike]: `%${text}%` } },
            ];
        }
        const { count, rows: data } = await this.userModel.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: user_entity_1.Permission,
                    where: { name: 'staff' },
                    required: true,
                },
            ],
            limit,
            offset,
            order: [['created_at', 'DESC']],
        });
        const url = `/all-staffs/list?limit=${limit}`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
};
UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(user_entity_1.User)),
    __param(1, (0, sequelize_1.InjectModel)(user_entity_1.Permission)),
    __param(2, (0, sequelize_1.InjectModel)(profile_entity_1.Profile)),
    __metadata("design:paramtypes", [Object, Object, Object])
], UsersService);
exports.UsersService = UsersService;
//# sourceMappingURL=users.service.js.map