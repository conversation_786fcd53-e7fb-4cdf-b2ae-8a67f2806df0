"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OwnershipTransferService = void 0;
const common_1 = require("@nestjs/common");
const ownership_transfer_json_1 = __importDefault(require("../db/pickbazar/ownership-transfer.json"));
const class_transformer_1 = require("class-transformer");
const fuse_js_1 = __importDefault(require("fuse.js"));
const paginate_1 = require("../common/pagination/paginate");
const ownership_transfer_entity_1 = require("./entities/ownership-transfer.entity");
const ownershipTransfer = (0, class_transformer_1.plainToClass)(ownership_transfer_entity_1.OwnershipTransfer, ownership_transfer_json_1.default);
const options = {
    keys: ['name'],
    threshold: 0.3,
};
const fuse = new fuse_js_1.default(ownershipTransfer, options);
let OwnershipTransferService = class OwnershipTransferService {
    constructor() {
        this.ownershipTransfer = ownershipTransfer;
    }
    create(createOwnershipTransferDto) {
        return this.ownershipTransfer[0];
    }
    findAll({ search, limit, page }) {
        var _a;
        if (!page)
            page = 1;
        if (!limit)
            limit = 10;
        const startIndex = (page - 1) * limit;
        const endIndex = page * limit;
        let data = this.ownershipTransfer;
        if (search) {
            const parseSearchParams = search.split(';');
            for (const searchParam of parseSearchParams) {
                const [key, value] = searchParam.split(':');
                data = (_a = fuse.search(value)) === null || _a === void 0 ? void 0 : _a.map(({ item }) => item);
            }
        }
        let results = data.slice(startIndex, endIndex);
        if (results.length == 0) {
            results = this.ownershipTransfer.slice(0, limit);
        }
        const url = `/refund-policies?search=${search}&limit=${limit}`;
        return Object.assign({ data: results }, (0, paginate_1.paginate)(data.length, page, limit, results.length, url));
    }
    getOwnershipTransfer(param, language) {
        return this.ownershipTransfer.find((p) => p.name === param);
    }
    update(id, updateOwnershipTransferDto) {
        return this.ownershipTransfer[0];
    }
    remove(id) {
        return `This action removes a #${id} Ownership Transfer`;
    }
};
OwnershipTransferService = __decorate([
    (0, common_1.Injectable)()
], OwnershipTransferService);
exports.OwnershipTransferService = OwnershipTransferService;
//# sourceMappingURL=ownership-transfer.service.js.map