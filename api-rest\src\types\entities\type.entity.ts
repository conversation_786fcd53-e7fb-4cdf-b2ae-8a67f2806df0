import { Column, Model, Table, DataType } from 'sequelize-typescript';

export class Banner {
  id: number;
  title?: string;
  description?: string;
  image: any; // Attachment
}

export class TypeSettings {
  isHome: boolean;
  layoutType: string;
  productCard: string;
}

@Table({
  tableName: 'types',
  timestamps: true,
})
export class Type extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  slug: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  image: any; // Attachment

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  icon: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  banners?: Banner[];

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  promotional_sliders?: any[]; // Attachment[]

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  settings?: TypeSettings;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'en',
  })
  language: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  translated_languages: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}
