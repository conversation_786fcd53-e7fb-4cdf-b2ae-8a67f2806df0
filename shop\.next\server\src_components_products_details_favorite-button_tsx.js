"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_products_details_favorite-button_tsx";
exports.ids = ["src_components_products_details_favorite-button_tsx"];
exports.modules = {

/***/ "./src/components/icons/heart-fill.tsx":
/*!*********************************************!*\
  !*** ./src/components/icons/heart-fill.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeartFillIcon: () => (/* binding */ HeartFillIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst HeartFillIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        viewBox: \"0 -28 512 512\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"1em\",\n        height: \"1em\",\n        fill: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M471.383 44.578C444.879 15.832 408.512 0 368.973 0c-29.555 0-56.621 9.344-80.45 27.77C276.5 37.07 265.605 48.45 256 61.73c-9.602-13.277-20.5-24.66-32.527-33.96C199.648 9.344 172.582 0 143.027 0c-39.539 0-75.91 15.832-102.414 44.578C14.426 72.988 0 111.801 0 153.871c0 43.3 16.137 82.938 50.781 124.742 30.992 37.395 75.535 75.356 127.117 119.313 17.614 15.012 37.579 32.027 58.309 50.152A30.023 30.023 0 0 0 256 455.516c7.285 0 14.316-2.641 19.785-7.43 20.73-18.129 40.707-35.152 58.328-50.172 51.575-43.95 96.117-81.906 127.11-119.305C495.867 236.81 512 197.172 512 153.867c0-42.066-14.426-80.879-40.617-109.289zm0 0\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-fill.tsx\",\n            lineNumber: 10,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-fill.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9oZWFydC1maWxsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU8sTUFBTUEsZ0JBQW1ELENBQUNDLHNCQUMvRCw4REFBQ0M7UUFDQ0MsU0FBUTtRQUNSQyxPQUFNO1FBQ05DLE9BQU07UUFDTkMsUUFBTztRQUNQQyxNQUFLO1FBQ0osR0FBR04sS0FBSztrQkFFVCw0RUFBQ087WUFBS0MsR0FBRTs7Ozs7Ozs7OztrQkFFViIsInNvdXJjZXMiOlsid2VicGFjazovL0BwaWNrLWJhemFyL3Nob3AvLi9zcmMvY29tcG9uZW50cy9pY29ucy9oZWFydC1maWxsLnRzeD8xOGIwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBIZWFydEZpbGxJY29uOiBSZWFjdC5GQzxSZWFjdC5TVkdBdHRyaWJ1dGVzPHt9Pj4gPSAocHJvcHMpID0+IChcbiAgPHN2Z1xuICAgIHZpZXdCb3g9XCIwIC0yOCA1MTIgNTEyXCJcbiAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICB3aWR0aD1cIjFlbVwiXG4gICAgaGVpZ2h0PVwiMWVtXCJcbiAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICB7Li4ucHJvcHN9XG4gID5cbiAgICA8cGF0aCBkPVwiTTQ3MS4zODMgNDQuNTc4QzQ0NC44NzkgMTUuODMyIDQwOC41MTIgMCAzNjguOTczIDBjLTI5LjU1NSAwLTU2LjYyMSA5LjM0NC04MC40NSAyNy43N0MyNzYuNSAzNy4wNyAyNjUuNjA1IDQ4LjQ1IDI1NiA2MS43M2MtOS42MDItMTMuMjc3LTIwLjUtMjQuNjYtMzIuNTI3LTMzLjk2QzE5OS42NDggOS4zNDQgMTcyLjU4MiAwIDE0My4wMjcgMGMtMzkuNTM5IDAtNzUuOTEgMTUuODMyLTEwMi40MTQgNDQuNTc4QzE0LjQyNiA3Mi45ODggMCAxMTEuODAxIDAgMTUzLjg3MWMwIDQzLjMgMTYuMTM3IDgyLjkzOCA1MC43ODEgMTI0Ljc0MiAzMC45OTIgMzcuMzk1IDc1LjUzNSA3NS4zNTYgMTI3LjExNyAxMTkuMzEzIDE3LjYxNCAxNS4wMTIgMzcuNTc5IDMyLjAyNyA1OC4zMDkgNTAuMTUyQTMwLjAyMyAzMC4wMjMgMCAwIDAgMjU2IDQ1NS41MTZjNy4yODUgMCAxNC4zMTYtMi42NDEgMTkuNzg1LTcuNDMgMjAuNzMtMTguMTI5IDQwLjcwNy0zNS4xNTIgNTguMzI4LTUwLjE3MiA1MS41NzUtNDMuOTUgOTYuMTE3LTgxLjkwNiAxMjcuMTEtMTE5LjMwNUM0OTUuODY3IDIzNi44MSA1MTIgMTk3LjE3MiA1MTIgMTUzLjg2N2MwLTQyLjA2Ni0xNC40MjYtODAuODc5LTQwLjYxNy0xMDkuMjg5em0wIDBcIiAvPlxuICA8L3N2Zz5cbik7XG4iXSwibmFtZXMiOlsiSGVhcnRGaWxsSWNvbiIsInByb3BzIiwic3ZnIiwidmlld0JveCIsInhtbG5zIiwid2lkdGgiLCJoZWlnaHQiLCJmaWxsIiwicGF0aCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/heart-fill.tsx\n");

/***/ }),

/***/ "./src/components/icons/heart-ghost.tsx":
/*!**********************************************!*\
  !*** ./src/components/icons/heart-ghost.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeartGhostIcon: () => (/* binding */ HeartGhostIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst HeartGhostIcon = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"1em\",\n        height: \"1em\",\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                opacity: 0.2,\n                d: \"M14.5 5.875C14.5 10 8 13.5 8 13.5S1.5 10 1.5 5.875A3.375 3.375 0 014.875 2.5c1.412 0 2.621.77 3.125 2 .504-1.23 1.713-2 3.125-2A3.375 3.375 0 0114.5 5.875z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-ghost.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M11.125 2C9.835 2 8.705 2.555 8 3.493 7.296 2.555 6.166 2 4.875 2A3.88 3.88 0 001 5.875c0 4.375 6.487 7.916 6.763 8.063a.5.5 0 00.474 0C8.513 13.79 15 10.25 15 5.874A3.88 3.88 0 0011.125 2zM8 12.925c-1.141-.665-6-3.694-6-7.05A2.879 2.879 0 014.875 3c1.216 0 2.236.647 2.662 1.688a.5.5 0 00.926 0C8.889 3.646 9.909 3 11.125 3A2.879 2.879 0 0114 5.875c0 3.35-4.86 6.384-6 7.05z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-ghost.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-ghost.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/heart-ghost.tsx\n");

/***/ }),

/***/ "./src/components/icons/heart-outline.tsx":
/*!************************************************!*\
  !*** ./src/components/icons/heart-outline.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeartOutlineIcon: () => (/* binding */ HeartOutlineIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst HeartOutlineIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        viewBox: \"0 -28 512.001 512\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"1em\",\n        height: \"1em\",\n        fill: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M256 455.516c-7.29 0-14.316-2.641-19.793-7.438-20.684-18.086-40.625-35.082-58.219-50.074l-.09-.078c-51.582-43.957-96.125-81.918-127.117-119.313C16.137 236.81 0 197.172 0 153.871c0-42.07 14.426-80.883 40.617-109.293C67.121 15.832 103.488 0 143.031 0c29.555 0 56.621 9.344 80.446 27.77C235.5 37.07 246.398 48.453 256 61.73c9.605-13.277 20.5-24.66 32.527-33.96C312.352 9.344 339.418 0 368.973 0c39.539 0 75.91 15.832 102.414 44.578C497.578 72.988 512 111.801 512 153.871c0 43.3-16.133 82.938-50.777 124.738-30.993 37.399-75.532 75.356-127.106 119.309-17.625 15.016-37.597 32.039-58.328 50.168a30.046 30.046 0 0 1-19.789 7.43zM143.031 29.992c-31.066 0-59.605 12.399-80.367 34.914-21.07 22.856-32.676 54.45-32.676 88.965 0 36.418 13.535 68.988 43.883 105.606 29.332 35.394 72.961 72.574 123.477 115.625l.093.078c17.66 15.05 37.68 32.113 58.516 50.332 20.961-18.254 41.012-35.344 58.707-50.418 50.512-43.051 94.137-80.223 123.469-115.617 30.344-36.618 43.879-69.188 43.879-105.606 0-34.516-11.606-66.11-32.676-88.965-20.758-22.515-49.3-34.914-80.363-34.914-22.758 0-43.653 7.235-62.102 21.5-16.441 12.719-27.894 28.797-34.61 40.047-3.452 5.785-9.53 9.238-16.261 9.238s-12.809-3.453-16.262-9.238c-6.71-11.25-18.164-27.328-34.61-40.047-18.448-14.265-39.343-21.5-62.097-21.5zm0 0\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-outline.tsx\",\n            lineNumber: 10,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-outline.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/components/icons/heart-outline.tsx\n");

/***/ }),

/***/ "./src/components/products/details/favorite-button.tsx":
/*!*************************************************************!*\
  !*** ./src/components/products/details/favorite-button.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_icons_heart_fill__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/icons/heart-fill */ \"./src/components/icons/heart-fill.tsx\");\n/* harmony import */ var _components_icons_heart_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons/heart-outline */ \"./src/components/icons/heart-outline.tsx\");\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _framework_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/framework/user */ \"./src/framework/rest/user.ts\");\n/* harmony import */ var _framework_wishlist__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/framework/wishlist */ \"./src/framework/rest/wishlist.ts\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\n/* harmony import */ var _components_icons_heart_ghost__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/icons/heart-ghost */ \"./src/components/icons/heart-ghost.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_framework_user__WEBPACK_IMPORTED_MODULE_5__, _framework_wishlist__WEBPACK_IMPORTED_MODULE_6__, tailwind_merge__WEBPACK_IMPORTED_MODULE_8__]);\n([_framework_user__WEBPACK_IMPORTED_MODULE_5__, _framework_wishlist__WEBPACK_IMPORTED_MODULE_6__, tailwind_merge__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nfunction FavoriteButton({ productId, className, variant = \"default\" }) {\n    const { isAuthorized } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_5__.useUser)();\n    const { toggleWishlist, isLoading: adding } = (0,_framework_wishlist__WEBPACK_IMPORTED_MODULE_6__.useToggleWishlist)(productId);\n    const { inWishlist, isLoading: checking } = (0,_framework_wishlist__WEBPACK_IMPORTED_MODULE_6__.useInWishlist)({\n        enabled: isAuthorized,\n        product_id: productId\n    });\n    const { openModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__.useModalAction)();\n    function toggle() {\n        if (!isAuthorized) {\n            openModal(\"LOGIN_VIEW\");\n            return;\n        }\n        toggleWishlist({\n            product_id: productId\n        });\n    }\n    const isLoading = adding || checking;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_8__.twMerge)(classnames__WEBPACK_IMPORTED_MODULE_7___default()(\"mt-0.5 flex h-10 w-10 shrink-0 items-center justify-center rounded-full border border-gray-300\", variant === \"minimal\" ? \"bg-black bg-opacity-20\" : \"\", className)),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                simple: true,\n                className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(variant === \"default\" ? \"h-5 w-5\" : \"h-4 w-4\")\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: \"button\",\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_8__.twMerge)(classnames__WEBPACK_IMPORTED_MODULE_7___default()(\"mt-0.5 flex h-10 w-10 shrink-0 items-center justify-center rounded-full border border-gray-300 text-xl text-accent transition-colors\", inWishlist ? \"border-accent\" : \"border-gray-300\", variant === \"minimal\" && (inWishlist ? \"bg-accent text-white\" : \"bg-black bg-opacity-20 text-white\"), className)),\n        onClick: toggle,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: variant === \"default\" ? inWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_heart_fill__WEBPACK_IMPORTED_MODULE_1__.HeartFillIcon, {}, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n                lineNumber: 73,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_heart_outline__WEBPACK_IMPORTED_MODULE_2__.HeartOutlineIcon, {}, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n                lineNumber: 75,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_heart_ghost__WEBPACK_IMPORTED_MODULE_9__.HeartGhostIcon, {}, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n                lineNumber: 78,\n                columnNumber: 11\n            }, this)\n        }, void 0, false)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FavoriteButton);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/details/favorite-button.tsx\n");

/***/ }),

/***/ "./src/framework/rest/wishlist.ts":
/*!****************************************!*\
  !*** ./src/framework/rest/wishlist.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInWishlist: () => (/* binding */ useInWishlist),\n/* harmony export */   useRemoveFromWishlist: () => (/* binding */ useRemoveFromWishlist),\n/* harmony export */   useToggleWishlist: () => (/* binding */ useToggleWishlist),\n/* harmony export */   useWishlist: () => (/* binding */ useWishlist)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./client */ \"./src/framework/rest/client/index.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/framework/rest/client/api-endpoints.ts\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/data-mappers */ \"./src/framework/rest/utils/data-mappers.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__, react_toastify__WEBPACK_IMPORTED_MODULE_3__, _client__WEBPACK_IMPORTED_MODULE_4__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__]);\n([axios__WEBPACK_IMPORTED_MODULE_0__, react_toastify__WEBPACK_IMPORTED_MODULE_3__, _client__WEBPACK_IMPORTED_MODULE_4__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction useToggleWishlist(product_id) {\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"common\");\n    const { mutate: toggleWishlist, isLoading, isSuccess } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"].wishlist.toggle, {\n        onSuccess: (data)=>{\n            queryClient.setQueryData([\n                `${_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.WISHLIST}/in_wishlist`,\n                product_id\n            ], (old)=>!old);\n        },\n        onError: (error)=>{\n            if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(`${t(error.response?.data.message)}`);\n            }\n        }\n    });\n    return {\n        toggleWishlist,\n        isLoading,\n        isSuccess\n    };\n}\nfunction useRemoveFromWishlist() {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"common\");\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const { mutate: removeFromWishlist, isLoading, isSuccess } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"].wishlist.remove, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(`${t(\"text-removed-from-wishlist\")}`);\n            queryClient.refetchQueries([\n                _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.USERS_WISHLIST\n            ]);\n        },\n        onError: (error)=>{\n            if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(`${t(error.response?.data.message)}`);\n            }\n        }\n    });\n    return {\n        removeFromWishlist,\n        isLoading,\n        isSuccess\n    };\n}\nfunction useWishlist(options) {\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const formattedOptions = {\n        ...options\n    };\n    const { data, isLoading, error, fetchNextPage, hasNextPage, isFetching, isFetchingNextPage } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useInfiniteQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.USERS_WISHLIST,\n        formattedOptions\n    ], ({ queryKey, pageParam })=>_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"].wishlist.all(Object.assign({}, queryKey[1], pageParam)), {\n        getNextPageParam: ({ current_page, last_page })=>last_page > current_page && {\n                page: current_page + 1\n            }\n    });\n    function handleLoadMore() {\n        fetchNextPage();\n    }\n    return {\n        wishlists: data?.pages?.flatMap((page)=>page.data) ?? [],\n        paginatorInfo: Array.isArray(data?.pages) ? (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data?.pages[data.pages.length - 1]) : null,\n        isLoading,\n        error,\n        isFetching,\n        isLoadingMore: isFetchingNextPage,\n        loadMore: handleLoadMore,\n        hasMore: Boolean(hasNextPage)\n    };\n}\nfunction useInWishlist({ enabled, product_id }) {\n    const { data, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        `${_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.WISHLIST}/in_wishlist`,\n        product_id\n    ], ()=>_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"].wishlist.checkIsInWishlist({\n            product_id\n        }), {\n        enabled\n    });\n    return {\n        inWishlist: Boolean(data) ?? false,\n        isLoading,\n        error,\n        refetch\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/framework/rest/wishlist.ts\n");

/***/ })

};
;