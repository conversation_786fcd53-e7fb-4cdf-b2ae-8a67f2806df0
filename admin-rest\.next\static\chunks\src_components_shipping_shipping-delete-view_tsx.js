"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_shipping_shipping-delete-view_tsx"],{

/***/ "./src/components/shipping/shipping-delete-view.tsx":
/*!**********************************************************!*\
  !*** ./src/components/shipping/shipping-delete-view.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_shipping__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/shipping */ \"./src/data/shipping.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst ShippingDeleteView = ()=>{\n    _s();\n    const { mutate: deleteShippingClass, isLoading: loading } = (0,_data_shipping__WEBPACK_IMPORTED_MODULE_3__.useDeleteShippingClassMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteShippingClass({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\shipping\\\\shipping-delete-view.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ShippingDeleteView, \"1IvQNCffWE/+K7VraLVj0ItzVLU=\", false, function() {\n    return [\n        _data_shipping__WEBPACK_IMPORTED_MODULE_3__.useDeleteShippingClassMutation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction\n    ];\n});\n_c = ShippingDeleteView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ShippingDeleteView);\nvar _c;\n$RefreshReg$(_c, \"ShippingDeleteView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/shipping/shipping-delete-view.tsx\n"));

/***/ }),

/***/ "./src/data/client/shipping.ts":
/*!*************************************!*\
  !*** ./src/data/client/shipping.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shippingClient: function() { return /* binding */ shippingClient; }\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\n\n\n\nconst shippingClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS),\n    get (param) {\n        let { id } = param;\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(\"\".concat(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS, \"/\").concat(id));\n    },\n    paginated: (param)=>{\n        let { name, ...params } = param;\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    },\n    all: (param)=>{\n        let { name, ...params } = param;\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZGF0YS9jbGllbnQvc2hpcHBpbmcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQU9nRDtBQUNIO0FBQ1U7QUFFaEQsTUFBTUcsaUJBQWlCO0lBQzVCLEdBQUdGLDBEQUFXQSxDQUNaRCx5REFBYUEsQ0FBQ0ksU0FBUyxDQUN4QjtJQUNEQyxLQUFJLEtBQXNCO1lBQXRCLEVBQUVDLEVBQUUsRUFBa0IsR0FBdEI7UUFDRixPQUFPSixnRUFBVUEsQ0FBQ0csR0FBRyxDQUFXLEdBQThCQyxPQUEzQk4seURBQWFBLENBQUNJLFNBQVMsRUFBQyxLQUFNLE9BQUhFO0lBQ2hFO0lBQ0FDLFdBQVc7WUFBQyxFQUFFQyxJQUFJLEVBQUUsR0FBR0MsUUFBdUM7UUFDNUQsT0FBT1AsZ0VBQVVBLENBQUNHLEdBQUcsQ0FBb0JMLHlEQUFhQSxDQUFDSSxTQUFTLEVBQUU7WUFDaEVNLFlBQVk7WUFDWixHQUFHRCxNQUFNO1lBQ1RFLFFBQVFULGdFQUFVQSxDQUFDVSxrQkFBa0IsQ0FBQztnQkFBRUo7WUFBSztRQUMvQztJQUNGO0lBQ0FLLEtBQUs7WUFBQyxFQUFFTCxJQUFJLEVBQUUsR0FBR0MsUUFBdUM7UUFDdEQsT0FBT1AsZ0VBQVVBLENBQUNHLEdBQUcsQ0FBYUwseURBQWFBLENBQUNJLFNBQVMsRUFBRTtZQUN6RE0sWUFBWTtZQUNaLEdBQUdELE1BQU07WUFDVEUsUUFBUVQsZ0VBQVVBLENBQUNVLGtCQUFrQixDQUFDO2dCQUFFSjtZQUFLO1FBQy9DO0lBQ0Y7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9kYXRhL2NsaWVudC9zaGlwcGluZy50cz81ZDllIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XHJcbiAgU2hpcHBpbmcsXHJcbiAgU2hpcHBpbmdJbnB1dCxcclxuICBRdWVyeU9wdGlvbnMsXHJcbiAgU2hpcHBpbmdRdWVyeU9wdGlvbnMsXHJcbiAgU2hpcHBpbmdQYWdpbmF0b3IsXHJcbn0gZnJvbSAnQC90eXBlcyc7XHJcbmltcG9ydCB7IEFQSV9FTkRQT0lOVFMgfSBmcm9tICcuL2FwaS1lbmRwb2ludHMnO1xyXG5pbXBvcnQgeyBjcnVkRmFjdG9yeSB9IGZyb20gJy4vY3VyZC1mYWN0b3J5JztcclxuaW1wb3J0IHsgSHR0cENsaWVudCB9IGZyb20gJ0AvZGF0YS9jbGllbnQvaHR0cC1jbGllbnQnO1xyXG5cclxuZXhwb3J0IGNvbnN0IHNoaXBwaW5nQ2xpZW50ID0ge1xyXG4gIC4uLmNydWRGYWN0b3J5PFNoaXBwaW5nLCBRdWVyeU9wdGlvbnMsIFNoaXBwaW5nSW5wdXQ+KFxyXG4gICAgQVBJX0VORFBPSU5UUy5TSElQUElOR1NcclxuICApLFxyXG4gIGdldCh7IGlkIH06IHsgaWQ6IHN0cmluZyB9KSB7XHJcbiAgICByZXR1cm4gSHR0cENsaWVudC5nZXQ8U2hpcHBpbmc+KGAke0FQSV9FTkRQT0lOVFMuU0hJUFBJTkdTfS8ke2lkfWApO1xyXG4gIH0sXHJcbiAgcGFnaW5hdGVkOiAoeyBuYW1lLCAuLi5wYXJhbXMgfTogUGFydGlhbDxTaGlwcGluZ1F1ZXJ5T3B0aW9ucz4pID0+IHtcclxuICAgIHJldHVybiBIdHRwQ2xpZW50LmdldDxTaGlwcGluZ1BhZ2luYXRvcj4oQVBJX0VORFBPSU5UUy5TSElQUElOR1MsIHtcclxuICAgICAgc2VhcmNoSm9pbjogJ2FuZCcsXHJcbiAgICAgIC4uLnBhcmFtcyxcclxuICAgICAgc2VhcmNoOiBIdHRwQ2xpZW50LmZvcm1hdFNlYXJjaFBhcmFtcyh7IG5hbWUgfSksXHJcbiAgICB9KTtcclxuICB9LFxyXG4gIGFsbDogKHsgbmFtZSwgLi4ucGFyYW1zIH06IFBhcnRpYWw8U2hpcHBpbmdRdWVyeU9wdGlvbnM+KSA9PiB7XHJcbiAgICByZXR1cm4gSHR0cENsaWVudC5nZXQ8U2hpcHBpbmdbXT4oQVBJX0VORFBPSU5UUy5TSElQUElOR1MsIHtcclxuICAgICAgc2VhcmNoSm9pbjogJ2FuZCcsXHJcbiAgICAgIC4uLnBhcmFtcyxcclxuICAgICAgc2VhcmNoOiBIdHRwQ2xpZW50LmZvcm1hdFNlYXJjaFBhcmFtcyh7IG5hbWUgfSksXHJcbiAgICB9KTtcclxuICB9LFxyXG59O1xyXG4iXSwibmFtZXMiOlsiQVBJX0VORFBPSU5UUyIsImNydWRGYWN0b3J5IiwiSHR0cENsaWVudCIsInNoaXBwaW5nQ2xpZW50IiwiU0hJUFBJTkdTIiwiZ2V0IiwiaWQiLCJwYWdpbmF0ZWQiLCJuYW1lIiwicGFyYW1zIiwic2VhcmNoSm9pbiIsInNlYXJjaCIsImZvcm1hdFNlYXJjaFBhcmFtcyIsImFsbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/data/client/shipping.ts\n"));

/***/ }),

/***/ "./src/data/shipping.ts":
/*!******************************!*\
  !*** ./src/data/shipping.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateShippingMutation: function() { return /* binding */ useCreateShippingMutation; },\n/* harmony export */   useDeleteShippingClassMutation: function() { return /* binding */ useDeleteShippingClassMutation; },\n/* harmony export */   useShippingClassesQuery: function() { return /* binding */ useShippingClassesQuery; },\n/* harmony export */   useShippingQuery: function() { return /* binding */ useShippingQuery; },\n/* harmony export */   useUpdateShippingMutation: function() { return /* binding */ useUpdateShippingMutation; }\n/* harmony export */ });\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _client_shipping__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/shipping */ \"./src/data/client/shipping.ts\");\n\n\n\n\n\n\n\nconst useCreateShippingMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.create, {\n        onSuccess: ()=>{\n            router.push(_config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shipping.list);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS);\n        }\n    });\n};\nconst useDeleteShippingClassMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS);\n        }\n    });\n};\nconst useUpdateShippingMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.update, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS);\n        }\n    });\n};\nconst useShippingQuery = (id)=>{\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS,\n        id\n    ], ()=>_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.get({\n            id\n        }));\n};\nconst useShippingClassesQuery = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS,\n        options\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.all(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        keepPreviousData: true\n    });\n    return {\n        shippingClasses: data !== null && data !== void 0 ? data : [],\n        error,\n        loading: isLoading\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/shipping.ts\n"));

/***/ })

}]);