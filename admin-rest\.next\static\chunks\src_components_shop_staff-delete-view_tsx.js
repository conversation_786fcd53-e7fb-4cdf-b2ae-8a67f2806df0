"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_shop_staff-delete-view_tsx"],{

/***/ "./src/components/shop/staff-delete-view.tsx":
/*!***************************************************!*\
  !*** ./src/components/shop/staff-delete-view.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_staff__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/staff */ \"./src/data/staff.ts\");\n/* harmony import */ var _utils_form_error__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/form-error */ \"./src/utils/form-error.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst StaffDeleteView = ()=>{\n    _s();\n    const { mutate: removeStaffByID, isLoading: loading } = (0,_data_staff__WEBPACK_IMPORTED_MODULE_3__.useRemoveStaffMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    async function handleDelete() {\n        try {\n            removeStaffByID({\n                id: data\n            });\n            closeModal();\n        } catch (error) {\n            closeModal();\n            (0,_utils_form_error__WEBPACK_IMPORTED_MODULE_4__.getErrorMessage)(error);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\shop\\\\staff-delete-view.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StaffDeleteView, \"6a0RoQSD3oNHzgIkcnQJ5Mju+i4=\", false, function() {\n    return [\n        _data_staff__WEBPACK_IMPORTED_MODULE_3__.useRemoveStaffMutation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction\n    ];\n});\n_c = StaffDeleteView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StaffDeleteView);\nvar _c;\n$RefreshReg$(_c, \"StaffDeleteView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/shop/staff-delete-view.tsx\n"));

/***/ }),

/***/ "./src/data/client/staff.ts":
/*!**********************************!*\
  !*** ./src/data/client/staff.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   staffClient: function() { return /* binding */ staffClient; }\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\n\n\nconst staffClient = {\n    paginated: (param)=>{\n        let { ...params } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_1__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.STAFFS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_1__.HttpClient.formatSearchParams({})\n        });\n    },\n    addStaff: (variables)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_1__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.ADD_STAFF, variables);\n    },\n    removeStaff: (param)=>{\n        let { id } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_1__.HttpClient.delete(\"\".concat(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REMOVE_STAFF, \"/\").concat(id));\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/staff.ts\n"));

/***/ }),

/***/ "./src/data/staff.ts":
/*!***************************!*\
  !*** ./src/data/staff.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAddStaffMutation: function() { return /* binding */ useAddStaffMutation; },\n/* harmony export */   useRemoveStaffMutation: function() { return /* binding */ useRemoveStaffMutation; },\n/* harmony export */   useStaffsQuery: function() { return /* binding */ useStaffsQuery; }\n/* harmony export */ });\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _client_staff__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./client/staff */ \"./src/data/client/staff.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n\n\n\n\n\n\n\n\nconst useStaffsQuery = function(params) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.STAFFS,\n        params\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _client_staff__WEBPACK_IMPORTED_MODULE_3__.staffClient.paginated(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        keepPreviousData: true,\n        ...options\n    });\n    var _data_data;\n    return {\n        staffs: (_data_data = data === null || data === void 0 ? void 0 : data.data) !== null && _data_data !== void 0 ? _data_data : [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\nconst useAddStaffMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_client_staff__WEBPACK_IMPORTED_MODULE_3__.staffClient.addStaff, {\n        onSuccess: ()=>{\n            var _router_query;\n            router.push(\"/\".concat(router === null || router === void 0 ? void 0 : (_router_query = router.query) === null || _router_query === void 0 ? void 0 : _router_query.shop).concat(_config_routes__WEBPACK_IMPORTED_MODULE_7__.Routes.staff.list));\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.STAFFS);\n        }\n    });\n};\nconst useRemoveStaffMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_client_staff__WEBPACK_IMPORTED_MODULE_3__.staffClient.removeStaff, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.STAFFS);\n        }\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/staff.ts\n"));

/***/ }),

/***/ "./src/utils/form-error.tsx":
/*!**********************************!*\
  !*** ./src/utils/form-error.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getErrorMessage: function() { return /* binding */ getErrorMessage; }\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n\nfunction getErrorMessage(error) {\n    let processedError = {\n        message: \"\",\n        validation: []\n    };\n    if (error.graphQLErrors) {\n        for (const graphQLError of error.graphQLErrors){\n            if (graphQLError.extensions && graphQLError.extensions.category === \"validation\") {\n                processedError[\"message\"] = graphQLError.message;\n                processedError[\"validation\"] = graphQLError.extensions.validation;\n                return processedError;\n            } else if (graphQLError.extensions && graphQLError.extensions.category === \"authorization\") {\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_token\");\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_permissions\");\n                next_router__WEBPACK_IMPORTED_MODULE_0___default().push(\"/\");\n            }\n        }\n    }\n    processedError[\"message\"] = error.message;\n    return processedError;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvZm9ybS1lcnJvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpQztBQUNEO0FBRXpCLFNBQVNFLGdCQUFnQkMsS0FBVTtJQUN4QyxJQUFJQyxpQkFBaUI7UUFDbkJDLFNBQVM7UUFDVEMsWUFBWSxFQUFFO0lBQ2hCO0lBRUEsSUFBSUgsTUFBTUksYUFBYSxFQUFFO1FBQ3ZCLEtBQUssTUFBTUMsZ0JBQWdCTCxNQUFNSSxhQUFhLENBQUU7WUFDOUMsSUFDRUMsYUFBYUMsVUFBVSxJQUN2QkQsYUFBYUMsVUFBVSxDQUFDQyxRQUFRLEtBQUssY0FDckM7Z0JBQ0FOLGNBQWMsQ0FBQyxVQUFVLEdBQUdJLGFBQWFILE9BQU87Z0JBQ2hERCxjQUFjLENBQUMsYUFBYSxHQUFHSSxhQUFhQyxVQUFVLENBQUNILFVBQVU7Z0JBQ2pFLE9BQU9GO1lBQ1QsT0FBTyxJQUNMSSxhQUFhQyxVQUFVLElBQ3ZCRCxhQUFhQyxVQUFVLENBQUNDLFFBQVEsS0FBSyxpQkFDckM7Z0JBQ0FULHdEQUFjLENBQUM7Z0JBQ2ZBLHdEQUFjLENBQUM7Z0JBQ2ZELHVEQUFXLENBQUM7WUFDZDtRQUNGO0lBQ0Y7SUFDQUksY0FBYyxDQUFDLFVBQVUsR0FBR0QsTUFBTUUsT0FBTztJQUN6QyxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy91dGlscy9mb3JtLWVycm9yLnRzeD85MmU4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSb3V0ZXIgZnJvbSAnbmV4dC9yb3V0ZXInO1xyXG5pbXBvcnQgQ29va2llcyBmcm9tICdqcy1jb29raWUnO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGdldEVycm9yTWVzc2FnZShlcnJvcjogYW55KSB7XHJcbiAgbGV0IHByb2Nlc3NlZEVycm9yID0ge1xyXG4gICAgbWVzc2FnZTogJycsXHJcbiAgICB2YWxpZGF0aW9uOiBbXSxcclxuICB9O1xyXG5cclxuICBpZiAoZXJyb3IuZ3JhcGhRTEVycm9ycykge1xyXG4gICAgZm9yIChjb25zdCBncmFwaFFMRXJyb3Igb2YgZXJyb3IuZ3JhcGhRTEVycm9ycykge1xyXG4gICAgICBpZiAoXHJcbiAgICAgICAgZ3JhcGhRTEVycm9yLmV4dGVuc2lvbnMgJiZcclxuICAgICAgICBncmFwaFFMRXJyb3IuZXh0ZW5zaW9ucy5jYXRlZ29yeSA9PT0gJ3ZhbGlkYXRpb24nXHJcbiAgICAgICkge1xyXG4gICAgICAgIHByb2Nlc3NlZEVycm9yWydtZXNzYWdlJ10gPSBncmFwaFFMRXJyb3IubWVzc2FnZTtcclxuICAgICAgICBwcm9jZXNzZWRFcnJvclsndmFsaWRhdGlvbiddID0gZ3JhcGhRTEVycm9yLmV4dGVuc2lvbnMudmFsaWRhdGlvbjtcclxuICAgICAgICByZXR1cm4gcHJvY2Vzc2VkRXJyb3I7XHJcbiAgICAgIH0gZWxzZSBpZiAoXHJcbiAgICAgICAgZ3JhcGhRTEVycm9yLmV4dGVuc2lvbnMgJiZcclxuICAgICAgICBncmFwaFFMRXJyb3IuZXh0ZW5zaW9ucy5jYXRlZ29yeSA9PT0gJ2F1dGhvcml6YXRpb24nXHJcbiAgICAgICkge1xyXG4gICAgICAgIENvb2tpZXMucmVtb3ZlKCdhdXRoX3Rva2VuJyk7XHJcbiAgICAgICAgQ29va2llcy5yZW1vdmUoJ2F1dGhfcGVybWlzc2lvbnMnKTtcclxuICAgICAgICBSb3V0ZXIucHVzaCgnLycpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG4gIHByb2Nlc3NlZEVycm9yWydtZXNzYWdlJ10gPSBlcnJvci5tZXNzYWdlO1xyXG4gIHJldHVybiBwcm9jZXNzZWRFcnJvcjtcclxufVxyXG4iXSwibmFtZXMiOlsiUm91dGVyIiwiQ29va2llcyIsImdldEVycm9yTWVzc2FnZSIsImVycm9yIiwicHJvY2Vzc2VkRXJyb3IiLCJtZXNzYWdlIiwidmFsaWRhdGlvbiIsImdyYXBoUUxFcnJvcnMiLCJncmFwaFFMRXJyb3IiLCJleHRlbnNpb25zIiwiY2F0ZWdvcnkiLCJyZW1vdmUiLCJwdXNoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/utils/form-error.tsx\n"));

/***/ })

}]);