"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_layouts_mobile-navigation_tsx"],{

/***/ "./src/components/icons/home-icon.tsx":
/*!********************************************!*\
  !*** ./src/components/icons/home-icon.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomeIcon: function() { return /* binding */ HomeIcon; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst HomeIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"17.996\",\n        height: \"20.442\",\n        viewBox: \"0 0 17.996 20.442\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            transform: \"translate(-30.619 0.236)\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M48.187,7.823,39.851.182A.7.7,0,0,0,38.9.2L31.03,7.841a.7.7,0,0,0-.211.5V19.311a.694.694,0,0,0,.694.694H37.3A.694.694,0,0,0,38,19.311V14.217h3.242v5.095a.694.694,0,0,0,.694.694h5.789a.694.694,0,0,0,.694-.694V8.335a.7.7,0,0,0-.228-.512ZM47.023,18.617h-4.4V13.522a.694.694,0,0,0-.694-.694H37.3a.694.694,0,0,0-.694.694v5.095H32.2V8.63l7.192-6.98L47.02,8.642v9.975Z\",\n                transform: \"translate(0 0)\",\n                fill: \"currentColor\",\n                stroke: \"currentColor\",\n                strokeWidth: \"0.4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\home-icon.tsx\",\n                lineNumber: 4,\n                columnNumber: 4\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\home-icon.tsx\",\n            lineNumber: 3,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\home-icon.tsx\",\n        lineNumber: 2,\n        columnNumber: 2\n    }, undefined);\n_c = HomeIcon;\nvar _c;\n$RefreshReg$(_c, \"HomeIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9ob21lLWljb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBTyxNQUFNQSxXQUE4QyxDQUFDQyxzQkFDM0QsOERBQUNDO1FBQUlDLE9BQU07UUFBU0MsUUFBTztRQUFTQyxTQUFRO1FBQXFCLEdBQUdKLEtBQUs7a0JBQ3hFLDRFQUFDSztZQUFFQyxXQUFVO3NCQUNaLDRFQUFDQztnQkFDQUMsR0FBRTtnQkFDRkYsV0FBVTtnQkFDVkcsTUFBSztnQkFDTEMsUUFBTztnQkFDUEMsYUFBWTs7Ozs7Ozs7Ozs7Ozs7O2tCQUlkO0tBWldaIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2ljb25zL2hvbWUtaWNvbi50c3g/NjBlOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgSG9tZUljb246IFJlYWN0LkZDPFJlYWN0LlNWR0F0dHJpYnV0ZXM8e30+PiA9IChwcm9wcykgPT4gKFxuXHQ8c3ZnIHdpZHRoPVwiMTcuOTk2XCIgaGVpZ2h0PVwiMjAuNDQyXCIgdmlld0JveD1cIjAgMCAxNy45OTYgMjAuNDQyXCIgey4uLnByb3BzfT5cblx0XHQ8ZyB0cmFuc2Zvcm09XCJ0cmFuc2xhdGUoLTMwLjYxOSAwLjIzNilcIj5cblx0XHRcdDxwYXRoXG5cdFx0XHRcdGQ9XCJNNDguMTg3LDcuODIzLDM5Ljg1MS4xODJBLjcuNywwLDAsMCwzOC45LjJMMzEuMDMsNy44NDFhLjcuNywwLDAsMC0uMjExLjVWMTkuMzExYS42OTQuNjk0LDAsMCwwLC42OTQuNjk0SDM3LjNBLjY5NC42OTQsMCwwLDAsMzgsMTkuMzExVjE0LjIxN2gzLjI0MnY1LjA5NWEuNjk0LjY5NCwwLDAsMCwuNjk0LjY5NGg1Ljc4OWEuNjk0LjY5NCwwLDAsMCwuNjk0LS42OTRWOC4zMzVhLjcuNywwLDAsMC0uMjI4LS41MTJaTTQ3LjAyMywxOC42MTdoLTQuNFYxMy41MjJhLjY5NC42OTQsMCwwLDAtLjY5NC0uNjk0SDM3LjNhLjY5NC42OTQsMCwwLDAtLjY5NC42OTR2NS4wOTVIMzIuMlY4LjYzbDcuMTkyLTYuOThMNDcuMDIsOC42NDJ2OS45NzVaXCJcblx0XHRcdFx0dHJhbnNmb3JtPVwidHJhbnNsYXRlKDAgMClcIlxuXHRcdFx0XHRmaWxsPVwiY3VycmVudENvbG9yXCJcblx0XHRcdFx0c3Ryb2tlPVwiY3VycmVudENvbG9yXCJcblx0XHRcdFx0c3Ryb2tlV2lkdGg9XCIwLjRcIlxuXHRcdFx0Lz5cblx0XHQ8L2c+XG5cdDwvc3ZnPlxuKTtcbiJdLCJuYW1lcyI6WyJIb21lSWNvbiIsInByb3BzIiwic3ZnIiwid2lkdGgiLCJoZWlnaHQiLCJ2aWV3Qm94IiwiZyIsInRyYW5zZm9ybSIsInBhdGgiLCJkIiwiZmlsbCIsInN0cm9rZSIsInN0cm9rZVdpZHRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/icons/home-icon.tsx\n"));

/***/ }),

/***/ "./src/components/icons/navbar-icon.tsx":
/*!**********************************************!*\
  !*** ./src/components/icons/navbar-icon.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavbarIcon: function() { return /* binding */ NavbarIcon; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst NavbarIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"25.567\",\n        height: \"18\",\n        viewBox: \"0 0 25.567 18\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            transform: \"translate(-776 -462)\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    width: \"12.749\",\n                    height: \"2.499\",\n                    rx: \"1.25\",\n                    transform: \"translate(776 462)\",\n                    fill: \"currentColor\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\navbar-icon.tsx\",\n                    lineNumber: 4,\n                    columnNumber: 4\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    width: \"25.567\",\n                    height: \"2.499\",\n                    rx: \"1.25\",\n                    transform: \"translate(776 469.75)\",\n                    fill: \"currentColor\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\navbar-icon.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 4\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    width: \"17.972\",\n                    height: \"2.499\",\n                    rx: \"1.25\",\n                    transform: \"translate(776 477.501)\",\n                    fill: \"currentColor\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\navbar-icon.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 4\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\navbar-icon.tsx\",\n            lineNumber: 3,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\navbar-icon.tsx\",\n        lineNumber: 2,\n        columnNumber: 2\n    }, undefined);\n_c = NavbarIcon;\nvar _c;\n$RefreshReg$(_c, \"NavbarIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9uYXZiYXItaWNvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFPLE1BQU1BLGFBQWdELENBQUNDLHNCQUM3RCw4REFBQ0M7UUFBSUMsT0FBTTtRQUFTQyxRQUFPO1FBQUtDLFNBQVE7UUFBaUIsR0FBR0osS0FBSztrQkFDaEUsNEVBQUNLO1lBQUVDLFdBQVU7OzhCQUNaLDhEQUFDQztvQkFDQUwsT0FBTTtvQkFDTkMsUUFBTztvQkFDUEssSUFBRztvQkFDSEYsV0FBVTtvQkFDVkcsTUFBSzs7Ozs7OzhCQUVOLDhEQUFDRjtvQkFDQUwsT0FBTTtvQkFDTkMsUUFBTztvQkFDUEssSUFBRztvQkFDSEYsV0FBVTtvQkFDVkcsTUFBSzs7Ozs7OzhCQUVOLDhEQUFDRjtvQkFDQUwsT0FBTTtvQkFDTkMsUUFBTztvQkFDUEssSUFBRztvQkFDSEYsV0FBVTtvQkFDVkcsTUFBSzs7Ozs7Ozs7Ozs7Ozs7OztrQkFJUDtLQTFCV1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvaWNvbnMvbmF2YmFyLWljb24udHN4P2Y5NzMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IE5hdmJhckljb246IFJlYWN0LkZDPFJlYWN0LlNWR0F0dHJpYnV0ZXM8e30+PiA9IChwcm9wcykgPT4gKFxuXHQ8c3ZnIHdpZHRoPVwiMjUuNTY3XCIgaGVpZ2h0PVwiMThcIiB2aWV3Qm94PVwiMCAwIDI1LjU2NyAxOFwiIHsuLi5wcm9wc30+XG5cdFx0PGcgdHJhbnNmb3JtPVwidHJhbnNsYXRlKC03NzYgLTQ2MilcIj5cblx0XHRcdDxyZWN0XG5cdFx0XHRcdHdpZHRoPVwiMTIuNzQ5XCJcblx0XHRcdFx0aGVpZ2h0PVwiMi40OTlcIlxuXHRcdFx0XHRyeD1cIjEuMjVcIlxuXHRcdFx0XHR0cmFuc2Zvcm09XCJ0cmFuc2xhdGUoNzc2IDQ2MilcIlxuXHRcdFx0XHRmaWxsPVwiY3VycmVudENvbG9yXCJcblx0XHRcdC8+XG5cdFx0XHQ8cmVjdFxuXHRcdFx0XHR3aWR0aD1cIjI1LjU2N1wiXG5cdFx0XHRcdGhlaWdodD1cIjIuNDk5XCJcblx0XHRcdFx0cng9XCIxLjI1XCJcblx0XHRcdFx0dHJhbnNmb3JtPVwidHJhbnNsYXRlKDc3NiA0NjkuNzUpXCJcblx0XHRcdFx0ZmlsbD1cImN1cnJlbnRDb2xvclwiXG5cdFx0XHQvPlxuXHRcdFx0PHJlY3Rcblx0XHRcdFx0d2lkdGg9XCIxNy45NzJcIlxuXHRcdFx0XHRoZWlnaHQ9XCIyLjQ5OVwiXG5cdFx0XHRcdHJ4PVwiMS4yNVwiXG5cdFx0XHRcdHRyYW5zZm9ybT1cInRyYW5zbGF0ZSg3NzYgNDc3LjUwMSlcIlxuXHRcdFx0XHRmaWxsPVwiY3VycmVudENvbG9yXCJcblx0XHRcdC8+XG5cdFx0PC9nPlxuXHQ8L3N2Zz5cbik7XG4iXSwibmFtZXMiOlsiTmF2YmFySWNvbiIsInByb3BzIiwic3ZnIiwid2lkdGgiLCJoZWlnaHQiLCJ2aWV3Qm94IiwiZyIsInRyYW5zZm9ybSIsInJlY3QiLCJyeCIsImZpbGwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/navbar-icon.tsx\n"));

/***/ }),

/***/ "./src/components/icons/shopping-bag-icon.tsx":
/*!****************************************************!*\
  !*** ./src/components/icons/shopping-bag-icon.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShoppingBagIcon: function() { return /* binding */ ShoppingBagIcon; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ShoppingBagIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"18\",\n        height: \"18\",\n        viewBox: \"0 0 18 18\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            transform: \"translate(-127 -122)\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M4.7,3.8H17.3a.9.9,0,0,1,.9.9V17.3a.9.9,0,0,1-.9.9H4.7a.9.9,0,0,1-.9-.9V4.7A.9.9,0,0,1,4.7,3.8ZM2,4.7A2.7,2.7,0,0,1,4.7,2H17.3A2.7,2.7,0,0,1,20,4.7V17.3A2.7,2.7,0,0,1,17.3,20H4.7A2.7,2.7,0,0,1,2,17.3ZM11,11C8.515,11,6.5,8.583,6.5,5.6H8.3c0,2.309,1.5,3.6,2.7,3.6s2.7-1.291,2.7-3.6h1.8C15.5,8.583,13.485,11,11,11Z\",\n                transform: \"translate(125 120)\",\n                fill: \"currentColor\",\n                fillRule: \"evenodd\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\shopping-bag-icon.tsx\",\n                lineNumber: 4,\n                columnNumber: 4\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\shopping-bag-icon.tsx\",\n            lineNumber: 3,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\shopping-bag-icon.tsx\",\n        lineNumber: 2,\n        columnNumber: 2\n    }, undefined);\n_c = ShoppingBagIcon;\nvar _c;\n$RefreshReg$(_c, \"ShoppingBagIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9zaG9wcGluZy1iYWctaWNvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFPLE1BQU1BLGtCQUFxRCxDQUFDQyxzQkFDbEUsOERBQUNDO1FBQUlDLE9BQU07UUFBS0MsUUFBTztRQUFLQyxTQUFRO1FBQWEsR0FBR0osS0FBSztrQkFDeEQsNEVBQUNLO1lBQUVDLFdBQVU7c0JBQ1osNEVBQUNDO2dCQUNBQyxHQUFFO2dCQUNGRixXQUFVO2dCQUNWRyxNQUFLO2dCQUNMQyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7a0JBSVg7S0FYV1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvaWNvbnMvc2hvcHBpbmctYmFnLWljb24udHN4PzVlYmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFNob3BwaW5nQmFnSWNvbjogUmVhY3QuRkM8UmVhY3QuU1ZHQXR0cmlidXRlczx7fT4+ID0gKHByb3BzKSA9PiAoXG5cdDxzdmcgd2lkdGg9XCIxOFwiIGhlaWdodD1cIjE4XCIgdmlld0JveD1cIjAgMCAxOCAxOFwiIHsuLi5wcm9wc30+XG5cdFx0PGcgdHJhbnNmb3JtPVwidHJhbnNsYXRlKC0xMjcgLTEyMilcIj5cblx0XHRcdDxwYXRoXG5cdFx0XHRcdGQ9XCJNNC43LDMuOEgxNy4zYS45LjksMCwwLDEsLjkuOVYxNy4zYS45LjksMCwwLDEtLjkuOUg0LjdhLjkuOSwwLDAsMS0uOS0uOVY0LjdBLjkuOSwwLDAsMSw0LjcsMy44Wk0yLDQuN0EyLjcsMi43LDAsMCwxLDQuNywySDE3LjNBMi43LDIuNywwLDAsMSwyMCw0LjdWMTcuM0EyLjcsMi43LDAsMCwxLDE3LjMsMjBINC43QTIuNywyLjcsMCwwLDEsMiwxNy4zWk0xMSwxMUM4LjUxNSwxMSw2LjUsOC41ODMsNi41LDUuNkg4LjNjMCwyLjMwOSwxLjUsMy42LDIuNywzLjZzMi43LTEuMjkxLDIuNy0zLjZoMS44QzE1LjUsOC41ODMsMTMuNDg1LDExLDExLDExWlwiXG5cdFx0XHRcdHRyYW5zZm9ybT1cInRyYW5zbGF0ZSgxMjUgMTIwKVwiXG5cdFx0XHRcdGZpbGw9XCJjdXJyZW50Q29sb3JcIlxuXHRcdFx0XHRmaWxsUnVsZT1cImV2ZW5vZGRcIlxuXHRcdFx0Lz5cblx0XHQ8L2c+XG5cdDwvc3ZnPlxuKTtcbiJdLCJuYW1lcyI6WyJTaG9wcGluZ0JhZ0ljb24iLCJwcm9wcyIsInN2ZyIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsImciLCJ0cmFuc2Zvcm0iLCJwYXRoIiwiZCIsImZpbGwiLCJmaWxsUnVsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/icons/shopping-bag-icon.tsx\n"));

/***/ }),

/***/ "./src/components/icons/user-icon.tsx":
/*!********************************************!*\
  !*** ./src/components/icons/user-icon.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserIcon: function() { return /* binding */ UserIcon; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst UserIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16.577\",\n        height: \"18.6\",\n        viewBox: \"0 0 16.577 18.6\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            transform: \"translate(-95.7 -121.203)\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M-7722.37,2933a.63.63,0,0,1-.63-.63c0-4.424,2.837-6.862,7.989-6.862s7.989,2.438,7.989,6.862a.629.629,0,0,1-.63.63Zm.647-1.251h13.428c-.246-3.31-2.5-4.986-6.713-4.986s-6.471,1.673-6.714,4.986Zm2.564-12.518a4.1,4.1,0,0,1,1.172-3,4.1,4.1,0,0,1,2.979-1.229,4.1,4.1,0,0,1,2.979,1.229,4.1,4.1,0,0,1,1.171,3,4.341,4.341,0,0,1-4.149,4.5,4.344,4.344,0,0,1-4.16-4.5Zm1.251,0a3.1,3.1,0,0,0,2.9,3.254,3.094,3.094,0,0,0,2.9-3.253,2.878,2.878,0,0,0-.813-2.109,2.88,2.88,0,0,0-2.085-.872,2.843,2.843,0,0,0-2.1.856,2.841,2.841,0,0,0-.806,2.122Z\",\n                transform: \"translate(7819 -2793.5)\",\n                fill: \"currentColor\",\n                stroke: \"currentColor\",\n                strokeWidth: \"0.6\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\user-icon.tsx\",\n                lineNumber: 4,\n                columnNumber: 4\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\user-icon.tsx\",\n            lineNumber: 3,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\user-icon.tsx\",\n        lineNumber: 2,\n        columnNumber: 2\n    }, undefined);\n_c = UserIcon;\nvar _c;\n$RefreshReg$(_c, \"UserIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy91c2VyLWljb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBTyxNQUFNQSxXQUE4QyxDQUFDQyxzQkFDM0QsOERBQUNDO1FBQUlDLE9BQU07UUFBU0MsUUFBTztRQUFPQyxTQUFRO1FBQW1CLEdBQUdKLEtBQUs7a0JBQ3BFLDRFQUFDSztZQUFFQyxXQUFVO3NCQUNaLDRFQUFDQztnQkFDQUMsR0FBRTtnQkFDRkYsV0FBVTtnQkFDVkcsTUFBSztnQkFDTEMsUUFBTztnQkFDUEMsYUFBWTs7Ozs7Ozs7Ozs7Ozs7O2tCQUlkO0tBWldaIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2ljb25zL3VzZXItaWNvbi50c3g/NDljMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgVXNlckljb246IFJlYWN0LkZDPFJlYWN0LlNWR0F0dHJpYnV0ZXM8e30+PiA9IChwcm9wcykgPT4gKFxuXHQ8c3ZnIHdpZHRoPVwiMTYuNTc3XCIgaGVpZ2h0PVwiMTguNlwiIHZpZXdCb3g9XCIwIDAgMTYuNTc3IDE4LjZcIiB7Li4ucHJvcHN9PlxuXHRcdDxnIHRyYW5zZm9ybT1cInRyYW5zbGF0ZSgtOTUuNyAtMTIxLjIwMylcIj5cblx0XHRcdDxwYXRoXG5cdFx0XHRcdGQ9XCJNLTc3MjIuMzcsMjkzM2EuNjMuNjMsMCwwLDEtLjYzLS42M2MwLTQuNDI0LDIuODM3LTYuODYyLDcuOTg5LTYuODYyczcuOTg5LDIuNDM4LDcuOTg5LDYuODYyYS42MjkuNjI5LDAsMCwxLS42My42M1ptLjY0Ny0xLjI1MWgxMy40MjhjLS4yNDYtMy4zMS0yLjUtNC45ODYtNi43MTMtNC45ODZzLTYuNDcxLDEuNjczLTYuNzE0LDQuOTg2Wm0yLjU2NC0xMi41MThhNC4xLDQuMSwwLDAsMSwxLjE3Mi0zLDQuMSw0LjEsMCwwLDEsMi45NzktMS4yMjksNC4xLDQuMSwwLDAsMSwyLjk3OSwxLjIyOSw0LjEsNC4xLDAsMCwxLDEuMTcxLDMsNC4zNDEsNC4zNDEsMCwwLDEtNC4xNDksNC41LDQuMzQ0LDQuMzQ0LDAsMCwxLTQuMTYtNC41Wm0xLjI1MSwwYTMuMSwzLjEsMCwwLDAsMi45LDMuMjU0LDMuMDk0LDMuMDk0LDAsMCwwLDIuOS0zLjI1MywyLjg3OCwyLjg3OCwwLDAsMC0uODEzLTIuMTA5LDIuODgsMi44OCwwLDAsMC0yLjA4NS0uODcyLDIuODQzLDIuODQzLDAsMCwwLTIuMS44NTYsMi44NDEsMi44NDEsMCwwLDAtLjgwNiwyLjEyMlpcIlxuXHRcdFx0XHR0cmFuc2Zvcm09XCJ0cmFuc2xhdGUoNzgxOSAtMjc5My41KVwiXG5cdFx0XHRcdGZpbGw9XCJjdXJyZW50Q29sb3JcIlxuXHRcdFx0XHRzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuXHRcdFx0XHRzdHJva2VXaWR0aD1cIjAuNlwiXG5cdFx0XHQvPlxuXHRcdDwvZz5cblx0PC9zdmc+XG4pO1xuIl0sIm5hbWVzIjpbIlVzZXJJY29uIiwicHJvcHMiLCJzdmciLCJ3aWR0aCIsImhlaWdodCIsInZpZXdCb3giLCJnIiwidHJhbnNmb3JtIiwicGF0aCIsImQiLCJmaWxsIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/user-icon.tsx\n"));

/***/ }),

/***/ "./src/components/layouts/mobile-navigation.tsx":
/*!******************************************************!*\
  !*** ./src/components/layouts/mobile-navigation.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MobileNavigation; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _components_icons_navbar_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons/navbar-icon */ \"./src/components/icons/navbar-icon.tsx\");\n/* harmony import */ var _components_icons_home_icon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/icons/home-icon */ \"./src/components/icons/home-icon.tsx\");\n/* harmony import */ var _components_icons_shopping_bag_icon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/icons/shopping-bag-icon */ \"./src/components/icons/shopping-bag-icon.tsx\");\n/* harmony import */ var _components_icons_user_icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons/user-icon */ \"./src/components/icons/user-icon.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/store/quick-cart/cart.context */ \"./src/store/quick-cart/cart.context.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! jotai */ \"./node_modules/jotai/esm/index.mjs\");\n/* harmony import */ var _store_drawer_atom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/store/drawer-atom */ \"./src/store/drawer-atom.ts\");\n/* harmony import */ var _store_authorization_atom__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/store/authorization-atom */ \"./src/store/authorization-atom.ts\");\n/* harmony import */ var _lib_locals__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/locals */ \"./src/lib/locals.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction MobileNavigation(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)(\"common\");\n    const { openModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_8__.useModalAction)();\n    const [isAuthorize] = (0,jotai__WEBPACK_IMPORTED_MODULE_12__.useAtom)(_store_authorization_atom__WEBPACK_IMPORTED_MODULE_10__.authorizationAtom);\n    const [_, setDrawerView] = (0,jotai__WEBPACK_IMPORTED_MODULE_12__.useAtom)(_store_drawer_atom__WEBPACK_IMPORTED_MODULE_9__.drawerAtom);\n    const { isRTL } = (0,_lib_locals__WEBPACK_IMPORTED_MODULE_11__.useIsRTL)();\n    const { totalUniqueItems } = (0,_store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_7__.useCart)();\n    function handleSidebar(view) {\n        setDrawerView({\n            display: true,\n            view\n        });\n    }\n    function handleJoin() {\n        return openModal(\"LOGIN_VIEW\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"visible fixed bottom-0 z-10 flex h-12 w-full justify-between bg-light py-1.5 px-2 shadow-400 ltr:left-0 rtl:right-0 md:h-14 lg:hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                whileTap: {\n                    scale: 0.88\n                },\n                onClick: ()=>handleSidebar(\"MAIN_MENU_VIEW\"),\n                className: \"flex h-full items-center justify-center p-2 focus:text-accent focus:outline-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"text-burger-menu\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_navbar_icon__WEBPACK_IMPORTED_MODULE_2__.NavbarIcon, {\n                        className: \"\".concat(isRTL && \"rotate-180 transform\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                whileTap: {\n                    scale: 0.88\n                },\n                onClick: ()=>router.push(\"/\"),\n                className: \"flex h-full items-center justify-center p-2 focus:text-accent focus:outline-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"text-home\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_home_icon__WEBPACK_IMPORTED_MODULE_3__.HomeIcon, {}, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                whileTap: {\n                    scale: 0.88\n                },\n                onClick: ()=>handleSidebar(\"cart\"),\n                className: \"product-cart relative flex h-full items-center justify-center p-2 focus:text-accent focus:outline-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"text-cart\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_shopping_bag_icon__WEBPACK_IMPORTED_MODULE_4__.ShoppingBagIcon, {}, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    totalUniqueItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-0 mt-0.5 rounded-full bg-accent py-1 px-1.5 text-10px font-semibold leading-none text-light ltr:right-0 ltr:-mr-0.5 rtl:left-0 rtl:-ml-0.5\",\n                        children: totalUniqueItems\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            isAuthorize ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                whileTap: {\n                    scale: 0.88\n                },\n                onClick: ()=>handleSidebar(\"AUTH_MENU_VIEW\"),\n                className: \"flex h-full items-center justify-center p-2 focus:text-accent focus:outline-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"text-user\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_user_icon__WEBPACK_IMPORTED_MODULE_5__.UserIcon, {}, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                whileTap: {\n                    scale: 0.88\n                },\n                onClick: handleJoin,\n                className: \"flex h-full items-center justify-center p-2 focus:text-accent focus:outline-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"text-user\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_user_icon__WEBPACK_IMPORTED_MODULE_5__.UserIcon, {}, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-navigation.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(MobileNavigation, \"ZiWR6LA4R2W6B3KoWDMwvEgGWNw=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_8__.useModalAction,\n        jotai__WEBPACK_IMPORTED_MODULE_12__.useAtom,\n        jotai__WEBPACK_IMPORTED_MODULE_12__.useAtom,\n        _lib_locals__WEBPACK_IMPORTED_MODULE_11__.useIsRTL,\n        _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_7__.useCart\n    ];\n});\n_c = MobileNavigation;\nvar _c;\n$RefreshReg$(_c, \"MobileNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layouts/mobile-navigation.tsx\n"));

/***/ })

}]);