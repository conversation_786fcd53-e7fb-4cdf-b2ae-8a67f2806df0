"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagsService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const tag_entity_1 = require("./entities/tag.entity");
const type_entity_1 = require("../types/entities/type.entity");
const sequelize_2 = require("sequelize");
let TagsService = class TagsService {
    constructor(tagModel) {
        this.tagModel = tagModel;
    }
    async create(createTagDto) {
        return this.tagModel.create(Object.assign({}, createTagDto));
    }
    async findAll({ page, limit, search }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const whereClause = {};
        if (search) {
            const parseSearchParams = search.split(';');
            for (const searchParam of parseSearchParams) {
                const [key, value] = searchParam.split(':');
                if (key !== 'slug' && key in this.tagModel.rawAttributes) {
                    whereClause[key] = {
                        [sequelize_2.Op.iLike]: `%${value}%`,
                    };
                }
            }
        }
        const { count, rows } = await this.tagModel.findAndCountAll({
            where: whereClause,
            include: [{ model: type_entity_1.Type, as: 'type' }],
            limit,
            offset,
            order: [['created_at', 'DESC']],
        });
        const url = `/tags?limit=${limit}`;
        const totalPages = Math.ceil(count / limit);
        return {
            data: rows,
            count,
            current_page: page,
            firstItem: offset + 1,
            lastItem: Math.min(offset + limit, count),
            last_page: totalPages,
            per_page: limit,
            total: count,
            first_page_url: `${url}&page=1`,
            last_page_url: `${url}&page=${totalPages}`,
            next_page_url: page < totalPages ? `${url}&page=${page + 1}` : null,
            prev_page_url: page > 1 ? `${url}&page=${page - 1}` : null,
        };
    }
    async findOne(param, language) {
        return this.tagModel.findOne({
            where: {
                [sequelize_2.Op.or]: [{ id: Number(param) || 0 }, { slug: param }],
            },
            include: [{ model: type_entity_1.Type, as: 'type' }],
        });
    }
    async update(id, updateTagDto) {
        const [affectedCount, updatedTags] = await this.tagModel.update(Object.assign({}, updateTagDto), { where: { id }, returning: true });
        return [affectedCount, updatedTags];
    }
    async remove(id) {
        return this.tagModel.destroy({ where: { id } });
    }
};
TagsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(tag_entity_1.Tag)),
    __metadata("design:paramtypes", [Object])
], TagsService);
exports.TagsService = TagsService;
//# sourceMappingURL=tags.service.js.map