import { Injectable } from '@nestjs/common';
import {
  AuthResponse,
  ChangePasswordDto,
  ForgetPasswordDto,
  LoginDto,
  CoreResponse,
  RegisterDto,
  ResetPasswordDto,
  VerifyForgetPasswordDto,
  SocialLoginDto,
  OtpLoginDto,
  OtpResponse,
  VerifyOtpDto,
  OtpDto,
} from './dto/create-auth.dto';
import { v4 as uuidv4 } from 'uuid';
import { InjectModel } from '@nestjs/sequelize';
import { User } from 'src/users/entities/user.entity';

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(User)
    private userModel: typeof User,
  ) {}
  async register(createUserInput: RegisterDto): Promise<AuthResponse> {
    // For now, return a mock response until we implement proper Sequelize user creation
    // TODO: Implement actual user creation with this.userModel.create()

    return {
      token: 'jwt token',
      permissions: ['super_admin', 'customer'],
    };
  }
  async login(loginInput: LoginDto): Promise<AuthResponse> {
    console.log(loginInput);
    if (loginInput.email === '<EMAIL>') {
      return {
        token: 'jwt token',
        permissions: ['store_owner', 'super_admin'],
        role: 'super_admin',
      };
    } else if (
      ['<EMAIL>', '<EMAIL>'].includes(loginInput.email)
    ) {
      return {
        token: 'jwt token',
        permissions: ['store_owner', 'customer'],
        role: 'store_owner',
      };
    } else {
      return {
        token: 'jwt token',
        permissions: ['customer'],
        role: 'customer',
      };
    }
  }
  async changePassword(
    changePasswordInput: ChangePasswordDto,
  ): Promise<CoreResponse> {
    console.log(changePasswordInput);

    return {
      success: true,
      message: 'Password change successful',
    };
  }
  async forgetPassword(
    forgetPasswordInput: ForgetPasswordDto,
  ): Promise<CoreResponse> {
    console.log(forgetPasswordInput);

    return {
      success: true,
      message: 'Password change successful',
    };
  }
  async verifyForgetPasswordToken(
    verifyForgetPasswordTokenInput: VerifyForgetPasswordDto,
  ): Promise<CoreResponse> {
    console.log(verifyForgetPasswordTokenInput);

    return {
      success: true,
      message: 'Password change successful',
    };
  }
  async resetPassword(
    resetPasswordInput: ResetPasswordDto,
  ): Promise<CoreResponse> {
    console.log(resetPasswordInput);

    return {
      success: true,
      message: 'Password change successful',
    };
  }
  async socialLogin(socialLoginDto: SocialLoginDto): Promise<AuthResponse> {
    console.log(socialLoginDto);
    return {
      token: 'jwt token',
      permissions: ['super_admin', 'customer'],
      role: 'customer',
    };
  }
  async otpLogin(otpLoginDto: OtpLoginDto): Promise<AuthResponse> {
    console.log(otpLoginDto);
    return {
      token: 'jwt token',
      permissions: ['super_admin', 'customer'],
      role: 'customer',
    };
  }
  async verifyOtpCode(verifyOtpInput: VerifyOtpDto): Promise<CoreResponse> {
    console.log(verifyOtpInput);
    return {
      message: 'success',
      success: true,
    };
  }
  async sendOtpCode(otpInput: OtpDto): Promise<OtpResponse> {
    console.log(otpInput);
    return {
      message: 'success',
      success: true,
      id: '1',
      provider: 'google',
      phone_number: '+919494949494',
      is_contact_exist: true,
    };
  }

  // async getUsers({ text, first, page }: GetUsersArgs): Promise<UserPaginator> {
  //   const startIndex = (page - 1) * first;
  //   const endIndex = page * first;
  //   let data: User[] = this.users;
  //   if (text?.replace(/%/g, '')) {
  //     data = fuse.search(text)?.map(({ item }) => item);
  //   }
  //   const results = data.slice(startIndex, endIndex);
  //   return {
  //     data: results,
  //     paginatorInfo: paginate(data.length, page, first, results.length),
  //   };
  // }
  // public getUser(getUserArgs: GetUserArgs): User {
  //   return this.users.find((user) => user.id === getUserArgs.id);
  // }
  async me(): Promise<User | null> {
    // TODO: Implement proper user retrieval based on JWT token
    // For now, return a mock admin user for development
    const mockUser = {
      id: 1,
      name: 'Admin User',
      email: '<EMAIL>',
      email_verified_at: new Date(),
      created_at: new Date(),
      updated_at: new Date(),
      is_active: true,
      shops: [
        {
          id: 1,
          name: 'Demo Shop',
          slug: 'demo-shop',
          owner_id: 1,
          is_active: true,
        },
        {
          id: 3,
          name: 'Ridwan',
          slug: 'ridwan',
          owner_id: 1,
          is_active: true,
        },
      ],
      managed_shop: {
        id: 1,
        name: 'Demo Shop',
        slug: 'demo-shop',
        owner_id: 1,
        is_active: true,
      },
      profile: {
        id: 1,
        bio: 'Administrator',
        contact: '<EMAIL>',
        avatar: null,
      },
      address: [],
      permissions: ['super_admin'], // Grant super admin permissions
    } as any;

    return mockUser;
  }

  async updateEmail(updateEmailDto: { email: string }): Promise<CoreResponse> {
    try {
      // TODO: Implement proper email update logic with JWT token validation
      // For now, return a success response
      console.log('Updating email to:', updateEmailDto.email);

      return {
        success: true,
        message: 'Email updated successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to update email',
      };
    }
  }

  // updateUser(id: number, updateUserInput: UpdateUserInput) {
  //   return `This action updates a #${id} user`;
  // }
}
