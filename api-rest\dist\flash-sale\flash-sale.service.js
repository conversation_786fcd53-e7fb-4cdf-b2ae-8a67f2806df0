"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlashSaleService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const paginate_1 = require("../common/pagination/paginate");
const flash_sale_entity_1 = require("./entities/flash-sale.entity");
const product_entity_1 = require("../products/entities/product.entity");
let FlashSaleService = class FlashSaleService {
    constructor(flashSaleModel, productModel) {
        this.flashSaleModel = flashSaleModel;
        this.productModel = productModel;
    }
    async create(createFlashSaleDto) {
        return this.flashSaleModel.create(createFlashSaleDto);
    }
    async findAllFlashSale({ search, limit, page }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 10;
        const offset = (page - 1) * limit;
        const { count, rows: data } = await this.flashSaleModel.findAndCountAll({
            limit,
            offset,
        });
        const url = `/flash-sale?search=${search}&limit=${limit}`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
    async getFlashSale(param, language) {
        return this.flashSaleModel.findOne({ where: { slug: param } });
    }
    async update(id, updateFlashSaleDto) {
        await this.flashSaleModel.update(updateFlashSaleDto, {
            where: { id },
        });
        return this.flashSaleModel.findByPk(id);
    }
    remove(id) {
        return `This action removes a #${id} Flash Sale`;
    }
    async findAllProductsByFlashSale({ search, limit, page }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 10;
        const offset = (page - 1) * limit;
        const { count, rows: productsByData } = await this.productModel.findAndCountAll({
            limit,
            offset,
        });
        const url = `/products-by-flash-sale?search=${search}&limit=${limit}`;
        return Object.assign({ data: productsByData }, (0, paginate_1.paginate)(count, page, limit, productsByData.length, url));
    }
};
FlashSaleService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(flash_sale_entity_1.FlashSale)),
    __param(1, (0, sequelize_1.InjectModel)(product_entity_1.Product)),
    __metadata("design:paramtypes", [Object, Object])
], FlashSaleService);
exports.FlashSaleService = FlashSaleService;
//# sourceMappingURL=flash-sale.service.js.map