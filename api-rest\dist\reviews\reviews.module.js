"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewModule = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const reports_controller_1 = require("./reports.controller");
const reports_service_1 = require("./reports.service");
const reviews_controller_1 = require("./reviews.controller");
const reviews_service_1 = require("./reviews.service");
const review_entity_1 = require("./entities/review.entity");
let ReviewModule = class ReviewModule {
};
ReviewModule = __decorate([
    (0, common_1.Module)({
        imports: [sequelize_1.SequelizeModule.forFeature([review_entity_1.Review])],
        controllers: [reviews_controller_1.ReviewController, reports_controller_1.AbusiveReportsController],
        providers: [reviews_service_1.ReviewService, reports_service_1.AbusiveReportService],
        exports: [reviews_service_1.ReviewService, reports_service_1.AbusiveReportService],
    })
], ReviewModule);
exports.ReviewModule = ReviewModule;
//# sourceMappingURL=reviews.module.js.map