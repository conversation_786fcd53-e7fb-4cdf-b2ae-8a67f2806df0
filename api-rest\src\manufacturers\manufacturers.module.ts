import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ManufacturersService } from './manufacturers.service';
import {
  ManufacturersController,
  TopManufacturersController,
} from './manufacturers.controller';
import { Manufacturer } from './entities/manufacturer.entity';

@Module({
  imports: [SequelizeModule.forFeature([Manufacturer])],
  controllers: [ManufacturersController, TopManufacturersController],
  providers: [ManufacturersService],
  exports: [ManufacturersService],
})
export class ManufacturersModule {}
