# oneKart - E-Commerce Platform

A modern, full-stack e-commerce platform built with Next.js, NestJS, and PostgreSQL.

## 🛍️ Features

- **Modern Shop Frontend** - Built with Next.js 13, TypeScript, and Tailwind CSS
- **Powerful Admin Dashboard** - Complete management interface for products, orders, and customers
- **RESTful API Backend** - NestJS-powered API with PostgreSQL database
- **Multi-language Support** - Internationalization ready
- **Authentication** - NextAuth.js with multiple providers
- **Payment Integration** - Stripe, PayPal, and more
- **Responsive Design** - Mobile-first approach

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- PostgreSQL database
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd onekart-ecommerce
   ```

2. **Install dependencies**
   ```bash
   # Install API dependencies
   cd api-rest && npm install

   # Install Admin dependencies  
   cd ../admin-rest && npm install

   # Install Shop dependencies
   cd ../shop && npm install
   ```

3. **Configure environment variables**
   - Copy `.env.example` to `.env` in each directory
   - Update database connection strings and API keys

4. **Start the applications**
   ```bash
   # Start API server (port 9000)
   cd api-rest && npm run start:dev

   # Start Admin dashboard (port 3002)
   cd admin-rest && npm run dev

   # Start Shop frontend (port 3005)
   cd shop && npx next dev -p 3005
   ```

## 📁 Project Structure

```
oneKart/
├── api-rest/          # NestJS API backend
├── admin-rest/        # React admin dashboard
├── shop/              # Next.js shop frontend
├── docker-compose.yml # Docker configuration
└── README.md
```

## 🌐 Applications

- **Shop**: http://localhost:3005 - Customer-facing e-commerce site
- **Admin**: http://localhost:3002 - Admin management dashboard  
- **API**: http://localhost:9000/api - RESTful API endpoints

## 🛠️ Built With

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: NestJS, TypeScript, Sequelize ORM
- **Database**: PostgreSQL
- **Authentication**: NextAuth.js
- **Deployment**: Docker, Vercel

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
