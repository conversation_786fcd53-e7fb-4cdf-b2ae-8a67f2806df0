"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetAttributeArgs = void 0;
const openapi = require("@nestjs/swagger");
class GetAttributeArgs {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => Number }, slug: { required: false, type: () => String }, language: { required: false, type: () => String } };
    }
}
exports.GetAttributeArgs = GetAttributeArgs;
//# sourceMappingURL=get-attribute.dto.js.map