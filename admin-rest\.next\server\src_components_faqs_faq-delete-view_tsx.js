"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_faqs_faq-delete-view_tsx";
exports.ids = ["src_components_faqs_faq-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/faqs/faq-delete-view.tsx":
/*!*************************************************!*\
  !*** ./src/components/faqs/faq-delete-view.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_faqs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/faqs */ \"./src/data/faqs.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_faqs__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_faqs__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst FaqsDeleteView = ()=>{\n    const { mutate: deleteFaq, isLoading: loading } = (0,_data_faqs__WEBPACK_IMPORTED_MODULE_3__.useDeleteFaqsMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteFaq({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\faqs\\\\faq-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FaqsDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/faqs/faq-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/faqs.ts":
/*!*********************************!*\
  !*** ./src/data/client/faqs.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   faqsClient: () => (/* binding */ faqsClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst faqsClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS),\n    all: ({ faq_title, shop_id, ...params } = {})=>_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                faq_title,\n                shop_id\n            })\n        }),\n    get ({ id, language }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS}/${id}`, {\n            language\n        });\n    },\n    paginated: ({ faq_title, shop_id, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                faq_title,\n                shop_id\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/faqs.ts\n");

/***/ }),

/***/ "./src/data/faqs.ts":
/*!**************************!*\
  !*** ./src/data/faqs.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateFaqsMutation: () => (/* binding */ useCreateFaqsMutation),\n/* harmony export */   useDeleteFaqsMutation: () => (/* binding */ useDeleteFaqsMutation),\n/* harmony export */   useFaqQuery: () => (/* binding */ useFaqQuery),\n/* harmony export */   useFaqsLoadMoreQuery: () => (/* binding */ useFaqsLoadMoreQuery),\n/* harmony export */   useFaqsQuery: () => (/* binding */ useFaqsQuery),\n/* harmony export */   useUpdateFaqsMutation: () => (/* binding */ useUpdateFaqsMutation)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\n/* harmony import */ var _data_client_faqs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/data/client/faqs */ \"./src/data/client/faqs.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _config__WEBPACK_IMPORTED_MODULE_7__, _data_client_faqs__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _config__WEBPACK_IMPORTED_MODULE_7__, _data_client_faqs__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// Read Single FAQ\nconst useFaqQuery = ({ id, language })=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS,\n        {\n            id,\n            language\n        }\n    ], ()=>_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.get({\n            id,\n            language\n        }));\n    return {\n        faqs: data,\n        error,\n        loading: isLoading\n    };\n};\n// Read All FAQs\nconst useFaqsQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        faqs: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n// Read All FAQs paginated\nconst useFaqsLoadMoreQuery = (options, config)=>{\n    const { data, error, isLoading, isFetchingNextPage, hasNextPage, fetchNextPage } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useInfiniteQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.all(Object.assign({}, queryKey[1], pageParam)), {\n        ...config,\n        getNextPageParam: ({ current_page, last_page })=>last_page > current_page && {\n                page: current_page + 1\n            }\n    });\n    function handleLoadMore() {\n        fetchNextPage();\n    }\n    return {\n        faqs: data?.pages.flatMap((page)=>page?.data) ?? [],\n        paginatorInfo: Array.isArray(data?.pages) ? data?.pages[data.pages.length - 1] : null,\n        error,\n        hasNextPage,\n        loading: isLoading,\n        isLoadingMore: isFetchingNextPage,\n        loadMore: handleLoadMore\n    };\n};\n// Create FAQ\nconst useCreateFaqsMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// Update FAQ\nconst useUpdateFaqsMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list;\n            await router.push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// Delete FAQ\nconst useDeleteFaqsMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/faqs.ts\n");

/***/ })

};
;