"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FaqItems = exports.BusinessPurposeItem = exports.SellingStepItem = exports.BecomeSellerOptions = exports.BecomeSeller = void 0;
const openapi = require("@nestjs/swagger");
const sequelize_typescript_1 = require("sequelize-typescript");
let BecomeSeller = class BecomeSeller extends sequelize_typescript_1.Model {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => Number }, page_options: { required: true, type: () => ({ page_options: { required: true, type: () => require("./become-seller.entity").BecomeSellerOptions } }) }, language: { required: true, type: () => String }, missions: { required: true, type: () => [Object] } };
    }
};
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    }),
    __metadata("design:type", Number)
], BecomeSeller.prototype, "id", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.JSON,
        allowNull: true,
    }),
    __metadata("design:type", Object)
], BecomeSeller.prototype, "page_options", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: true,
    }),
    __metadata("design:type", String)
], BecomeSeller.prototype, "language", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.JSON,
        allowNull: true,
    }),
    __metadata("design:type", Array)
], BecomeSeller.prototype, "missions", void 0);
BecomeSeller = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: 'become_sellers',
        timestamps: true,
    })
], BecomeSeller);
exports.BecomeSeller = BecomeSeller;
class BecomeSellerOptions {
    static _OPENAPI_METADATA_FACTORY() {
        return { banner: { required: true, type: () => require("../../common/entities/attachment.entity").Attachment }, sellingStepsTitle: { required: true, type: () => String }, sellingStepsDescription: { required: true, type: () => String }, sellingStepsItem: { required: true, type: () => [require("./become-seller.entity").SellingStepItem] }, purposeTitle: { required: true, type: () => String }, purposeDescription: { required: true, type: () => String }, purposeItems: { required: true, type: () => [require("./become-seller.entity").BusinessPurposeItem] }, commissionTitle: { required: true, type: () => String }, commissionDescription: { required: true, type: () => String }, faqTitle: { required: true, type: () => String }, faqDescription: { required: true, type: () => String }, faqItems: { required: true, type: () => [require("./become-seller.entity").FaqItems] } };
    }
}
exports.BecomeSellerOptions = BecomeSellerOptions;
class SellingStepItem {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => String }, description: { required: true, type: () => String }, title: { required: true, type: () => String }, image: { required: false, type: () => require("../../common/entities/attachment.entity").Attachment } };
    }
}
exports.SellingStepItem = SellingStepItem;
class BusinessPurposeItem {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => String }, description: { required: true, type: () => String }, title: { required: true, type: () => String }, icon: { required: true, type: () => ({ value: { required: true, type: () => String } }) } };
    }
}
exports.BusinessPurposeItem = BusinessPurposeItem;
class FaqItems {
    static _OPENAPI_METADATA_FACTORY() {
        return { description: { required: true, type: () => String }, title: { required: true, type: () => String } };
    }
}
exports.FaqItems = FaqItems;
//# sourceMappingURL=become-seller.entity.js.map