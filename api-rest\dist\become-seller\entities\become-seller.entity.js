"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FaqItems = exports.BusinessPurposeItem = exports.SellingStepItem = exports.BecomeSellerOptions = exports.BecomeSeller = void 0;
const openapi = require("@nestjs/swagger");
const core_entity_1 = require("../../common/entities/core.entity");
class BecomeSeller extends core_entity_1.CoreEntity {
    static _OPENAPI_METADATA_FACTORY() {
        return { page_options: { required: true, type: () => ({ page_options: { required: true, type: () => require("./become-seller.entity").BecomeSellerOptions } }) }, language: { required: true, type: () => String }, missions: { required: true, type: () => [Object] } };
    }
}
exports.BecomeSeller = BecomeSeller;
class BecomeSellerOptions {
    static _OPENAPI_METADATA_FACTORY() {
        return { banner: { required: true, type: () => require("../../common/entities/attachment.entity").Attachment }, sellingStepsTitle: { required: true, type: () => String }, sellingStepsDescription: { required: true, type: () => String }, sellingStepsItem: { required: true, type: () => [require("./become-seller.entity").SellingStepItem] }, purposeTitle: { required: true, type: () => String }, purposeDescription: { required: true, type: () => String }, purposeItems: { required: true, type: () => [require("./become-seller.entity").BusinessPurposeItem] }, commissionTitle: { required: true, type: () => String }, commissionDescription: { required: true, type: () => String }, faqTitle: { required: true, type: () => String }, faqDescription: { required: true, type: () => String }, faqItems: { required: true, type: () => [require("./become-seller.entity").FaqItems] } };
    }
}
exports.BecomeSellerOptions = BecomeSellerOptions;
class SellingStepItem {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => String }, description: { required: true, type: () => String }, title: { required: true, type: () => String }, image: { required: false, type: () => require("../../common/entities/attachment.entity").Attachment } };
    }
}
exports.SellingStepItem = SellingStepItem;
class BusinessPurposeItem {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: false, type: () => String }, description: { required: true, type: () => String }, title: { required: true, type: () => String }, icon: { required: true, type: () => ({ value: { required: true, type: () => String } }) } };
    }
}
exports.BusinessPurposeItem = BusinessPurposeItem;
class FaqItems {
    static _OPENAPI_METADATA_FACTORY() {
        return { description: { required: true, type: () => String }, title: { required: true, type: () => String } };
    }
}
exports.FaqItems = FaqItems;
//# sourceMappingURL=become-seller.entity.js.map