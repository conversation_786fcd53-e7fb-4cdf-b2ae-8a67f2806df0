"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_form_location-based-shop-form_tsx";
exports.ids = ["src_components_form_location-based-shop-form_tsx"];
exports.modules = {

/***/ "./src/components/form/google-places-autocomplete.tsx":
/*!************************************************************!*\
  !*** ./src/components/form/google-places-autocomplete.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GooglePlacesAutocomplete)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_google_maps_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-google-maps/api */ \"@react-google-maps/api\");\n/* harmony import */ var _react_google_maps_api__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_react_google_maps_api__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\n/* harmony import */ var _lib_use_location__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/use-location */ \"./src/lib/use-location.tsx\");\n/* harmony import */ var _icons_current_location__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../icons/current-location */ \"./src/components/icons/current-location.tsx\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! jotai */ \"jotai\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_use_location__WEBPACK_IMPORTED_MODULE_5__, jotai__WEBPACK_IMPORTED_MODULE_7__]);\n([_lib_use_location__WEBPACK_IMPORTED_MODULE_5__, jotai__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction GooglePlacesAutocomplete({ register, onChange, onChangeCurrentLocation, data, disabled = false }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onLoad, onUnmount, onPlaceChanged, getCurrentLocation, isLoaded, loadError] = (0,_lib_use_location__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n        onChange,\n        onChangeCurrentLocation,\n        setInputValue\n    });\n    const [location] = (0,jotai__WEBPACK_IMPORTED_MODULE_7__.useAtom)(_lib_use_location__WEBPACK_IMPORTED_MODULE_5__.locationAtom);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getLocation = data?.formattedAddress;\n        setInputValue(getLocation);\n    }, [\n        data\n    ]);\n    if (loadError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: t(\"common:text-map-cant-load\")\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n            lineNumber: 42,\n            columnNumber: 12\n        }, this);\n    }\n    return isLoaded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_google_maps_api__WEBPACK_IMPORTED_MODULE_2__.Autocomplete, {\n                onLoad: onLoad,\n                onPlaceChanged: onPlaceChanged,\n                onUnmount: onUnmount,\n                fields: [\n                    \"address_components\",\n                    \"geometry.location\",\n                    \"formatted_address\"\n                ],\n                types: [\n                    \"address\"\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    ...register(\"location\"),\n                    placeholder: t(\"common:placeholder-search-location\"),\n                    value: inputValue,\n                    onChange: (e)=>setInputValue(e.target.value),\n                    className: `line-clamp-1 flex h-12 w-full appearance-none items-center rounded border border-border-base p-4 pr-9 text-sm font-medium text-heading transition duration-300 ease-in-out invalid:border-red-500 focus:border-accent focus:outline-0 focus:ring-0 ${disabled ? \"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]\" : \"\"}`,\n                    disabled: disabled\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 flex h-12 w-12 items-center justify-center text-accent\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons_current_location__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-5 w-5 cursor-pointer hover:text-accent\",\n                    onClick: ()=>{\n                        getCurrentLocation();\n                        setInputValue(location?.formattedAddress);\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_4__.SpinnerLoader, {}, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/form/google-places-autocomplete.tsx\n");

/***/ }),

/***/ "./src/components/form/location-based-shop-form.tsx":
/*!**********************************************************!*\
  !*** ./src/components/form/location-based-shop-form.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocationBasedShopForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hook-form */ \"react-hook-form\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jotai */ \"jotai\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_forms_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/forms/form */ \"./src/components/ui/forms/form.tsx\");\n/* harmony import */ var _components_form_google_places_autocomplete__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/form/google-places-autocomplete */ \"./src/components/form/google-places-autocomplete.tsx\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _components_icons_arrow_right__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/arrow-right */ \"./src/components/icons/arrow-right.tsx\");\n/* harmony import */ var _lib_use_location__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/use-location */ \"./src/lib/use-location.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hook_form__WEBPACK_IMPORTED_MODULE_1__, jotai__WEBPACK_IMPORTED_MODULE_5__, _components_ui_forms_form__WEBPACK_IMPORTED_MODULE_8__, _components_form_google_places_autocomplete__WEBPACK_IMPORTED_MODULE_9__, _lib_use_location__WEBPACK_IMPORTED_MODULE_13__]);\n([react_hook_form__WEBPACK_IMPORTED_MODULE_1__, jotai__WEBPACK_IMPORTED_MODULE_5__, _components_ui_forms_form__WEBPACK_IMPORTED_MODULE_8__, _components_form_google_places_autocomplete__WEBPACK_IMPORTED_MODULE_9__, _lib_use_location__WEBPACK_IMPORTED_MODULE_13__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LocationBasedShopForm({ className, closeLocation }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_11__.useModalAction)();\n    const [location, setLocation] = (0,jotai__WEBPACK_IMPORTED_MODULE_5__.useAtom)(_lib_use_location__WEBPACK_IMPORTED_MODULE_13__.locationAtom);\n    const onSubmit = (values)=>{\n        router.push(_config_routes__WEBPACK_IMPORTED_MODULE_10__.Routes?.nearByShop({\n            lat: values?.location?.lat?.toString(),\n            lng: values?.location?.lng?.toString()\n        }));\n        setLocation(values?.location);\n        closeModal();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        const storedLocation = localStorage.getItem(\"currentLocation\");\n        if (storedLocation) {\n            const parsedLocation = JSON.parse(storedLocation);\n            setLocation(parsedLocation);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        if (location) {\n            const stringifiedLocation = JSON.stringify(location);\n            localStorage.setItem(\"currentLocation\", stringifiedLocation);\n        }\n    }, [\n        location\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(\"w-full border border-border-200 bg-light p-5 shadow-[-8px_8px_16px_rgba(0,0,0,0.18)] md:min-h-0 md:w-[650px] md:rounded-xl xl:w-[1076px]\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_form__WEBPACK_IMPORTED_MODULE_8__.Form, {\n            onSubmit: onSubmit,\n            className: \"flex h-full gap-2.5\",\n            children: ({ register, control, watch })=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_1__.Controller, {\n                                control: control,\n                                name: \"location\",\n                                render: ({ field: { onChange, value } })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_form_google_places_autocomplete__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        register: register,\n                                        onChange: onChange,\n                                        onChangeCurrentLocation: onChange,\n                                        data: value\n                                    }, void 0, false, void 0, void 0)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-12 w-12 !px-0\",\n                            disabled: !watch(\"location\"),\n                            onClick: closeLocation,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_arrow_right__WEBPACK_IMPORTED_MODULE_12__.ArrowRight, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true);\n            }\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/form/location-based-shop-form.tsx\n");

/***/ }),

/***/ "./src/components/icons/arrow-right.tsx":
/*!**********************************************!*\
  !*** ./src/components/icons/arrow-right.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRight: () => (/* binding */ ArrowRight)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ArrowRight = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.8,\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\arrow-right.tsx\",\n            lineNumber: 3,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\arrow-right.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9hcnJvdy1yaWdodC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFPLE1BQU1BLGFBQWdELENBQUNDLHNCQUM1RCw4REFBQ0M7UUFBSUMsT0FBTTtRQUE2QkMsTUFBSztRQUFPQyxTQUFRO1FBQVlDLGFBQWE7UUFBS0MsUUFBTztRQUFnQixHQUFHTixLQUFLO2tCQUN2SCw0RUFBQ087WUFBS0MsZUFBYztZQUFRQyxnQkFBZTtZQUFRQyxHQUFFOzs7Ozs7Ozs7O2tCQUV2RCIsInNvdXJjZXMiOlsid2VicGFjazovL0BwaWNrLWJhemFyL3Nob3AvLi9zcmMvY29tcG9uZW50cy9pY29ucy9hcnJvdy1yaWdodC50c3g/YmY0YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQXJyb3dSaWdodDogUmVhY3QuRkM8UmVhY3QuU1ZHQXR0cmlidXRlczx7fT4+ID0gKHByb3BzKSA9PiAoXG4gIDxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZVdpZHRoPXsxLjh9IHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHsuLi5wcm9wc30+XG4gICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIGQ9XCJNMTMuNSA0LjVMMjEgMTJtMCAwbC03LjUgNy41TTIxIDEySDNcIiAvPlxuICA8L3N2Zz5cbik7XG4iXSwibmFtZXMiOlsiQXJyb3dSaWdodCIsInByb3BzIiwic3ZnIiwieG1sbnMiLCJmaWxsIiwidmlld0JveCIsInN0cm9rZVdpZHRoIiwic3Ryb2tlIiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/arrow-right.tsx\n");

/***/ }),

/***/ "./src/components/icons/current-location.tsx":
/*!***************************************************!*\
  !*** ./src/components/icons/current-location.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction CurrentLocation({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: 16,\n        height: 16,\n        viewBox: \"0 0 24 24\",\n        strokeWidth: \"2\",\n        stroke: \"currentColor\",\n        fill: \"none\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                stroke: \"none\",\n                d: \"M0 0h24v24H0z\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 12m-8 0a8 8 0 1 0 16 0a8 8 0 1 0 -16 0\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2l0 2\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 20l0 2\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M20 12l2 0\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M2 12l2 0\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CurrentLocation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9jdXJyZW50LWxvY2F0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBeUI7QUFFekIsU0FBU0MsZ0JBQWdCLEVBQUUsR0FBR0MsT0FBTztJQUNuQyxxQkFDRSw4REFBQ0M7UUFDQ0MsT0FBTTtRQUNOQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsU0FBUTtRQUNSQyxhQUFZO1FBQ1pDLFFBQU87UUFDUEMsTUFBSztRQUNMQyxlQUFjO1FBQ2RDLGdCQUFlO1FBQ2QsR0FBR1YsS0FBSzs7MEJBQ1QsOERBQUNXO2dCQUFLSixRQUFPO2dCQUFPSyxHQUFFO2dCQUFnQkosTUFBSzs7Ozs7OzBCQUMzQyw4REFBQ0c7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7OztBQUdkO0FBRUEsaUVBQWViLGVBQWVBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AcGljay1iYXphci9zaG9wLy4vc3JjL2NvbXBvbmVudHMvaWNvbnMvY3VycmVudC1sb2NhdGlvbi50c3g/MjlhYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmZ1bmN0aW9uIEN1cnJlbnRMb2NhdGlvbih7IC4uLnByb3BzIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8c3ZnXG4gICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICAgIHdpZHRoPXsxNn1cbiAgICAgIGhlaWdodD17MTZ9XG4gICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXG4gICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgey4uLnByb3BzfT5cbiAgICAgIDxwYXRoIHN0cm9rZT1cIm5vbmVcIiBkPVwiTTAgMGgyNHYyNEgwelwiIGZpbGw9XCJub25lXCI+PC9wYXRoPlxuICAgICAgPHBhdGggZD1cIk0xMiAxMm0tMyAwYTMgMyAwIDEgMCA2IDBhMyAzIDAgMSAwIC02IDBcIj48L3BhdGg+XG4gICAgICA8cGF0aCBkPVwiTTEyIDEybS04IDBhOCA4IDAgMSAwIDE2IDBhOCA4IDAgMSAwIC0xNiAwXCI+PC9wYXRoPlxuICAgICAgPHBhdGggZD1cIk0xMiAybDAgMlwiPjwvcGF0aD5cbiAgICAgIDxwYXRoIGQ9XCJNMTIgMjBsMCAyXCI+PC9wYXRoPlxuICAgICAgPHBhdGggZD1cIk0yMCAxMmwyIDBcIj48L3BhdGg+XG4gICAgICA8cGF0aCBkPVwiTTIgMTJsMiAwXCI+PC9wYXRoPlxuICAgIDwvc3ZnPlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IEN1cnJlbnRMb2NhdGlvbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkN1cnJlbnRMb2NhdGlvbiIsInByb3BzIiwic3ZnIiwieG1sbnMiLCJ3aWR0aCIsImhlaWdodCIsInZpZXdCb3giLCJzdHJva2VXaWR0aCIsInN0cm9rZSIsImZpbGwiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJwYXRoIiwiZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/icons/current-location.tsx\n");

/***/ }),

/***/ "./src/lib/use-location.tsx":
/*!**********************************!*\
  !*** ./src/lib/use-location.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useLocation),\n/* harmony export */   fullAddressAtom: () => (/* binding */ fullAddressAtom),\n/* harmony export */   locationAtom: () => (/* binding */ locationAtom)\n/* harmony export */ });\n/* harmony import */ var _react_google_maps_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-google-maps/api */ \"@react-google-maps/api\");\n/* harmony import */ var _react_google_maps_api__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_react_google_maps_api__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"react-i18next\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! jotai */ \"jotai\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_2__, jotai__WEBPACK_IMPORTED_MODULE_3__]);\n([react_i18next__WEBPACK_IMPORTED_MODULE_2__, jotai__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst locationAtom = (0,jotai__WEBPACK_IMPORTED_MODULE_3__.atom)(null);\nconst libraries = [\n    \"places\"\n];\nconst fullAddressAtom = (0,jotai__WEBPACK_IMPORTED_MODULE_3__.atom)((get)=>{\n    const location = get(locationAtom);\n    return location ? `${location.street_address}, ${location.city}, ${location.state}, ${location.zip}, ${location.country}` : \"\";\n});\nfunction getLocation(placeOrResult) {\n    // Declare the location variable with the Location interface\n    const location = {\n        lat: placeOrResult?.geometry?.location.lat(),\n        lng: placeOrResult?.geometry?.location.lng(),\n        formattedAddress: placeOrResult.formatted_address\n    };\n    // Define an object that maps component types to location properties\n    const componentMap = {\n        postal_code: \"zip\",\n        postal_code_suffix: \"zip\",\n        state_name: \"street_address\",\n        route: \"street_address\",\n        sublocality_level_1: \"street_address\",\n        locality: \"city\",\n        administrative_area_level_1: \"state\",\n        country: \"country\"\n    };\n    for (const component of placeOrResult?.address_components){\n        const [componentType] = component.types;\n        const { long_name, short_name } = component;\n        // Check if the component type is in the map\n        if (componentMap[componentType]) {\n            // Assign the component value to the location property\n            location[componentMap[componentType]] ??= long_name;\n            // If the component type is postal_code_suffix, append it to the zip\n            componentType === \"postal_code_suffix\" ? location[\"zip\"] = `${location?.zip}-${long_name}` : null;\n            // If the component type is administrative_area_level_1, use the short name\n            componentType === \"administrative_area_level_1\" ? location[\"state\"] = short_name : null;\n        }\n    }\n    // Return the location object\n    return location;\n}\nfunction useLocation({ onChange, onChangeCurrentLocation, setInputValue }) {\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [autocomplete, setAutocomplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { isLoaded, loadError } = (0,_react_google_maps_api__WEBPACK_IMPORTED_MODULE_0__.useJsApiLoader)({\n        id: \"google_map_autocomplete\",\n        googleMapsApiKey: \"\",\n        libraries\n    });\n    const onLoad = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((autocompleteInstance)=>{\n        setAutocomplete(autocompleteInstance);\n    }, []);\n    const onUnmount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setAutocomplete(true);\n    }, []);\n    const onPlaceChanged = ()=>{\n        const place = autocomplete?.getPlace();\n        if (!place?.geometry?.location) {\n            return;\n        }\n        const location = getLocation(place);\n        if (onChange) {\n            onChange(location);\n        }\n        if (setInputValue) {\n            setInputValue(place?.formatted_address);\n        }\n    };\n    const getCurrentLocation = ()=>{\n        if (navigator?.geolocation) {\n            navigator?.geolocation.getCurrentPosition(async (position)=>{\n                const { latitude, longitude } = position.coords;\n                const geocoder = new google.maps.Geocoder();\n                const latlng = {\n                    lat: latitude,\n                    lng: longitude\n                };\n                geocoder.geocode({\n                    location: latlng\n                }, (results, status)=>{\n                    if (status === \"OK\" && results?.[0]) {\n                        const location = getLocation(results?.[0]);\n                        onChangeCurrentLocation?.(location);\n                    }\n                });\n            }, (error)=>{\n                console.error(\"Error getting current location:\", error);\n            });\n        } else {\n            console.error(\"Geolocation is not supported by this browser.\");\n        }\n    };\n    return [\n        onLoad,\n        onUnmount,\n        onPlaceChanged,\n        getCurrentLocation,\n        isLoaded,\n        loadError && t(loadError)\n    ];\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/use-location.tsx\n");

/***/ })

};
;