"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_terms-and-conditions_terms-and-conditions-delete-view_tsx"],{

/***/ "./src/components/terms-and-conditions/terms-and-conditions-delete-view.tsx":
/*!**********************************************************************************!*\
  !*** ./src/components/terms-and-conditions/terms-and-conditions-delete-view.tsx ***!
  \**********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_terms_and_condition__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/terms-and-condition */ \"./src/data/terms-and-condition.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst TermsAndConditionsDeleteView = ()=>{\n    _s();\n    const { mutate: deleteTermsAndConditions, isLoading: loading } = (0,_data_terms_and_condition__WEBPACK_IMPORTED_MODULE_3__.useDeleteTermsAndConditionsMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteTermsAndConditions({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\terms-and-conditions\\\\terms-and-conditions-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TermsAndConditionsDeleteView, \"bYRDrgDsyV+B7ioG0+Us2D23htY=\", false, function() {\n    return [\n        _data_terms_and_condition__WEBPACK_IMPORTED_MODULE_3__.useDeleteTermsAndConditionsMutation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction\n    ];\n});\n_c = TermsAndConditionsDeleteView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TermsAndConditionsDeleteView);\nvar _c;\n$RefreshReg$(_c, \"TermsAndConditionsDeleteView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/terms-and-conditions/terms-and-conditions-delete-view.tsx\n"));

/***/ }),

/***/ "./src/data/client/terms-and-condition.ts":
/*!************************************************!*\
  !*** ./src/data/client/terms-and-condition.ts ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   termsAndConditionClients: function() { return /* binding */ termsAndConditionClients; }\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\n\n\n\nconst termsAndConditionClients = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TERMS_AND_CONDITIONS),\n    paginated: (param)=>{\n        let { title, shop_id, ...params } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TERMS_AND_CONDITIONS, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                title,\n                shop_id\n            })\n        });\n    },\n    approve: (variables)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.APPROVE_TERMS_AND_CONDITIONS, variables);\n    },\n    disapprove: (variables)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.DISAPPROVE_TERMS_AND_CONDITIONS, variables);\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/terms-and-condition.ts\n"));

/***/ }),

/***/ "./src/data/terms-and-condition.ts":
/*!*****************************************!*\
  !*** ./src/data/terms-and-condition.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApproveTermAndConditionMutation: function() { return /* binding */ useApproveTermAndConditionMutation; },\n/* harmony export */   useCreateTermsAndConditionsMutation: function() { return /* binding */ useCreateTermsAndConditionsMutation; },\n/* harmony export */   useDeleteTermsAndConditionsMutation: function() { return /* binding */ useDeleteTermsAndConditionsMutation; },\n/* harmony export */   useDisApproveTermAndConditionMutation: function() { return /* binding */ useDisApproveTermAndConditionMutation; },\n/* harmony export */   useTermsAndConditionQuery: function() { return /* binding */ useTermsAndConditionQuery; },\n/* harmony export */   useTermsAndConditionsQuery: function() { return /* binding */ useTermsAndConditionsQuery; },\n/* harmony export */   useUpdateTermsAndConditionsMutation: function() { return /* binding */ useUpdateTermsAndConditionsMutation; }\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\n/* harmony import */ var _data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/data/client/terms-and-condition */ \"./src/data/client/terms-and-condition.ts\");\n\n\n\n\n\n\n\n\n\n// approve terms\nconst useApproveTermAndConditionMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.approve, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS);\n        }\n    });\n};\n// disapprove terms\nconst useDisApproveTermAndConditionMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.disapprove, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS);\n        }\n    });\n};\n// Read Single Terms And Conditions\nconst useTermsAndConditionQuery = (param)=>{\n    let { slug, language } = param;\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS,\n        {\n            slug,\n            language\n        }\n    ], ()=>_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.get({\n            slug,\n            language\n        }));\n    return {\n        termsAndConditions: data,\n        error,\n        loading: isLoading\n    };\n};\n// Read All Terms And Conditions\nconst useTermsAndConditionsQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS,\n        options\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.paginated(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        keepPreviousData: true\n    });\n    var _data_data;\n    return {\n        termsAndConditions: (_data_data = data === null || data === void 0 ? void 0 : data.data) !== null && _data_data !== void 0 ? _data_data : [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n// Create Terms And Conditions\nconst useCreateTermsAndConditionsMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? \"/\".concat(router.query.shop).concat(_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.termsAndCondition.list) : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.termsAndCondition.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\n// Update Terms And Conditions\nconst useUpdateTermsAndConditionsMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? \"/\".concat(router.query.shop).concat(_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.termsAndCondition.list) : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.termsAndCondition.list;\n            await router.push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\n// Delete Terms And Conditions\nconst useDeleteTermsAndConditionsMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/terms-and-condition.ts\n"));

/***/ })

}]);