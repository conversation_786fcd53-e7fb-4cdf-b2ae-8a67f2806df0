import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreateTagDto } from './dto/create-tag.dto';
import { GetTagsDto } from './dto/get-tags.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { Tag } from './entities/tag.entity';
import { Type } from 'src/types/entities/type.entity';
import { Op } from 'sequelize';

@Injectable()
export class TagsService {
  constructor(
    @InjectModel(Tag)
    private tagModel: typeof Tag,
  ) {}

  async create(createTagDto: CreateTagDto): Promise<Tag> {
    return this.tagModel.create({ ...createTagDto });
  }

  async findAll({ page, limit, search }: GetTagsDto) {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const offset = (page - 1) * limit;

    const whereClause: any = {};

    if (search) {
      const parseSearchParams = search.split(';');
      for (const searchParam of parseSearchParams) {
        const [key, value] = searchParam.split(':');
        if (key !== 'slug' && key in this.tagModel.rawAttributes) {
          whereClause[key] = {
            [Op.iLike]: `%${value}%`,
          };
        }
      }
    }

    const { count, rows } = await this.tagModel.findAndCountAll({
      where: whereClause,
      include: [{ model: Type, as: 'type' }],
      limit,
      offset,
      order: [['created_at', 'DESC']],
    });

    const url = `/tags?limit=${limit}`;
    const totalPages = Math.ceil(count / limit);

    return {
      data: rows,
      count,
      current_page: page,
      firstItem: offset + 1,
      lastItem: Math.min(offset + limit, count),
      last_page: totalPages,
      per_page: limit,
      total: count,
      first_page_url: `${url}&page=1`,
      last_page_url: `${url}&page=${totalPages}`,
      next_page_url: page < totalPages ? `${url}&page=${page + 1}` : null,
      prev_page_url: page > 1 ? `${url}&page=${page - 1}` : null,
    };
  }

  async findOne(param: string, language: string): Promise<Tag> {
    return this.tagModel.findOne({
      where: {
        [Op.or]: [{ id: Number(param) || 0 }, { slug: param }],
      },
      include: [{ model: Type, as: 'type' }],
    });
  }

  async update(
    id: number,
    updateTagDto: UpdateTagDto,
  ): Promise<[number, Tag[]]> {
    const [affectedCount, updatedTags] = await this.tagModel.update(
      { ...updateTagDto },
      { where: { id }, returning: true },
    );
    return [affectedCount, updatedTags];
  }

  async remove(id: number): Promise<number> {
    return this.tagModel.destroy({ where: { id } });
  }
}
