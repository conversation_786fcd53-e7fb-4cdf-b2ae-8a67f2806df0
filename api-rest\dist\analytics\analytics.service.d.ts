import { Analytics } from './entities/analytics.entity';
import { CategoryWiseProduct } from './entities/category-wise-product.entity';
import { Product } from 'src/products/entities/product.entity';
import { TopRateProduct } from './entities/top-rate-product.entity';
export declare class AnalyticsService {
    private analyticsModel;
    private categoryWiseProductModel;
    private productModel;
    private topRateProductModel;
    constructor(analyticsModel: typeof Analytics, categoryWiseProductModel: typeof CategoryWiseProduct, productModel: typeof Product, topRateProductModel: typeof TopRateProduct);
    findAll(): Promise<Analytics[]>;
    findAllCategoryWiseProduct(): Promise<CategoryWiseProduct[]>;
    findAllLowStockProducts(): Promise<Product[]>;
    findAllTopRateProduct(): Promise<TopRateProduct[]>;
}
