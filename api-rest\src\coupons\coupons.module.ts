import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { CouponsService } from './coupons.service';
import {
  ApproveCouponController,
  CouponsController,
  DisapproveCouponController,
} from './coupons.controller';
import { Coupon } from './entities/coupon.entity';

@Module({
  imports: [SequelizeModule.forFeature([Coupon])],
  controllers: [
    CouponsController,
    ApproveCouponController,
    DisapproveCouponController,
  ],
  providers: [CouponsService],
  exports: [CouponsService],
})
export class CouponsModule {}
