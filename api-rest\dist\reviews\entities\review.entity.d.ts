import { Model } from 'sequelize-typescript';
import { Shop } from 'src/shops/entities/shop.entity';
import { User } from 'src/users/entities/user.entity';
import { Product } from 'src/products/entities/product.entity';
export declare class Review extends Model {
    id: number;
    rating: number;
    name: string;
    comment: string;
    shop_id: number;
    shop: Shop;
    order_id?: number;
    user_id: number;
    customer: User;
    user: User;
    photos: any[];
    product_id: number;
    product: Product;
    feedbacks: any[];
    my_feedback: any;
    positive_feedbacks_count: number;
    negative_feedbacks_count: number;
    abusive_reports: any[];
    variation_option_id: string;
    abusive_reports_count?: number;
    created_at: Date;
    updated_at: Date;
}
