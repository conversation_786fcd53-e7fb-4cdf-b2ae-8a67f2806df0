"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_layouts_mobile-menu_mobile-main-menu_tsx"],{

/***/ "./src/components/layouts/mobile-menu/mobile-main-menu.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/layouts/mobile-menu/mobile-main-menu.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MobileMainMenu; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/drawer/drawer-wrapper */ \"./src/components/ui/drawer/drawer-wrapper.tsx\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! jotai */ \"./node_modules/jotai/esm/index.mjs\");\n/* harmony import */ var _store_drawer_atom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/drawer-atom */ \"./src/store/drawer-atom.ts\");\n/* harmony import */ var _config_site__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/site */ \"./src/config/site.ts\");\n/* harmony import */ var _components_ui_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/link */ \"./src/components/ui/link.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MobileMainMenu() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [_, closeSidebar] = (0,jotai__WEBPACK_IMPORTED_MODULE_7__.useAtom)(_store_drawer_atom__WEBPACK_IMPORTED_MODULE_4__.drawerAtom);\n    const { headerLinks } = _config_site__WEBPACK_IMPORTED_MODULE_5__.siteSettings;\n    // function handleClick(path: string) {\n    //   router.push(path);\n    //   closeSidebar({ display: false, view: '' });\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            className: \"grow\",\n            children: headerLinks === null || headerLinks === void 0 ? void 0 : headerLinks.map((param)=>/*#__PURE__*/ {\n                let { href, label } = param;\n                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        href: href,\n                        className: \"flex items-center px-5 py-3 text-sm font-semibold capitalize transition duration-200 cursor-pointer text-heading hover:text-accent md:px-6\",\n                        title: t(label),\n                        onClick: ()=>closeSidebar({\n                                display: false,\n                                view: \"\"\n                            }),\n                        children: t(label)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, this)\n                }, \"\".concat(href).concat(label), false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 11\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(MobileMainMenu, \"6yWsjLJg/fqapYaqZ8lbowMXmZg=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        jotai__WEBPACK_IMPORTED_MODULE_7__.useAtom\n    ];\n});\n_c = MobileMainMenu;\nvar _c;\n$RefreshReg$(_c, \"MobileMainMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layouts/mobile-menu/mobile-main-menu.tsx\n"));

/***/ }),

/***/ "./src/config/site.ts":
/*!****************************!*\
  !*** ./src/config/site.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siteSettings: function() { return /* binding */ siteSettings; }\n/* harmony export */ });\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"./src/types/index.ts\");\n\n\nconst siteSettings = {\n    name: \"oneKart\",\n    description: \"\",\n    logo: {\n        url: \"/logo.svg\",\n        alt: \"oneKart\",\n        href: \"/grocery\",\n        width: 128,\n        height: 40\n    },\n    defaultLanguage: \"en\",\n    currencyCode: \"USD\",\n    product: {\n        placeholderImage: \"/product-placeholder.svg\",\n        cardMaps: {\n            grocery: \"Krypton\",\n            furniture: \"Radon\",\n            bag: \"Oganesson\",\n            makeup: \"Neon\",\n            book: \"Xenon\",\n            medicine: \"Helium\",\n            default: \"Argon\"\n        }\n    },\n    authorizedLinks: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"auth-menu-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"auth-menu-my-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.checkout,\n            label: \"auth-menu-checkout\"\n        }\n    ],\n    authorizedLinksMobile: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"auth-menu-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.notifyLogs,\n            label: \"profile-sidebar-notifications\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"auth-menu-my-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.cards,\n            label: \"profile-sidebar-my-cards\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.questions,\n            label: \"profile-sidebar-my-questions\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.refunds,\n            label: \"text-my-refunds\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.reports,\n            label: \"profile-sidebar-my-reports\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.checkout,\n            label: \"auth-menu-checkout\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.changePassword,\n            label: \"profile-sidebar-password\"\n        }\n    ],\n    dashboardSidebarMenu: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"profile-sidebar-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.changePassword,\n            label: \"profile-sidebar-password\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.notifyLogs,\n            label: \"profile-sidebar-notifications\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.cards,\n            label: \"profile-sidebar-my-cards\",\n            // MultiPayment: Make it dynamic or from mapper\n            cardsPayment: [\n                _types__WEBPACK_IMPORTED_MODULE_1__.PaymentGateway.STRIPE\n            ]\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"profile-sidebar-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.downloads,\n            label: \"profile-sidebar-downloads\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.questions,\n            label: \"profile-sidebar-my-questions\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.refunds,\n            label: \"text-my-refunds\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.reports,\n            label: \"profile-sidebar-my-reports\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help,\n            label: \"profile-sidebar-help\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.logout,\n            label: \"profile-sidebar-logout\"\n        }\n    ],\n    sellingAdvertisement: {\n        image: {\n            src: \"/selling.png\",\n            alt: \"Selling Advertisement\"\n        }\n    },\n    cta: {\n        mockup_img_src: \"/mockup-img.png\",\n        play_store_link: \"/\",\n        app_store_link: \"/\"\n    },\n    headerLinks: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shops,\n            icon: null,\n            label: \"nav-menu-shops\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.coupons,\n            icon: null,\n            label: \"nav-menu-offer\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.contactUs,\n            label: \"nav-menu-contact\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.becomeSeller,\n            label: \"Become a seller\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.flashSale,\n            label: \"nav-menu-flash-sale\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.manufacturers,\n            label: \"text-manufacturers\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.authors,\n            label: \"text-authors\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help,\n            label: \"nav-menu-faq\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.terms,\n            label: \"nav-menu-terms\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.customerRefundPolicies,\n            label: \"nav-menu-refund-policy\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.vendorRefundPolicies,\n            label: \"nav-menu-vendor-refund-policy\"\n        }\n    ],\n    footer: {\n        // copyright: {\n        //   name: 'RedQ, Inc',\n        //   href: 'https://redq.io/',\n        // },\n        // address: '2429 River Drive, Suite 35 Cottonhall, CA 2296 United Kingdom',\n        // email: '<EMAIL>',\n        // phone: '******-698-0694',\n        menus: [\n            {\n                title: \"text-explore\",\n                links: [\n                    {\n                        name: \"Shops\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shops\n                    },\n                    {\n                        name: \"Authors\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.authors\n                    },\n                    {\n                        name: \"Flash Deals\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes === null || _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes === void 0 ? void 0 : _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.flashSale\n                    },\n                    {\n                        name: \"Coupon\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.coupons\n                    }\n                ]\n            },\n            {\n                title: \"text-customer-service\",\n                links: [\n                    {\n                        name: \"text-faq-help\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help\n                    },\n                    {\n                        name: \"Vendor Refund Policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.vendorRefundPolicies\n                    },\n                    {\n                        name: \"Customer Refund Policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.customerRefundPolicies\n                    }\n                ]\n            },\n            {\n                title: \"text-our-information\",\n                links: [\n                    {\n                        name: \"Manufacturers\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes === null || _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes === void 0 ? void 0 : _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.manufacturers\n                    },\n                    {\n                        name: \"Privacy policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.privacy\n                    },\n                    {\n                        name: \"text-terms-condition\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.terms\n                    },\n                    {\n                        name: \"text-contact-us\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.contactUs\n                    }\n                ]\n            }\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/config/site.ts\n"));

/***/ })

}]);