import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { TaxesService } from './taxes.service';
import { TaxesController } from './taxes.controller';
import { Tax } from './entities/tax.entity';

@Module({
  imports: [SequelizeModule.forFeature([Tax])],
  controllers: [TaxesController],
  providers: [TaxesService],
  exports: [TaxesService],
})
export class TaxesModule {}
