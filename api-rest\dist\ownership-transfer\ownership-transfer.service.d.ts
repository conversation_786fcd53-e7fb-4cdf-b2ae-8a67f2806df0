import { CreateOwnershipTransferDto } from './dto/create-ownership-transfer.dto';
import { OwnershipTransfer } from './entities/ownership-transfer.entity';
import { GetOwnershipTransferDto } from './dto/get-ownership-transfer.dto';
import { UpdateOwnershipTransferDto } from './dto/update-ownership-transfer.dto';
export declare class OwnershipTransferService {
    private ownershipTransfer;
    create(createOwnershipTransferDto: CreateOwnershipTransferDto): OwnershipTransfer;
    findAll({ search, limit, page }: GetOwnershipTransferDto): {
        count: number;
        current_page: number;
        firstItem: number;
        lastItem: number;
        last_page: number;
        per_page: number;
        total: number;
        first_page_url: string;
        last_page_url: string;
        next_page_url: string;
        prev_page_url: string;
        data: OwnershipTransfer[];
    };
    getOwnershipTransfer(param: string, language: string): OwnershipTransfer;
    update(id: number, updateOwnershipTransferDto: UpdateOwnershipTransferDto): OwnershipTransfer;
    remove(id: number): string;
}
