"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOwnershipTransferDto = void 0;
const openapi = require("@nestjs/swagger");
const swagger_1 = require("@nestjs/swagger");
const ownership_transfer_entity_1 = require("../entities/ownership-transfer.entity");
class CreateOwnershipTransferDto extends (0, swagger_1.PickType)(ownership_transfer_entity_1.OwnershipTransfer, [
    'status',
]) {
    static _OPENAPI_METADATA_FACTORY() {
        return {};
    }
}
exports.CreateOwnershipTransferDto = CreateOwnershipTransferDto;
//# sourceMappingURL=create-ownership-transfer.dto.js.map