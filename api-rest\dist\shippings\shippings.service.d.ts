import { CreateShippingDto } from './dto/create-shipping.dto';
import { GetShippingsDto } from './dto/get-shippings.dto';
import { UpdateShippingDto } from './dto/update-shipping.dto';
import { Shipping } from './entities/shipping.entity';
export declare class ShippingsService {
    private shippingModel;
    constructor(shippingModel: typeof Shipping);
    create(createShippingDto: CreateShippingDto): Promise<Shipping>;
    getShippings({}: GetShippingsDto): Promise<Shipping[]>;
    findOne(id: number): Promise<Shipping | null>;
    update(id: number, updateShippingDto: UpdateShippingDto): Promise<Shipping | null>;
    remove(id: number): string;
}
