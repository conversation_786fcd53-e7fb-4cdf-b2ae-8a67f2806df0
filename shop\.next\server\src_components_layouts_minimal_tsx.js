"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_layouts_minimal_tsx";
exports.ids = ["src_components_layouts_minimal_tsx"];
exports.modules = {

/***/ "./src/components/banners/banner.tsx":
/*!*******************************************!*\
  !*** ./src/components/banners/banner.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _framework_type__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/framework/type */ \"./src/framework/rest/type.ts\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_framework_type__WEBPACK_IMPORTED_MODULE_1__]);\n_framework_type__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst ErrorMessage = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/components/ui/error-message */ \"./src/components/ui/error-message.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\banners\\\\banner.tsx -> \" + \"@/components/ui/error-message\"\n        ]\n    }\n});\nconst BannerWithSearch = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/swiper\"), __webpack_require__.e(\"vendor-chunks/react-use\"), __webpack_require__.e(\"src_components_banners_banner-with-search_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/components/banners/banner-with-search */ \"./src/components/banners/banner-with-search.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\banners\\\\banner.tsx -> \" + \"@/components/banners/banner-with-search\"\n        ]\n    }\n});\nconst BannerShort = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/swiper\"), __webpack_require__.e(\"src_components_banners_banner-short_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/components/banners/banner-short */ \"./src/components/banners/banner-short.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\banners\\\\banner.tsx -> \" + \"@/components/banners/banner-short\"\n        ]\n    }\n});\nconst BannerWithoutSlider = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>__webpack_require__.e(/*! import() */ \"src_components_banners_banner-without-slider_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/banners/banner-without-slider */ \"./src/components/banners/banner-without-slider.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\banners\\\\banner.tsx -> \" + \"@/components/banners/banner-without-slider\"\n        ]\n    }\n});\nconst BannerWithPagination = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/swiper\"), __webpack_require__.e(\"src_components_banners_banner-with-pagination_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/components/banners/banner-with-pagination */ \"./src/components/banners/banner-with-pagination.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\banners\\\\banner.tsx -> \" + \"@/components/banners/banner-with-pagination\"\n        ]\n    }\n});\nconst MAP_BANNER_TO_GROUP = {\n    classic: BannerWithSearch,\n    modern: BannerShort,\n    minimal: BannerWithoutSlider,\n    standard: BannerWithSearch,\n    compact: BannerWithPagination,\n    default: BannerWithSearch\n};\nconst Banner = ({ layout, variables })=>{\n    const { type, error } = (0,_framework_type__WEBPACK_IMPORTED_MODULE_1__.useType)(variables.type);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorMessage, {\n        message: error.message\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\banners\\\\banner.tsx\",\n        lineNumber: 28,\n        columnNumber: 21\n    }, undefined);\n    const Component = MAP_BANNER_TO_GROUP[layout];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        banners: type?.banners,\n        layout: layout,\n        slug: type?.slug\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\banners\\\\banner.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Banner);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9iYW5uZXJzL2Jhbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUEyQztBQUNSO0FBQ25DLE1BQU1FLGVBQWVELG1EQUFPQSxDQUFDLElBQU0sa0tBQU87Ozs7Ozs7QUFDMUMsTUFBTUUsbUJBQW1CRixtREFBT0EsQ0FDOUIsSUFBTSwyVkFBTzs7Ozs7OztBQUVmLE1BQU1HLGNBQWNILG1EQUFPQSxDQUFDLElBQU0sdVJBQU87Ozs7Ozs7QUFDekMsTUFBTUksc0JBQXNCSixtREFBT0EsQ0FDakMsSUFBTSxxUEFBTzs7Ozs7OztBQUVmLE1BQU1LLHVCQUF1QkwsbURBQU9BLENBQ2xDLElBQU0scVRBQU87Ozs7Ozs7QUFFZixNQUFNTSxzQkFBMkM7SUFDL0NDLFNBQVNMO0lBQ1RNLFFBQVFMO0lBQ1JNLFNBQVNMO0lBQ1RNLFVBQVVSO0lBQ1ZTLFNBQVNOO0lBQ1RPLFNBQVNWO0FBQ1g7QUFFQSxNQUFNVyxTQUF1RCxDQUFDLEVBQzVEQyxNQUFNLEVBQ05DLFNBQVMsRUFDVjtJQUNDLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBR2xCLHdEQUFPQSxDQUFDZ0IsVUFBVUMsSUFBSTtJQUM5QyxJQUFJQyxPQUFPLHFCQUFPLDhEQUFDaEI7UUFBYWlCLFNBQVNELE1BQU1DLE9BQU87Ozs7OztJQUN0RCxNQUFNQyxZQUFZYixtQkFBbUIsQ0FBQ1EsT0FBTztJQUM3QyxxQkFDRSw4REFBQ0s7UUFBVUMsU0FBU0osTUFBTUk7UUFBU04sUUFBUUE7UUFBUU8sTUFBTUwsTUFBTUs7Ozs7OztBQUVuRTtBQUVBLGlFQUFlUixNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBpY2stYmF6YXIvc2hvcC8uL3NyYy9jb21wb25lbnRzL2Jhbm5lcnMvYmFubmVyLnRzeD8wN2MxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVR5cGUgfSBmcm9tICdAL2ZyYW1ld29yay90eXBlJztcbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYyc7XG5jb25zdCBFcnJvck1lc3NhZ2UgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnQC9jb21wb25lbnRzL3VpL2Vycm9yLW1lc3NhZ2UnKSk7XG5jb25zdCBCYW5uZXJXaXRoU2VhcmNoID0gZHluYW1pYyhcbiAgKCkgPT4gaW1wb3J0KCdAL2NvbXBvbmVudHMvYmFubmVycy9iYW5uZXItd2l0aC1zZWFyY2gnKVxuKTtcbmNvbnN0IEJhbm5lclNob3J0ID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9iYW5uZXJzL2Jhbm5lci1zaG9ydCcpKTtcbmNvbnN0IEJhbm5lcldpdGhvdXRTbGlkZXIgPSBkeW5hbWljKFxuICAoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9iYW5uZXJzL2Jhbm5lci13aXRob3V0LXNsaWRlcicpXG4pO1xuY29uc3QgQmFubmVyV2l0aFBhZ2luYXRpb24gPSBkeW5hbWljKFxuICAoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9iYW5uZXJzL2Jhbm5lci13aXRoLXBhZ2luYXRpb24nKVxuKTtcbmNvbnN0IE1BUF9CQU5ORVJfVE9fR1JPVVA6IFJlY29yZDxzdHJpbmcsIGFueT4gPSB7XG4gIGNsYXNzaWM6IEJhbm5lcldpdGhTZWFyY2gsXG4gIG1vZGVybjogQmFubmVyU2hvcnQsXG4gIG1pbmltYWw6IEJhbm5lcldpdGhvdXRTbGlkZXIsXG4gIHN0YW5kYXJkOiBCYW5uZXJXaXRoU2VhcmNoLFxuICBjb21wYWN0OiBCYW5uZXJXaXRoUGFnaW5hdGlvbixcbiAgZGVmYXVsdDogQmFubmVyV2l0aFNlYXJjaCxcbn07XG5cbmNvbnN0IEJhbm5lcjogUmVhY3QuRkM8eyBsYXlvdXQ6IHN0cmluZzsgdmFyaWFibGVzOiBhbnkgfT4gPSAoe1xuICBsYXlvdXQsXG4gIHZhcmlhYmxlcyxcbn0pID0+IHtcbiAgY29uc3QgeyB0eXBlLCBlcnJvciB9ID0gdXNlVHlwZSh2YXJpYWJsZXMudHlwZSk7XG4gIGlmIChlcnJvcikgcmV0dXJuIDxFcnJvck1lc3NhZ2UgbWVzc2FnZT17ZXJyb3IubWVzc2FnZX0gLz47XG4gIGNvbnN0IENvbXBvbmVudCA9IE1BUF9CQU5ORVJfVE9fR1JPVVBbbGF5b3V0XTtcbiAgcmV0dXJuIChcbiAgICA8Q29tcG9uZW50IGJhbm5lcnM9e3R5cGU/LmJhbm5lcnN9IGxheW91dD17bGF5b3V0fSBzbHVnPXt0eXBlPy5zbHVnfSAvPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQmFubmVyO1xuIl0sIm5hbWVzIjpbInVzZVR5cGUiLCJkeW5hbWljIiwiRXJyb3JNZXNzYWdlIiwiQmFubmVyV2l0aFNlYXJjaCIsIkJhbm5lclNob3J0IiwiQmFubmVyV2l0aG91dFNsaWRlciIsIkJhbm5lcldpdGhQYWdpbmF0aW9uIiwiTUFQX0JBTk5FUl9UT19HUk9VUCIsImNsYXNzaWMiLCJtb2Rlcm4iLCJtaW5pbWFsIiwic3RhbmRhcmQiLCJjb21wYWN0IiwiZGVmYXVsdCIsIkJhbm5lciIsImxheW91dCIsInZhcmlhYmxlcyIsInR5cGUiLCJlcnJvciIsIm1lc3NhZ2UiLCJDb21wb25lbnQiLCJiYW5uZXJzIiwic2x1ZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/banners/banner.tsx\n");

/***/ }),

/***/ "./src/components/layouts/minimal.tsx":
/*!********************************************!*\
  !*** ./src/components/layouts/minimal.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MinimalLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_banners_banner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/banners/banner */ \"./src/components/banners/banner.tsx\");\n/* harmony import */ var _components_categories_categories__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/categories/categories */ \"./src/components/categories/categories.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_banners_banner__WEBPACK_IMPORTED_MODULE_1__, _components_categories_categories__WEBPACK_IMPORTED_MODULE_2__]);\n([_components_banners_banner__WEBPACK_IMPORTED_MODULE_1__, _components_categories_categories__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nfunction MinimalLayout({ variables }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_banners_banner__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                layout: \"minimal\",\n                variables: variables.types\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\minimal.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_categories_categories__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                layout: \"minimal\",\n                variables: variables.categories\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\minimal.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9sYXlvdXRzL21pbmltYWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFpRDtBQUNXO0FBRzdDLFNBQVNFLGNBQWMsRUFBRUMsU0FBUyxFQUFpQjtJQUNoRSxxQkFDRTs7MEJBQ0UsOERBQUNILGtFQUFNQTtnQkFBQ0ksUUFBTztnQkFBVUQsV0FBV0EsVUFBVUUsS0FBSzs7Ozs7OzBCQUNuRCw4REFBQ0oseUVBQVVBO2dCQUFDRyxRQUFPO2dCQUFVRCxXQUFXQSxVQUFVRyxVQUFVOzs7Ozs7OztBQUdsRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BwaWNrLWJhemFyL3Nob3AvLi9zcmMvY29tcG9uZW50cy9sYXlvdXRzL21pbmltYWwudHN4PzhlZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEJhbm5lciBmcm9tICdAL2NvbXBvbmVudHMvYmFubmVycy9iYW5uZXInO1xuaW1wb3J0IENhdGVnb3JpZXMgZnJvbSAnQC9jb21wb25lbnRzL2NhdGVnb3JpZXMvY2F0ZWdvcmllcyc7XG5pbXBvcnQgdHlwZSB7IEhvbWVQYWdlUHJvcHMgfSBmcm9tICdAL3R5cGVzJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWluaW1hbExheW91dCh7IHZhcmlhYmxlcyB9OiBIb21lUGFnZVByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIDxCYW5uZXIgbGF5b3V0PVwibWluaW1hbFwiIHZhcmlhYmxlcz17dmFyaWFibGVzLnR5cGVzfSAvPlxuICAgICAgPENhdGVnb3JpZXMgbGF5b3V0PVwibWluaW1hbFwiIHZhcmlhYmxlcz17dmFyaWFibGVzLmNhdGVnb3JpZXN9IC8+XG4gICAgPC8+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQmFubmVyIiwiQ2F0ZWdvcmllcyIsIk1pbmltYWxMYXlvdXQiLCJ2YXJpYWJsZXMiLCJsYXlvdXQiLCJ0eXBlcyIsImNhdGVnb3JpZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/layouts/minimal.tsx\n");

/***/ })

};
;