"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_ui_auto-suggestion_tsx";
exports.ids = ["src_components_ui_auto-suggestion_tsx"];
exports.modules = {

/***/ "__barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Transition: () => (/* reexport safe */ E_Projects_BB_Projects_e_commerce_logorithm_e_site_shop_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_0__.Transition)
/* harmony export */ });
/* harmony import */ var E_Projects_BB_Projects_e_commerce_logorithm_e_site_shop_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/transitions/transition.js */ "./node_modules/@headlessui/react/dist/components/transitions/transition.js");



/***/ }),

/***/ "./src/components/ui/auto-suggestion.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/auto-suggestion.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scrollbar */ \"./src/components/ui/scrollbar.tsx\");\n/* harmony import */ var _components_ui_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/image */ \"./src/components/ui/image.tsx\");\n/* harmony import */ var _lib_placeholders__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/placeholders */ \"./src/lib/placeholders.tsx\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__]);\n_components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\nconst AutoSuggestion = ({ className, suggestions, visible, notFound, showLoaders, seeMore, seeMoreLink })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleClick = (path)=>{\n        router.push(path);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_9__.Transition, {\n        show: visible,\n        enter: \"transition-opacity duration-75\",\n        enterFrom: \"opacity-0\",\n        enterTo: \"opacity-100\",\n        leave: \"transition-opacity duration-150\",\n        leaveFrom: \"opacity-100\",\n        leaveTo: \"opacity-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_2___default()(\"absolute top-11 left-0 mt-2 w-full lg:top-16 lg:mt-1\", className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full w-full rounded-lg bg-white py-2 shadow-downfall-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-full w-full\",\n                        children: [\n                            notFound && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"flex h-full w-full items-center justify-center py-10 font-semibold text-gray-400\",\n                                children: t(\"text-no-products\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, undefined),\n                            showLoaders && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-full w-full items-center justify-center py-14\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    simple: true,\n                                    className: \"h-9 w-9\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 15\n                            }, undefined),\n                            !notFound && !showLoaders && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-h-52\",\n                                children: suggestions?.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>handleClick(_config_routes__WEBPACK_IMPORTED_MODULE_7__.Routes.product(item?.slug)),\n                                        className: \"flex w-full cursor-pointer items-center border-b border-border-100 px-5 py-2 transition-colors last:border-b-0 hover:bg-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-8 w-8 overflow-hidden rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                                    className: \"h-full w-full\",\n                                                    src: item?.image?.original ?? _lib_placeholders__WEBPACK_IMPORTED_MODULE_6__.productPlaceholder,\n                                                    alt: item?.name ?? \"\",\n                                                    width: 100,\n                                                    height: 100\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-semibold text-heading ltr:ml-3 rtl:mr-3\",\n                                                children: item?.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, item?.slug, true, {\n                                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, undefined),\n                    seeMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full py-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: seeMoreLink,\n                            className: \"text-sm font-semibold text-accent transition-colors hover:text-accent-hover\",\n                            children: t(\"text-see-more\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AutoSuggestion);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/auto-suggestion.tsx\n");

/***/ })

};
;