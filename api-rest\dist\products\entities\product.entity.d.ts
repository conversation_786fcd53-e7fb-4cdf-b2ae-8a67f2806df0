import { Model } from 'sequelize-typescript';
import { AttributeValue } from 'src/attributes/entities/attribute-value.entity';
import { Category } from 'src/categories/entities/category.entity';
import { Shop } from 'src/shops/entities/shop.entity';
import { Tag } from 'src/tags/entities/tag.entity';
import { Type } from 'src/types/entities/type.entity';
import { Review } from '../../reviews/entities/review.entity';
import { CoreEntity } from '../../common/entities/core.entity';
declare enum ProductStatus {
    PUBLISH = "publish",
    DRAFT = "draft"
}
declare enum ProductType {
    SIMPLE = "simple",
    VARIABLE = "variable"
}
export declare class Product extends Model {
    id: number;
    name: string;
    slug: string;
    type_id: number;
    type: Type;
    product_type: ProductType;
    categories: Category[];
    tags?: Tag[];
    variations?: AttributeValue[];
    variation_options?: any[];
    pivot?: any;
    shop_id: number;
    shop: Shop;
    related_products?: Product[];
    description: string;
    in_stock: boolean;
    is_taxable: boolean;
    sale_price?: number;
    max_price?: number;
    min_price?: number;
    sku?: string;
    gallery?: any[];
    image?: any;
    status: ProductStatus;
    height?: string;
    length?: string;
    width?: string;
    price?: number;
    quantity: number;
    unit: string;
    ratings: number;
    in_wishlist: boolean;
    my_review?: Review[];
    language?: string;
    translated_languages?: string;
    visibility?: string;
    created_at: Date;
    updated_at: Date;
}
export declare class ProductTag extends Model {
    product_id: number;
    tag_id: number;
}
export declare class OrderProductPivot {
    variation_option_id?: number;
    order_quantity: number;
    unit_price: number;
    subtotal: number;
}
export declare class Variation {
    id: number;
    title: string;
    price: number;
    sku: string;
    is_disable: boolean;
    sale_price?: number;
    quantity: number;
    options: VariationOption[];
}
export declare class VariationOption {
    name: string;
    value: string;
}
export declare class File extends CoreEntity {
    attachment_id: number;
    url: string;
    fileable_id: number;
}
export {};
