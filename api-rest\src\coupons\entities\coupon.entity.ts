import {
  Column,
  Model,
  Table,
  DataType,
  HasMany,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Shop } from 'src/shops/entities/shop.entity';

export enum CouponType {
  FIXED_COUPON = 'fixed',
  PERCENTAGE_COUPON = 'percentage',
  FREE_SHIPPING_COUPON = 'free_shipping',
  DEFAULT_COUPON = 'fixed',
}

@Table({
  tableName: 'coupons',
  timestamps: true,
})
export class Coupon extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  code: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description?: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  minimum_cart_amount: number;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  orders?: any[];

  @Column({
    type: DataType.ENUM('fixed', 'percentage', 'free_shipping'),
    allowNull: false,
    defaultValue: 'fixed',
  })
  type: CouponType;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  image: any; // Attachment

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
  })
  is_valid: boolean;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  amount: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  active_from: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  expire_at: Date;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'en',
  })
  language: string;

  @Column({
    type: DataType.ARRAY(DataType.STRING),
    allowNull: true,
  })
  translated_languages: string[];

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  target?: boolean;

  @ForeignKey(() => Shop)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  shop_id?: number;

  @BelongsTo(() => Shop)
  shop?: Shop;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  is_approve?: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}
