const { Sequelize } = require('sequelize');
require('dotenv').config();

// Database connection
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USERNAME,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: process.env.DB_DIALECT,
    logging: false,
    dialectOptions: {
      ssl: {
        require: true,
        rejectUnauthorized: false,
      },
    },
  },
);

async function addSampleData() {
  try {
    await sequelize.authenticate();
    console.log('Database connected successfully.');

    // First, let's see what tables exist
    const [tables] = await sequelize.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);

    console.log(
      'Available tables:',
      tables.map((t) => t.table_name),
    );

    // Check the structure of the types table
    const [columns] = await sequelize.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'types'
      ORDER BY ordinal_position;
    `);

    console.log('Types table columns:', columns);

    // Add a default type
    const [type] = await sequelize.query(`
      INSERT INTO "types" (name, slug, image, icon, promotional_sliders, settings, language, created_at, updated_at, "createdAt", "updatedAt")
      VALUES (
        'General',
        'general',
        '{}',
        'ShoppingBagIcon',
        '[]',
        '{"isHome": true, "layoutType": "classic", "productCard": "neon"}',
        'en',
        NOW(),
        NOW(),
        NOW(),
        NOW()
      )
      ON CONFLICT (slug) DO NOTHING
      RETURNING id;
    `);

    console.log('Default type added:', type);

    // Skip category for now - we have a type which is enough for the shop to work

    console.log('Sample data added successfully!');
  } catch (error) {
    console.error('Error adding sample data:', error);
  } finally {
    await sequelize.close();
  }
}

addSampleData();
