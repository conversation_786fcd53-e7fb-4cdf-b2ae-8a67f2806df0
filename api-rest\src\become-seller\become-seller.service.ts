import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { BecomeSeller } from './entities/become-seller.entity';
import { CreateBecomeSellerDto } from './dto/create-become-seller.dto';
import { UpdateBecomeSellerDto } from './dto/update-become-seller.dto';

@Injectable()
export class BecomeSellerService {
  constructor(
    @InjectModel(BecomeSeller)
    private becomeSellerModel: typeof BecomeSeller,
  ) {}

  async create(
    createBecomeSellerDto: CreateBecomeSellerDto,
  ): Promise<BecomeSeller> {
    return this.becomeSellerModel.create(createBecomeSellerDto as any);
  }

  async findAll(): Promise<BecomeSeller[]> {
    return this.becomeSellerModel.findAll();
  }

  async findOne(id: number): Promise<BecomeSeller | null> {
    return this.becomeSellerModel.findByPk(id);
  }

  async update(
    id: number,
    updateBecomeSellerDto: UpdateBecomeSellerDto,
  ): Promise<BecomeSeller | null> {
    await this.becomeSellerModel.update(updateBecomeSellerDto as any, {
      where: { id },
    });
    return this.becomeSellerModel.findByPk(id);
  }

  remove(id: number) {
    return `This action removes a #${id} Become Seller`;
  }
}
