#!/bin/bash

# oneKart E-Commerce Development Startup Script
# This script starts all services in development mode

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[oneKart]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}  oneKart E-Commerce Platform${NC}"
    echo -e "${PURPLE}     Development Mode${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    if command_exists lsof; then
        lsof -i :$1 >/dev/null 2>&1
    elif command_exists netstat; then
        netstat -an | grep :$1 >/dev/null 2>&1
    else
        return 1
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    print_status "Checking port $port..."
    
    if port_in_use $port; then
        print_warning "Port $port is in use. Attempting to free it..."
        if command_exists lsof; then
            lsof -ti :$port | xargs kill -9 2>/dev/null || true
        elif command_exists netstat && command_exists taskkill; then
            # Windows with Git Bash
            netstat -ano | findstr :$port | awk '{print $5}' | xargs taskkill /PID /F 2>/dev/null || true
        fi
        sleep 2
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" >/dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    fi
    
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node -v)"
        exit 1
    fi
    
    # Check npm
    if ! command_exists npm; then
        print_error "npm is not installed."
        exit 1
    fi
    
    # Check curl for health checks
    if ! command_exists curl; then
        print_warning "curl is not installed. Health checks will be skipped."
    fi
    
    print_success "Prerequisites check passed"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install API dependencies
    print_status "Installing API dependencies..."
    cd api-rest
    if [ -f "package-lock.json" ]; then
        npm ci --legacy-peer-deps
    else
        npm install --legacy-peer-deps
    fi
    cd ..
    
    # Install Admin dependencies
    print_status "Installing Admin dependencies..."
    cd admin-rest
    if [ -f "yarn.lock" ]; then
        yarn install --frozen-lockfile
    elif [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    cd ..
    
    # Install Shop dependencies
    print_status "Installing Shop dependencies..."
    cd shop
    if [ -f "yarn.lock" ]; then
        yarn install --frozen-lockfile
    elif [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    cd ..
    
    print_success "Dependencies installed successfully"
}

# Function to start API server
start_api() {
    print_status "Starting API server..."
    kill_port 9000

    cd api-rest
    # Use nohup to ensure background execution
    nohup npm run start:dev > ../api.log 2>&1 &
    API_PID=$!
    echo $API_PID > ../api.pid
    cd ..

    print_status "API server starting with PID: $API_PID"

    # Wait for API to be ready
    if command_exists curl; then
        wait_for_service "http://localhost:9000/api" "API Server"
    else
        print_status "Waiting 20 seconds for API server to start..."
        sleep 20
    fi

    print_success "API server started on http://localhost:9000"
}

# Function to start Admin dashboard
start_admin() {
    print_status "Starting Admin dashboard..."
    kill_port 3002

    cd admin-rest
    # Use nohup to ensure background execution
    nohup npm run dev > ../admin.log 2>&1 &
    ADMIN_PID=$!
    echo $ADMIN_PID > ../admin.pid
    cd ..

    print_status "Admin dashboard starting with PID: $ADMIN_PID"

    # Wait for Admin to be ready
    if command_exists curl; then
        wait_for_service "http://localhost:3002" "Admin Dashboard"
    else
        print_status "Waiting 25 seconds for Admin dashboard to start..."
        sleep 25
    fi

    print_success "Admin dashboard started on http://localhost:3002"
}

# Function to start Shop frontend
start_shop() {
    print_status "Starting Shop frontend..."
    kill_port 3005

    cd shop
    # Use nohup to ensure background execution
    nohup npx next dev -p 3005 > ../shop.log 2>&1 &
    SHOP_PID=$!
    echo $SHOP_PID > ../shop.pid
    cd ..

    print_status "Shop frontend starting with PID: $SHOP_PID"

    # Wait for Shop to be ready
    if command_exists curl; then
        wait_for_service "http://localhost:3005" "Shop Frontend"
    else
        print_status "Waiting 25 seconds for Shop frontend to start..."
        sleep 25
    fi

    print_success "Shop frontend started on http://localhost:3005"
}

# Function to display running services
show_services() {
    echo ""
    print_header
    echo ""
    print_success "🚀 oneKart E-Commerce Platform is running in development mode!"
    echo ""
    echo -e "${BLUE}📊 Admin Dashboard:${NC} http://localhost:3002"
    echo -e "${BLUE}🛍️  Shop Frontend:${NC}   http://localhost:3005"
    echo -e "${BLUE}🔧 API Server:${NC}      http://localhost:9000/api"
    echo ""
    echo -e "${YELLOW}💡 Features:${NC}"
    echo "   • Purple Nebula Dark Theme"
    echo "   • oneKart Branding"
    echo "   • Hot Reload Enabled"
    echo "   • Development Tools Active"
    echo ""
    echo -e "${GREEN}Press Ctrl+C to stop all services${NC}"
    echo ""
}

# Function to cleanup on exit
cleanup() {
    print_status "Shutting down services..."

    # Kill processes using PID files
    if [ -f "api.pid" ]; then
        API_PID=$(cat api.pid)
        kill $API_PID 2>/dev/null || true
        rm -f api.pid
    fi

    if [ -f "admin.pid" ]; then
        ADMIN_PID=$(cat admin.pid)
        kill $ADMIN_PID 2>/dev/null || true
        rm -f admin.pid
    fi

    if [ -f "shop.pid" ]; then
        SHOP_PID=$(cat shop.pid)
        kill $SHOP_PID 2>/dev/null || true
        rm -f shop.pid
    fi

    # Kill any remaining processes on our ports
    kill_port 9000
    kill_port 3002
    kill_port 3005

    # Clean up log files
    rm -f api.log admin.log shop.log

    print_success "All services stopped and cleaned up"
    exit 0
}

# Main execution
main() {
    print_header
    
    # Set up signal handlers
    trap cleanup SIGINT SIGTERM
    
    # Check prerequisites
    check_prerequisites
    
    # Install dependencies if --install flag is provided
    if [ "$1" = "--install" ] || [ "$1" = "-i" ]; then
        install_dependencies
    fi
    
    # Start services
    start_api
    start_admin
    start_shop
    
    # Show running services
    show_services
    
    # Keep script running
    while true; do
        sleep 1
    done
}

# Run main function with all arguments
main "$@"
