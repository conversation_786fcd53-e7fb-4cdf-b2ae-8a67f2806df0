import { Model } from 'sequelize-typescript';
import { Shop } from 'src/shops/entities/shop.entity';
export declare class Withdraw extends Model {
    id: number;
    amount: number;
    status: WithdrawStatus;
    shop_id: number;
    shop: Shop;
    payment_method: string;
    details: string;
    note: string;
}
export declare enum WithdrawStatus {
    APPROVED = "Approved",
    PENDING = "Pending",
    ON_HOLD = "On hold",
    REJECTED = "Rejected",
    PROCESSING = "Processing"
}
