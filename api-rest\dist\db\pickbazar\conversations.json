[{"user_id": "6", "id": "1", "shop_id": "7", "unseen": 0, "created_at": "2021-03-08T10:26:13.000000Z", "updated_at": "2023-03-11T03:17:23.000Z", "shop": {"id": "7", "name": "Books Shop", "slug": "books-shop", "owner_id": "1", "owner": {"name": "Store Owner", "email": "<EMAIL>", "profile": {"contact": "12365141641631", "__typename": "Profile"}, "__typename": "User"}, "staffs": [], "description": "This is the book shop. All the publications sells their book under this book shop", "logo": {"id": "1613", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1613/conversions/Publisher-logo-thumbnail.jpg", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1613/Publisher-logo.png", "__typename": "Attachment"}, "cover_image": {"id": "1374", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1374/conversions/Cover-thumbnail.jpg", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1374/Cover.png", "__typename": "Attachment"}, "orders_count": 2, "products_count": 67, "is_active": true, "balance": {"admin_commission_rate": 10, "total_earnings": 0, "withdrawn_amount": 0, "current_balance": 0, "payment_info": {"account": "*********", "name": "book shop", "bank": "book bank", "email": "<EMAIL>", "__typename": "PaymentInfo"}, "__typename": "Balance"}, "address": {"street_address": "44444", "country": "Switzerland", "city": "<PERSON>ich", "state": "California", "zip": "8021", "__typename": "User<PERSON>ddress"}, "created_at": "2021-03-08T10:26:13.000000Z", "__typename": "Shop"}, "user": {"id": "6", "name": "Admin", "email": "<EMAIL>", "profile": null, "address": [], "wallet": null, "is_active": true, "__typename": "User"}, "latest_message": {"body": "New test", "conversation_id": "1", "created_at": "2021-03-08T10:26:13.000000Z", "updated_at": "2021-03-08T10:26:13.000000Z", "user_id": "1", "id": "2", "__typename": "Message"}, "__typename": "Conversation"}, {"user_id": "6", "id": "6", "shop_id": "2", "unseen": 0, "created_at": "2023-03-21 15:00:08", "updated_at": "2023-03-30 02:59:20", "shop": {"id": 2, "owner_id": 1, "name": "Clothing Shop", "slug": "clothing-shop", "description": "The clothing shop is the best shop around the city. This is being run under the store owner and our aim is to provide quality product and hassle free customer service.", "cover_image": {"id": "886", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/884/Untitled-4.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/884/conversions/Untitled-4-thumbnail.jpg"}, "logo": {"id": "896", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/894/fashion.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/894/conversions/fashion-thumbnail.jpg"}, "is_active": 1, "address": {"zip": "62656", "city": "Lincoln", "state": "Illinois", "country": "USA", "street_address": "4885  Spring Street"}, "settings": {"contact": "212901921221", "socials": [{"url": "https://www.facebook.com/", "icon": "FacebookIcon"}], "website": "https://redq.io/", "location": {"lat": 40.1576691, "lng": -89.38529779999999, "city": "Lincoln", "state": "IL", "country": "United States", "formattedAddress": "IL-121, Lincoln, IL, USA"}}, "created_at": "2021-06-27T03:47:10.000000Z", "updated_at": "2021-07-08T09:26:24.000000Z", "orders_count": 4, "products_count": 64, "owner": {"id": 1, "name": "Store Owner", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-06-27T04:13:00.000000Z", "updated_at": "2021-06-27T04:13:00.000000Z", "is_active": 1, "shop_id": null, "profile": {"id": 1, "avatar": {"id": "883", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/881/aatik-tasneem-7omHUGhhmZ0-unsplash%402x.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/881/conversions/aatik-tasneem-7omHUGhhmZ0-unsplash%402x-thumbnail.jpg"}, "bio": "This is the store owner and we have 6 shops under our banner. We are running all the shops to give our customers hassle-free service and quality products. Our goal is to provide best possible customer service and products for our clients", "socials": null, "contact": "12365141641631", "customer_id": 1, "created_at": "2021-06-30T11:20:29.000000Z", "updated_at": "2021-06-30T14:13:53.000000Z"}}}, "user": {"id": "6", "name": "Admin", "email": "<EMAIL>", "profile": null, "address": [], "wallet": null, "is_active": true, "__typename": "User"}, "latest_message": {"body": "rrerw", "conversation_id": "6", "created_at": "2023-03-30 02:59:20", "updated_at": "2023-03-30 02:59:20", "user_id": "6", "id": "38", "__typename": "Message"}, "__typename": "Conversation"}]