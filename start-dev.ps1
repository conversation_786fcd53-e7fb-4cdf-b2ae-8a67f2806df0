# oneKart E-Commerce Development Startup Script (PowerShell)
# This script starts all services in development mode on Windows

param(
    [switch]$Install,
    [switch]$Help
)

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Purple = "Magenta"
}

function Write-Status {
    param([string]$Message)
    Write-Host "[oneKart] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Write-Header {
    Write-Host "================================" -ForegroundColor $Colors.Purple
    Write-Host "  oneKart E-Commerce Platform" -ForegroundColor $Colors.Purple
    Write-Host "     Development Mode" -ForegroundColor $Colors.Purple
    Write-Host "================================" -ForegroundColor $Colors.Purple
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

function Stop-ProcessOnPort {
    param([int]$Port)
    Write-Status "Checking port $Port..."
    
    try {
        $processes = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | 
                    Select-Object -ExpandProperty OwningProcess -Unique
        
        foreach ($processId in $processes) {
            if ($processId -and $processId -ne 0) {
                Write-Warning "Stopping process $processId on port $Port"
                Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
            }
        }
        Start-Sleep -Seconds 2
    }
    catch {
        Write-Warning "Could not check/stop processes on port $Port"
    }
}

function Wait-ForService {
    param(
        [string]$Url,
        [string]$ServiceName,
        [int]$MaxAttempts = 30
    )
    
    Write-Status "Waiting for $ServiceName to be ready..."
    
    for ($i = 1; $i -le $MaxAttempts; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Write-Success "$ServiceName is ready!"
                return $true
            }
        }
        catch {
            Write-Host "." -NoNewline
            Start-Sleep -Seconds 2
        }
    }
    
    Write-Error "$ServiceName failed to start within expected time"
    return $false
}

function Test-Prerequisites {
    Write-Status "Checking prerequisites..."
    
    # Check Node.js
    if (-not (Test-Command "node")) {
        Write-Error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    }
    
    $nodeVersion = (node --version).Substring(1).Split('.')[0]
    if ([int]$nodeVersion -lt 18) {
        Write-Error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    }
    
    # Check npm
    if (-not (Test-Command "npm")) {
        Write-Error "npm is not installed."
        exit 1
    }
    
    Write-Success "Prerequisites check passed"
}

function Install-Dependencies {
    Write-Status "Installing dependencies..."
    
    # Install API dependencies
    Write-Status "Installing API dependencies..."
    Set-Location "api-rest"
    if (Test-Path "package-lock.json") {
        npm ci --legacy-peer-deps
    } else {
        npm install --legacy-peer-deps
    }
    Set-Location ".."
    
    # Install Admin dependencies
    Write-Status "Installing Admin dependencies..."
    Set-Location "admin-rest"
    if (Test-Path "yarn.lock") {
        yarn install --frozen-lockfile
    } elseif (Test-Path "package-lock.json") {
        npm ci
    } else {
        npm install
    }
    Set-Location ".."
    
    # Install Shop dependencies
    Write-Status "Installing Shop dependencies..."
    Set-Location "shop"
    if (Test-Path "yarn.lock") {
        yarn install --frozen-lockfile
    } elseif (Test-Path "package-lock.json") {
        npm ci
    } else {
        npm install
    }
    Set-Location ".."
    
    Write-Success "Dependencies installed successfully"
}

function Start-ApiServer {
    Write-Status "Starting API server..."
    Stop-ProcessOnPort 9000
    
    Set-Location "api-rest"
    $apiProcess = Start-Process -FilePath "npm" -ArgumentList "run", "start:dev" -PassThru -WindowStyle Minimized
    Set-Location ".."
    
    Write-Status "API server starting with PID: $($apiProcess.Id)"
    
    # Wait for API to be ready
    if (Test-Command "curl") {
        Wait-ForService "http://localhost:9000/api" "API Server"
    } else {
        Write-Status "Waiting 20 seconds for API server to start..."
        Start-Sleep -Seconds 20
    }
    
    Write-Success "API server started on http://localhost:9000"
    return $apiProcess
}

function Start-AdminDashboard {
    Write-Status "Starting Admin dashboard..."
    Stop-ProcessOnPort 3002
    
    Set-Location "admin-rest"
    $adminProcess = Start-Process -FilePath "npm" -ArgumentList "run", "dev" -PassThru -WindowStyle Minimized
    Set-Location ".."
    
    Write-Status "Admin dashboard starting with PID: $($adminProcess.Id)"
    
    # Wait for Admin to be ready
    if (Test-Command "curl") {
        Wait-ForService "http://localhost:3002" "Admin Dashboard"
    } else {
        Write-Status "Waiting 25 seconds for Admin dashboard to start..."
        Start-Sleep -Seconds 25
    }
    
    Write-Success "Admin dashboard started on http://localhost:3002"
    return $adminProcess
}

function Start-ShopFrontend {
    Write-Status "Starting Shop frontend..."
    Stop-ProcessOnPort 3005
    
    Set-Location "shop"
    $shopProcess = Start-Process -FilePath "npx" -ArgumentList "next", "dev", "-p", "3005" -PassThru -WindowStyle Minimized
    Set-Location ".."
    
    Write-Status "Shop frontend starting with PID: $($shopProcess.Id)"
    
    # Wait for Shop to be ready
    if (Test-Command "curl") {
        Wait-ForService "http://localhost:3005" "Shop Frontend"
    } else {
        Write-Status "Waiting 25 seconds for Shop frontend to start..."
        Start-Sleep -Seconds 25
    }
    
    Write-Success "Shop frontend started on http://localhost:3005"
    return $shopProcess
}

function Show-Services {
    Write-Host ""
    Write-Header
    Write-Host ""
    Write-Success "🚀 oneKart E-Commerce Platform is running in development mode!"
    Write-Host ""
    Write-Host "📊 Admin Dashboard: http://localhost:3002" -ForegroundColor $Colors.Blue
    Write-Host "🛍️  Shop Frontend:   http://localhost:3005" -ForegroundColor $Colors.Blue
    Write-Host "🔧 API Server:      http://localhost:9000/api" -ForegroundColor $Colors.Blue
    Write-Host ""
    Write-Host "💡 Features:" -ForegroundColor $Colors.Yellow
    Write-Host "   • Purple Nebula Dark Theme"
    Write-Host "   • oneKart Branding"
    Write-Host "   • Hot Reload Enabled"
    Write-Host "   • Development Tools Active"
    Write-Host ""
    Write-Host "Press Ctrl+C to stop all services" -ForegroundColor $Colors.Green
    Write-Host ""
}

function Show-Help {
    Write-Host "oneKart Development Server - PowerShell Script"
    Write-Host ""
    Write-Host "Usage: .\start-dev.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Install    Install dependencies before starting"
    Write-Host "  -Help       Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\start-dev.ps1                # Start with existing dependencies"
    Write-Host "  .\start-dev.ps1 -Install       # Install dependencies and start"
    Write-Host ""
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Write-Header

# Check prerequisites
Test-Prerequisites

# Install dependencies if requested
if ($Install) {
    Install-Dependencies
}

# Start services
$apiProcess = Start-ApiServer
$adminProcess = Start-AdminDashboard
$shopProcess = Start-ShopFrontend

# Show running services
Show-Services

# Open applications in browser
Write-Host "Opening applications in browser..."
Start-Process "http://localhost:3002"
Start-Process "http://localhost:3005"

# Keep script running and handle cleanup
try {
    Write-Host "Services are running. Press Ctrl+C to stop all services."
    while ($true) {
        Start-Sleep -Seconds 1
    }
}
finally {
    Write-Status "Shutting down services..."
    
    if ($apiProcess -and -not $apiProcess.HasExited) {
        $apiProcess.Kill()
    }
    if ($adminProcess -and -not $adminProcess.HasExited) {
        $adminProcess.Kill()
    }
    if ($shopProcess -and -not $shopProcess.HasExited) {
        $shopProcess.Kill()
    }
    
    # Clean up any remaining processes on ports
    Stop-ProcessOnPort 9000
    Stop-ProcessOnPort 3002
    Stop-ProcessOnPort 3005
    
    Write-Success "All services stopped"
}
