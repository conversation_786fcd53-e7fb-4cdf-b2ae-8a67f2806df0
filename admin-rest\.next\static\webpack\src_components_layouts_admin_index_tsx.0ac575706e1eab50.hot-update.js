"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src_components_layouts_admin_index_tsx",{

/***/ "./src/components/layouts/navigation/sidebar-item.tsx":
/*!************************************************************!*\
  !*** ./src/components/layouts/navigation/sidebar-item.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/link */ \"./src/components/ui/link.tsx\");\n/* harmony import */ var _utils_get_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/get-icon */ \"./src/utils/get-icon.tsx\");\n/* harmony import */ var _components_icons_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/icons/sidebar */ \"./src/components/icons/sidebar/index.tsx\");\n/* harmony import */ var _contexts_ui_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ui.context */ \"./src/contexts/ui.context.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_icons_chevron_right__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/icons/chevron-right */ \"./src/components/icons/chevron-right.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _utils_auth_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/auth-utils */ \"./src/utils/auth-utils.ts\");\n/* harmony import */ var _components_ui_advance_popover__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/advance-popover */ \"./src/components/ui/advance-popover.tsx\");\n/* harmony import */ var _utils_use_window_size__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/use-window-size */ \"./src/utils/use-window-size.ts\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/constants */ \"./src/utils/constants.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SidebarShortItem(param) {\n    let { childMenu, shop, label, currentUserPermissions, icon, miniSidebar } = param;\n    _s();\n    const { closeSidebar } = (0,_contexts_ui_context__WEBPACK_IMPORTED_MODULE_4__.useUI)();\n    const [dropdown, setDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const sanitizedPath = router.asPath.split(\"#\")[0].split(\"?\")[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_advance_popover__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        onMouseEnter: ()=>setDropdown(true),\n        onMouseLeave: ()=>setDropdown(false),\n        content: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: childMenu === null || childMenu === void 0 ? void 0 : childMenu.map((item, index)=>{\n                    if (shop && !(0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_10__.hasAccess)(item === null || item === void 0 ? void 0 : item.permissions, currentUserPermissions)) return null;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            passHref: true,\n                            as: shop ? item === null || item === void 0 ? void 0 : item.href(shop === null || shop === void 0 ? void 0 : shop.toString()) : item === null || item === void 0 ? void 0 : item.href,\n                            href: {\n                                pathname: \"\".concat(shop ? item === null || item === void 0 ? void 0 : item.href(shop === null || shop === void 0 ? void 0 : shop.toString()) : item === null || item === void 0 ? void 0 : item.href),\n                                query: {\n                                    parents: label\n                                }\n                            },\n                            className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"relative flex w-full cursor-pointer items-center rounded-lg py-2 text-sm text-start focus:text-primary-accent\", (shop ? sanitizedPath === (item === null || item === void 0 ? void 0 : item.href(shop === null || shop === void 0 ? void 0 : shop.toString())) : sanitizedPath === (item === null || item === void 0 ? void 0 : item.href)) ? \"bg-secondary-bg font-medium text-primary-accent border-l-4 border-primary-accent\" : \"text-primary-text hover:text-primary-accent focus:text-primary-accent\"),\n                            title: t(item === null || item === void 0 ? void 0 : item.label),\n                            onClick: ()=>closeSidebar(),\n                            children: t(item === null || item === void 0 ? void 0 : item.label)\n                        }, void 0, false, void 0, void 0)\n                    }, index, false, void 0, void 0);\n                })\n            }, void 0, false),\n        isPopover: true,\n        isOpen: dropdown,\n        placement: \"left\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_6___default()('relative flex w-full cursor-pointer items-center px-3 py-2.5 text-sm text-secondary-text before:absolute before:-right-5 before:top-0 before:h-full before:w-5 before:content-[\"\"]', miniSidebar ? \"hover:text-primary-accent ltr:pl-3 rtl:pr-3\" : null),\n            children: (0,_utils_get_icon__WEBPACK_IMPORTED_MODULE_2__.getIcon)({\n                iconList: _components_icons_sidebar__WEBPACK_IMPORTED_MODULE_3__,\n                iconName: icon,\n                className: \"w-5 h-5\"\n            })\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(SidebarShortItem, \"Sw87sSIZEdITtiLXN4/V2paigFM=\", false, function() {\n    return [\n        _contexts_ui_context__WEBPACK_IMPORTED_MODULE_4__.useUI,\n        next_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c = SidebarShortItem;\nconst SidebarItem = (param)=>{\n    let { href, icon, label, childMenu, miniSidebar } = param;\n    var _router_asPath_split_, _router_asPath, _router_query;\n    _s1();\n    const { closeSidebar } = (0,_contexts_ui_context__WEBPACK_IMPORTED_MODULE_4__.useUI)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { width } = (0,_utils_use_window_size__WEBPACK_IMPORTED_MODULE_12__.useWindowSize)();\n    const { query: { shop }, locale, pathname } = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const sanitizedPath = router === null || router === void 0 ? void 0 : (_router_asPath = router.asPath) === null || _router_asPath === void 0 ? void 0 : (_router_asPath_split_ = _router_asPath.split(\"#\")[0]) === null || _router_asPath_split_ === void 0 ? void 0 : _router_asPath_split_.split(\"?\")[0];\n    const isParents = router === null || router === void 0 ? void 0 : (_router_query = router.query) === null || _router_query === void 0 ? void 0 : _router_query.parents;\n    const isActive = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(()=>{\n        var _router_asPath, _label_trim, _router_asPath_trim_toUpperCase, _router_asPath_trim, _router_asPath1;\n        if (isParents) {\n            return isParents === label;\n        }\n        let lastIndex = router === null || router === void 0 ? void 0 : (_router_asPath = router.asPath) === null || _router_asPath === void 0 ? void 0 : _router_asPath.lastIndexOf(\"/\");\n        if (label !== \"Settings\") {\n            var _router_asPath_substring_replace_trim, _router_asPath_substring_replace, _router_asPath_substring, _router_asPath2, _label_trim1;\n            return (router === null || router === void 0 ? void 0 : (_router_asPath2 = router.asPath) === null || _router_asPath2 === void 0 ? void 0 : (_router_asPath_substring = _router_asPath2.substring(lastIndex + 1)) === null || _router_asPath_substring === void 0 ? void 0 : (_router_asPath_substring_replace = _router_asPath_substring.replace(/[^a-zA-Z ]/g, \" \")) === null || _router_asPath_substring_replace === void 0 ? void 0 : (_router_asPath_substring_replace_trim = _router_asPath_substring_replace.trim()) === null || _router_asPath_substring_replace_trim === void 0 ? void 0 : _router_asPath_substring_replace_trim.toUpperCase()) === (label === null || label === void 0 ? void 0 : (_label_trim1 = label.trim()) === null || _label_trim1 === void 0 ? void 0 : _label_trim1.toUpperCase());\n        }\n        return router === null || router === void 0 ? void 0 : (_router_asPath1 = router.asPath) === null || _router_asPath1 === void 0 ? void 0 : (_router_asPath_trim = _router_asPath1.trim()) === null || _router_asPath_trim === void 0 ? void 0 : (_router_asPath_trim_toUpperCase = _router_asPath_trim.toUpperCase()) === null || _router_asPath_trim_toUpperCase === void 0 ? void 0 : _router_asPath_trim_toUpperCase.includes(label === null || label === void 0 ? void 0 : (_label_trim = label.trim()) === null || _label_trim === void 0 ? void 0 : _label_trim.toUpperCase());\n    }, [\n        router === null || router === void 0 ? void 0 : router.asPath,\n        isParents\n    ]);\n    href = href && href !== \"/\" && (href === null || href === void 0 ? void 0 : href.endsWith(\"/\")) ? href === null || href === void 0 ? void 0 : href.slice(0, -1) : href;\n    const [isOpen, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(isActive);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        setOpen(isActive);\n    }, [\n        isActive\n    ]);\n    const toggleCollapse = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(()=>{\n        setOpen((prevValue)=>!prevValue);\n    }, [\n        isOpen\n    ]);\n    const onClick = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(()=>{\n        if (Array.isArray(childMenu) && !!childMenu.length) {\n            toggleCollapse();\n        }\n    }, [\n        isOpen\n    ]);\n    const { permissions: currentUserPermissions } = (0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_10__.getAuthCredentials)();\n    return childMenu && (childMenu === null || childMenu === void 0 ? void 0 : childMenu.length) ? miniSidebar && width >= _utils_constants__WEBPACK_IMPORTED_MODULE_13__.RESPONSIVE_WIDTH ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarShortItem, {\n        currentUserPermissions: currentUserPermissions,\n        shop: shop,\n        label: label,\n        childMenu: childMenu,\n        icon: icon,\n        miniSidebar: miniSidebar && width >= _utils_constants__WEBPACK_IMPORTED_MODULE_13__.RESPONSIVE_WIDTH\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n        lineNumber: 163,\n        columnNumber: 7\n    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                initial: false,\n                className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"group cursor-pointer rounded-md px-3 py-2.5 text-body-dark hover:bg-gray-100 focus:text-accent\", isOpen ? \"bg-gray-100 font-medium\" : \"\"),\n                onClick: onClick,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"flex w-full items-center text-sm\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-600\",\n                            children: (0,_utils_get_icon__WEBPACK_IMPORTED_MODULE_2__.getIcon)({\n                                iconList: _components_icons_sidebar__WEBPACK_IMPORTED_MODULE_3__,\n                                iconName: icon,\n                                className: \"w-5 h-5 me-3\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: width >= _utils_constants__WEBPACK_IMPORTED_MODULE_13__.RESPONSIVE_WIDTH && miniSidebar ? \"hidden\" : \"\",\n                            children: label\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_chevron_right__WEBPACK_IMPORTED_MODULE_8__.ChevronRight, {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"h-3.5 w-3.5 shrink-0 opacity-75 transition-transform duration-300 ltr:ml-auto ltr:mr-0 rtl:mr-auto rtl:ml-0\", isOpen ? \"rotate-90 transform\" : \"\", width >= _utils_constants__WEBPACK_IMPORTED_MODULE_13__.RESPONSIVE_WIDTH && miniSidebar ? \"hidden\" : \"\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                initial: false,\n                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: \"collapsed\",\n                    animate: \"open\",\n                    exit: \"collapsed\",\n                    variants: {\n                        open: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        collapsed: {\n                            opacity: 0,\n                            height: 0\n                        }\n                    },\n                    transition: {\n                        duration: 0.35,\n                        ease: [\n                            0.33,\n                            1,\n                            0.68,\n                            1\n                        ]\n                    },\n                    className: miniSidebar ? \"relative\" : \"!mt-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-2 ltr:pl-5 rtl:pr-5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1 border-0 border-l border-dashed border-slate-300 ltr:pl-1 rtl:pr-1\",\n                            children: childMenu === null || childMenu === void 0 ? void 0 : childMenu.map((item, index)=>{\n                                if (shop && !(0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_10__.hasAccess)(item === null || item === void 0 ? void 0 : item.permissions, currentUserPermissions)) return null;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        passHref: true,\n                                        href: {\n                                            pathname: \"\".concat(shop ? item === null || item === void 0 ? void 0 : item.href(shop === null || shop === void 0 ? void 0 : shop.toString()) : item === null || item === void 0 ? void 0 : item.href),\n                                            query: {\n                                                parents: label\n                                            }\n                                        },\n                                        as: shop ? item === null || item === void 0 ? void 0 : item.href(shop === null || shop === void 0 ? void 0 : shop.toString()) : item === null || item === void 0 ? void 0 : item.href,\n                                        className: classnames__WEBPACK_IMPORTED_MODULE_6___default()('relative flex w-full cursor-pointer items-center rounded-lg py-2 px-5 text-sm text-start before:absolute before:-left-0.5 before:top-[18px] before:h-px before:w-3 before:border-t before:border-dashed before:border-gray-300 before:content-[\"\"] focus:text-accent', (shop ? sanitizedPath === (item === null || item === void 0 ? void 0 : item.href(shop === null || shop === void 0 ? void 0 : shop.toString())) : sanitizedPath === (item === null || item === void 0 ? void 0 : item.href)) ? \"bg-transparent font-medium text-accent-hover\" : \"text-body-dark hover:text-accent focus:text-accent\"),\n                                        title: t(item.label),\n                                        onClick: ()=>closeSidebar(),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: t(item.label)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 27\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 25\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 23\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 15\n                    }, undefined)\n                }, \"content\", false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        href: href,\n        className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"group flex w-full items-center gap-2.5 rounded-md px-3 py-2.5 text-sm text-gray-700 text-start focus:text-accent \".concat(miniSidebar && width >= _utils_constants__WEBPACK_IMPORTED_MODULE_13__.RESPONSIVE_WIDTH ? \"hover:text-accent-hover ltr:pl-3 rtl:pr-3\" : \"hover:bg-gray-100\"), sanitizedPath === href ? \"font-medium !text-accent-hover \".concat(!miniSidebar ? \"bg-accent/10 hover:!bg-accent/10\" : \"\") : \"\"),\n        title: label,\n        onClick: ()=>closeSidebar(),\n        children: [\n            icon ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"transition\", sanitizedPath === href ? \"text-accent-hover\" : \"text-gray-600 group-focus:text-accent\", miniSidebar && width >= _utils_constants__WEBPACK_IMPORTED_MODULE_13__.RESPONSIVE_WIDTH ? \"group-hover:text-accent\" : null),\n                children: (0,_utils_get_icon__WEBPACK_IMPORTED_MODULE_2__.getIcon)({\n                    iconList: _components_icons_sidebar__WEBPACK_IMPORTED_MODULE_3__,\n                    iconName: icon,\n                    className: \"w-5 h-5\"\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, undefined) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(miniSidebar && width >= _utils_constants__WEBPACK_IMPORTED_MODULE_13__.RESPONSIVE_WIDTH ? \"hidden\" : \"\"),\n                children: label\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\layouts\\\\navigation\\\\sidebar-item.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(SidebarItem, \"s++ktDOjWQO5SnClJlJZZESrJoE=\", false, function() {\n    return [\n        _contexts_ui_context__WEBPACK_IMPORTED_MODULE_4__.useUI,\n        next_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _utils_use_window_size__WEBPACK_IMPORTED_MODULE_12__.useWindowSize,\n        next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c1 = SidebarItem;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarItem);\nvar _c, _c1;\n$RefreshReg$(_c, \"SidebarShortItem\");\n$RefreshReg$(_c1, \"SidebarItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layouts/navigation/sidebar-item.tsx\n"));

/***/ })

});