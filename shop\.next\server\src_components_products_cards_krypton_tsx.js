"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_products_cards_krypton_tsx";
exports.ids = ["src_components_products_cards_krypton_tsx"];
exports.modules = {

/***/ "./src/components/icons/external-icon.tsx":
/*!************************************************!*\
  !*** ./src/components/icons/external-icon.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExternalIcon: () => (/* binding */ ExternalIcon),\n/* harmony export */   ExternalIconNew: () => (/* binding */ ExternalIconNew)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ExternalIcon = ({ ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\nconst ExternalIconNew = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"1em\",\n        height: \"1em\",\n        viewBox: \"0 0 20 20\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                opacity: 0.4,\n                fillRule: \"evenodd\",\n                clipRule: \"evenodd\",\n                d: \"M1.668 6.667a2.5 2.5 0 012.5-2.5h5a.833.833 0 110 1.666h-5a.833.833 0 00-.833.834v9.166c0 .46.373.834.833.834h9.167c.46 0 .833-.373.833-.834v-5a.833.833 0 111.667 0v5a2.5 2.5 0 01-2.5 2.5H4.168a2.5 2.5 0 01-2.5-2.5V6.667z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                clipRule: \"evenodd\",\n                d: \"M18.34 7.5a.833.833 0 01-1.667 0V4.512L9.5 11.684a.833.833 0 11-1.179-1.178l7.172-7.173h-2.99a.833.833 0 110-1.666h5.002c.46 0 .833.373.833.833v5z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/external-icon.tsx\n");

/***/ }),

/***/ "./src/components/products/cards/krypton.tsx":
/*!***************************************************!*\
  !*** ./src/components/products/cards/krypton.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/link */ \"./src/components/ui/link.tsx\");\n/* harmony import */ var _components_ui_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/image */ \"./src/components/ui/image.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_use_price__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/use-price */ \"./src/lib/use-price.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _lib_placeholders__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/placeholders */ \"./src/lib/placeholders.tsx\");\n/* harmony import */ var _components_icons_external_icon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/icons/external-icon */ \"./src/components/icons/external-icon.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_use_price__WEBPACK_IMPORTED_MODULE_4__]);\n_lib_use_price__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\nconst Krypton = ({ product, className })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"common\");\n    const { query } = (0,next_router__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { name, image, slug, min_price, max_price, product_type, is_external } = product ?? {};\n    const { price, basePrice, discount } = (0,_lib_use_price__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        amount: product.sale_price ? product.sale_price : product.price,\n        baseAmount: product.price\n    });\n    const { price: minPrice } = (0,_lib_use_price__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        amount: min_price\n    });\n    const { price: maxPrice } = (0,_lib_use_price__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        amount: max_price\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        href: _config_routes__WEBPACK_IMPORTED_MODULE_6__.Routes.product(slug),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(\"product-card cart-type-krypton h-full cursor-pointer overflow-hidden rounded border border-border-200 bg-light transition-shadow duration-200 hover:shadow-sm\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(\"relative flex h-48 w-auto items-center justify-center sm:h-64\", query?.pages ? query?.pages?.includes(\"medicine\") ? \"m-4 mb-0\" : \"\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: t(\"text-product-image\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image__WEBPACK_IMPORTED_MODULE_2__.Image, {\n                            src: image?.original ?? _lib_placeholders__WEBPACK_IMPORTED_MODULE_7__.productPlaceholder,\n                            alt: name,\n                            fill: true,\n                            sizes: \"(max-width: 768px) 100vw\",\n                            className: \"product-image object-contain\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 rounded-full bg-yellow-500 px-2 text-xs font-semibold leading-6 text-light ltr:right-3 rtl:left-3 md:top-4 md:px-2.5 ltr:md:right-4 rtl:md:left-4\",\n                            children: discount\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"p-3 text-center md:p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mb-2 flex items-center justify-center gap-1 truncate text-sm font-semibold text-heading\",\n                            children: [\n                                name,\n                                is_external ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_external_icon__WEBPACK_IMPORTED_MODULE_8__.ExternalIcon, {\n                                    className: \"h-3.5 w-3.5 stroke-2 text-muted\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, undefined) : null\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined),\n                        product_type.toLowerCase() === \"variable\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-sub-heading\",\n                                    children: minPrice\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \" - \"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-sub-heading\",\n                                    children: maxPrice\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-sub-heading\",\n                                    children: price\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, undefined),\n                                basePrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"del\", {\n                                    className: \"text-sm text-muted ltr:ml-2 rtl:mr-2\",\n                                    children: basePrice\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Krypton);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/cards/krypton.tsx\n");

/***/ }),

/***/ "./src/lib/use-price.tsx":
/*!*******************************!*\
  !*** ./src/lib/use-price.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ usePrice),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatVariantPrice: () => (/* binding */ formatVariantPrice)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_framework_settings__WEBPACK_IMPORTED_MODULE_2__]);\n_framework_settings__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction formatPrice({ amount, currencyCode, locale, fractions }) {\n    const formatCurrency = new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency: currencyCode,\n        maximumFractionDigits: fractions\n    });\n    return formatCurrency.format(amount);\n}\nfunction formatVariantPrice({ amount, baseAmount, currencyCode, locale, fractions = 2 }) {\n    const hasDiscount = baseAmount > amount;\n    const formatDiscount = new Intl.NumberFormat(locale, {\n        style: \"percent\"\n    });\n    const discount = hasDiscount ? formatDiscount.format((baseAmount - amount) / baseAmount) : null;\n    const price = formatPrice({\n        amount,\n        currencyCode,\n        locale,\n        fractions\n    });\n    const basePrice = hasDiscount ? formatPrice({\n        amount: baseAmount,\n        currencyCode,\n        locale,\n        fractions\n    }) : null;\n    return {\n        price,\n        basePrice,\n        discount\n    };\n}\nfunction usePrice(data) {\n    const { settings } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_2__.useSettings)();\n    const currency = settings?.currency;\n    const currencyOptions = settings?.currencyOptions;\n    const { amount, baseAmount, currencyCode, currencyOptionsFormat } = {\n        ...data,\n        currencyCode: currency ?? \"USD\",\n        currencyOptionsFormat: currencyOptions ?? {\n            formation: \"en-US\",\n            fractions: 2\n        }\n    };\n    const { formation = \"en-US\", fractions = 2 } = currencyOptionsFormat;\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (typeof amount !== \"number\" || !currencyCode) return \"\";\n        const fractionalDigit = fractions ? fractions : 2;\n        let currentLocale = formation ? formation : \"en\";\n        // if (process.env.NEXT_PUBLIC_ENABLE_MULTI_LANG) {\n        //   currentLocale = locale ? locale : 'en';\n        // }\n        return baseAmount ? formatVariantPrice({\n            amount,\n            baseAmount,\n            currencyCode,\n            locale: currentLocale,\n            fractions: fractionalDigit\n        }) : formatPrice({\n            amount,\n            currencyCode,\n            locale: currentLocale,\n            fractions: fractionalDigit\n        });\n    }, [\n        amount,\n        baseAmount,\n        currencyCode,\n        locale\n    ]);\n    return typeof value === \"string\" ? {\n        price: value,\n        basePrice: null,\n        discount: null\n    } : value;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/use-price.tsx\n");

/***/ })

};
;