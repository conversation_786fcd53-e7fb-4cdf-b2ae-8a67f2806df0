"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_ui_search_search_tsx";
exports.ids = ["src_components_ui_search_search_tsx"];
exports.modules = {

/***/ "./src/components/ui/search/search.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/search/search.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_search_search_box__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/search/search-box */ \"./src/components/ui/search/search-box.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _search_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./search.context */ \"./src/components/ui/search/search.context.tsx\");\n\n\n\n\n\nconst Search = ({ label, variant, className, inputClassName, ...props })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { searchTerm, updateSearchTerm } = (0,_search_context__WEBPACK_IMPORTED_MODULE_4__.useSearch)();\n    const handleOnChange = (e)=>{\n        const { value } = e.target;\n        updateSearchTerm(value);\n    };\n    const onSearch = (e)=>{\n        e.preventDefault();\n        if (!searchTerm) return;\n        const { pathname, query } = router;\n        router.push({\n            pathname,\n            query: {\n                ...query,\n                text: searchTerm\n            }\n        }, undefined, {\n            scroll: false\n        });\n    };\n    function clearSearch() {\n        updateSearchTerm(\"\");\n        const { pathname, query } = router;\n        const { text, ...rest } = query;\n        if (text) {\n            router.push({\n                pathname,\n                query: {\n                    ...rest\n                }\n            }, undefined, {\n                scroll: false\n            });\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_search_search_box__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        label: label,\n        onSubmit: onSearch,\n        onClearSearch: clearSearch,\n        onChange: handleOnChange,\n        value: searchTerm,\n        name: \"search\",\n        placeholder: t(\"common:text-search-placeholder\"),\n        variant: variant,\n        className: className,\n        inputClassName: inputClassName,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\search\\\\search.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Search);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/search/search.tsx\n");

/***/ })

};
;