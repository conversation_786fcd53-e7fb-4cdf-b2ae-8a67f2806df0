import {
  Column,
  Model,
  Table,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  HasOne,
} from 'sequelize-typescript';
import { Order } from 'src/orders/entities/order.entity';
import { Shop } from 'src/shops/entities/shop.entity';
import { User } from 'src/users/entities/user.entity';
import { Product } from 'src/products/entities/product.entity';
import { Report } from './reports.entity';
import { Feedback } from 'src/feedbacks/entities/feedback.entity';

@Table({
  tableName: 'reviews',
  timestamps: true,
})
export class Review extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.DECIMAL(3, 2),
    allowNull: false,
    validate: {
      min: 0,
      max: 5,
    },
  })
  rating: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  comment: string;

  @ForeignKey(() => Shop)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  shop_id: number;

  @BelongsTo(() => Shop)
  shop: Shop;

  @ForeignKey(() => Order)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  order_id?: number;

  @BelongsTo(() => Order)
  order: Order;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  user_id: number;

  @BelongsTo(() => User, 'user_id')
  customer: User;

  @BelongsTo(() => User, 'user_id')
  user: User;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  photos: any[]; // Attachment[]

  @ForeignKey(() => Product)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  product_id: number;

  @BelongsTo(() => Product)
  product: Product;

  @HasMany(() => Feedback, 'review_id')
  feedbacks: Feedback[];

  @HasOne(() => Feedback, 'review_id')
  my_feedback: Feedback;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  positive_feedbacks_count: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  negative_feedbacks_count: number;

  @HasMany(() => Report, 'review_id')
  abusive_reports: Report[];

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  variation_option_id: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  abusive_reports_count?: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}
