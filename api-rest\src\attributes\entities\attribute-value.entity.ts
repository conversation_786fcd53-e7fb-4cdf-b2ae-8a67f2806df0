import {
  Column,
  Model,
  Table,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Attribute } from './attribute.entity';

@Table({
  tableName: 'attribute_values',
  timestamps: true,
})
export class AttributeValue extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  shop_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  value: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  meta?: string;

  @BelongsTo(() => Attribute)
  attribute: Attribute;
}
