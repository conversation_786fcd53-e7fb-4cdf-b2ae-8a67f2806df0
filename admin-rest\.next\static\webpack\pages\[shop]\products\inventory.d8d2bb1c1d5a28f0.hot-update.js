"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[shop]/products/inventory",{

/***/ "./src/pages/[shop]/products/inventory.tsx":
/*!*************************************************!*\
  !*** ./src/pages/[shop]/products/inventory.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; },\n/* harmony export */   \"default\": function() { return /* binding */ VendorProductInventoryPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/card */ \"./src/components/common/card.tsx\");\n/* harmony import */ var _components_ui_error_message__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/error-message */ \"./src/components/ui/error-message.tsx\");\n/* harmony import */ var _components_ui_loader_loader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/loader/loader */ \"./src/components/ui/loader/loader.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_common_search__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/common/search */ \"./src/components/common/search.tsx\");\n/* harmony import */ var _data_product__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/data/product */ \"./src/data/product.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_product_product_inventory_list__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/product/product-inventory-list */ \"./src/components/product/product-inventory-list.tsx\");\n/* harmony import */ var _data_user__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/data/user */ \"./src/data/user.ts\");\n/* harmony import */ var _utils_auth_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/auth-utils */ \"./src/utils/auth-utils.ts\");\n/* harmony import */ var _data_shop__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/data/shop */ \"./src/data/shop.ts\");\n/* harmony import */ var _components_layouts_shop__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/layouts/shop */ \"./src/components/layouts/shop/index.tsx\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _components_common_page_heading__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/common/page-heading */ \"./src/components/common/page-heading.tsx\");\n/* harmony import */ var _components_filters_category_type_filter__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/filters/category-type-filter */ \"./src/components/filters/category-type-filter.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _components_icons_arrow_down__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/icons/arrow-down */ \"./src/components/icons/arrow-down.tsx\");\n/* harmony import */ var _components_icons_arrow_up__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/icons/arrow-up */ \"./src/components/icons/arrow-up.tsx\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/types */ \"./src/types/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar __N_SSP = true;\nfunction VendorProductInventoryPage() {\n    var _me_shops, _me_managed_shop;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const { permissions } = (0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_11__.getAuthCredentials)();\n    const { data: me } = (0,_data_user__WEBPACK_IMPORTED_MODULE_10__.useMeQuery)();\n    const { query: { shop } } = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const { data: shopData, isLoading: fetchingShop } = (0,_data_shop__WEBPACK_IMPORTED_MODULE_12__.useShopQuery)({\n        slug: shop\n    });\n    const shopId = shopData === null || shopData === void 0 ? void 0 : shopData.id;\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(1);\n    const [orderBy, setOrder] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"created_at\");\n    const [sortedBy, setColumn] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(_types__WEBPACK_IMPORTED_MODULE_20__.SortOrder.Desc);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [type, setType] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [productType, setProductType] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const toggleVisible = ()=>{\n        setVisible((v)=>!v);\n    };\n    const { products, paginatorInfo, loading, error } = (0,_data_product__WEBPACK_IMPORTED_MODULE_7__.useProductsQuery)({\n        language: locale,\n        name: searchTerm,\n        limit: 20,\n        page,\n        orderBy,\n        sortedBy,\n        shop_id: shopId,\n        categories: category,\n        product_type: productType,\n        type\n    });\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loader_loader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        text: t(\"common:text-loading\")\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n        lineNumber: 73,\n        columnNumber: 23\n    }, this);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_error_message__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        message: error.message\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n        lineNumber: 74,\n        columnNumber: 21\n    }, this);\n    function handleSearch(param) {\n        let { searchText } = param;\n        setSearchTerm(searchText);\n        setPage(1);\n    }\n    function handlePagination(current) {\n        setPage(current);\n    }\n    if (!(0,_utils_auth_utils__WEBPACK_IMPORTED_MODULE_11__.hasAccess)(_utils_auth_utils__WEBPACK_IMPORTED_MODULE_11__.adminOnly, permissions) && !(me === null || me === void 0 ? void 0 : (_me_shops = me.shops) === null || _me_shops === void 0 ? void 0 : _me_shops.map((shop)=>shop.id).includes(shopId)) && (me === null || me === void 0 ? void 0 : (_me_managed_shop = me.managed_shop) === null || _me_managed_shop === void 0 ? void 0 : _me_managed_shop.id) != shopId) {\n        router.replace(_config_routes__WEBPACK_IMPORTED_MODULE_14__.Routes.dashboard);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"mb-8 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex w-full flex-col items-center md:flex-row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 md:mb-0 md:w-1/4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_page_heading__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    title: t(\"form:input-label-products\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex w-full flex-col items-center ms-auto md:w-2/4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_search__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    onSearch: handleSearch,\n                                    placeholderText: t(\"form:input-placeholder-search-name\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"mt-5 flex items-center whitespace-nowrap text-base font-semibold text-accent md:mt-0 md:ms-5\",\n                                onClick: toggleVisible,\n                                children: [\n                                    t(\"common:text-filter\"),\n                                    \" \",\n                                    visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_arrow_up__WEBPACK_IMPORTED_MODULE_19__.ArrowUp, {\n                                        className: \"ms-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_arrow_down__WEBPACK_IMPORTED_MODULE_18__.ArrowDown, {\n                                        className: \"ms-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_17___default()(\"flex w-full transition\", {\n                            \"visible h-auto\": visible,\n                            \"invisible h-0\": !visible\n                        }),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-5 flex w-full flex-col border-t border-gray-200 pt-5 md:mt-8 md:flex-row md:items-center md:pt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filters_category_type_filter__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-full\",\n                                type: type,\n                                onCategoryFilter: (category)=>{\n                                    setCategory(category === null || category === void 0 ? void 0 : category.slug);\n                                    setPage(1);\n                                },\n                                onTypeFilter: (type)=>{\n                                    setType(type === null || type === void 0 ? void 0 : type.slug);\n                                    setPage(1);\n                                },\n                                onProductTypeFilter: (productType)=>{\n                                    setProductType(productType === null || productType === void 0 ? void 0 : productType.slug);\n                                    setPage(1);\n                                },\n                                enableCategory: true,\n                                enableType: true,\n                                enableProductType: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_product_inventory_list__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                products: products,\n                paginatorInfo: paginatorInfo,\n                onPagination: handlePagination,\n                onOrder: setOrder,\n                onSort: setColumn\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\pages\\\\[shop]\\\\products\\\\inventory.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(VendorProductInventoryPage, \"yi4OLBXXSIoYOpgxyvyBMAulFlE=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _data_user__WEBPACK_IMPORTED_MODULE_10__.useMeQuery,\n        next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _data_shop__WEBPACK_IMPORTED_MODULE_12__.useShopQuery,\n        _data_product__WEBPACK_IMPORTED_MODULE_7__.useProductsQuery\n    ];\n});\n_c = VendorProductInventoryPage;\nVendorProductInventoryPage.authenticate = {\n    permissions: _utils_auth_utils__WEBPACK_IMPORTED_MODULE_11__.adminOwnerAndStaffOnly\n};\nVendorProductInventoryPage.Layout = _components_layouts_shop__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\nvar _c;\n$RefreshReg$(_c, \"VendorProductInventoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/[shop]/products/inventory.tsx\n"));

/***/ })

});