"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_tax_tax-delete-view_tsx"],{

/***/ "./src/components/tax/tax-delete-view.tsx":
/*!************************************************!*\
  !*** ./src/components/tax/tax-delete-view.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_tax__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/tax */ \"./src/data/tax.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst TaxDeleteView = ()=>{\n    _s();\n    const { mutate: deleteTax, isLoading: loading } = (0,_data_tax__WEBPACK_IMPORTED_MODULE_3__.useDeleteTaxMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    async function handleDelete() {\n        deleteTax({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\tax\\\\tax-delete-view.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TaxDeleteView, \"8ybHsEm9UU9YbDWl2ciwh30Vw1s=\", false, function() {\n    return [\n        _data_tax__WEBPACK_IMPORTED_MODULE_3__.useDeleteTaxMutation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction\n    ];\n});\n_c = TaxDeleteView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TaxDeleteView);\nvar _c;\n$RefreshReg$(_c, \"TaxDeleteView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/tax/tax-delete-view.tsx\n"));

/***/ }),

/***/ "./src/data/client/tax.ts":
/*!********************************!*\
  !*** ./src/data/client/tax.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   taxClient: function() { return /* binding */ taxClient; }\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\n\n\n\nconst taxClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES),\n    get (param) {\n        let { id } = param;\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(\"\".concat(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES, \"/\").concat(id));\n    },\n    paginated: (param)=>{\n        let { name, ...params } = param;\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    },\n    all: (param)=>{\n        let { name, ...params } = param;\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TAXES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/tax.ts\n"));

/***/ }),

/***/ "./src/data/tax.ts":
/*!*************************!*\
  !*** ./src/data/tax.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateTaxClassMutation: function() { return /* binding */ useCreateTaxClassMutation; },\n/* harmony export */   useDeleteTaxMutation: function() { return /* binding */ useDeleteTaxMutation; },\n/* harmony export */   useTaxQuery: function() { return /* binding */ useTaxQuery; },\n/* harmony export */   useTaxesQuery: function() { return /* binding */ useTaxesQuery; },\n/* harmony export */   useUpdateTaxClassMutation: function() { return /* binding */ useUpdateTaxClassMutation; }\n/* harmony export */ });\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _client_tax__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/tax */ \"./src/data/client/tax.ts\");\n\n\n\n\n\n\n\nconst useCreateTaxClassMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.create, {\n        onSuccess: ()=>{\n            router.push(_config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.tax.list);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES);\n        }\n    });\n};\nconst useDeleteTaxMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES);\n        }\n    });\n};\nconst useUpdateTaxClassMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.update, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES);\n        }\n    });\n};\nconst useTaxQuery = (id)=>{\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES,\n        id\n    ], ()=>_client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.get({\n            id\n        }));\n};\nconst useTaxesQuery = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.TAXES,\n        options\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _client_tax__WEBPACK_IMPORTED_MODULE_6__.taxClient.all(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        keepPreviousData: true\n    });\n    return {\n        taxes: data !== null && data !== void 0 ? data : [],\n        error,\n        loading: isLoading\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/tax.ts\n"));

/***/ })

}]);