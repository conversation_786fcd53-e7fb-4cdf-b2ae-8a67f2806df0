"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_ui_auto-suggestion_tsx"],{

/***/ "__barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js ***!
  \******************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Transition: function() { return /* reexport safe */ E_Projects_BB_Projects_e_commerce_logorithm_e_site_shop_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_0__.Transition; }
/* harmony export */ });
/* harmony import */ var E_Projects_BB_Projects_e_commerce_logorithm_e_site_shop_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/transitions/transition.js */ "./node_modules/@headlessui/react/dist/components/transitions/transition.js");



/***/ }),

/***/ "./src/components/ui/auto-suggestion.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/auto-suggestion.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scrollbar */ \"./src/components/ui/scrollbar.tsx\");\n/* harmony import */ var _components_ui_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/image */ \"./src/components/ui/image.tsx\");\n/* harmony import */ var _lib_placeholders__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/placeholders */ \"./src/lib/placeholders.tsx\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst AutoSuggestion = (param)=>{\n    let { className, suggestions, visible, notFound, showLoaders, seeMore, seeMoreLink } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleClick = (path)=>{\n        router.push(path);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_9__.Transition, {\n        show: visible,\n        enter: \"transition-opacity duration-75\",\n        enterFrom: \"opacity-0\",\n        enterTo: \"opacity-100\",\n        leave: \"transition-opacity duration-150\",\n        leaveFrom: \"opacity-100\",\n        leaveTo: \"opacity-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_2___default()(\"absolute top-11 left-0 mt-2 w-full lg:top-16 lg:mt-1\", className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full w-full rounded-lg bg-white py-2 shadow-downfall-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-full w-full\",\n                        children: [\n                            notFound && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"flex h-full w-full items-center justify-center py-10 font-semibold text-gray-400\",\n                                children: t(\"text-no-products\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, undefined),\n                            showLoaders && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-full w-full items-center justify-center py-14\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    simple: true,\n                                    className: \"h-9 w-9\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 15\n                            }, undefined),\n                            !notFound && !showLoaders && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-h-52\",\n                                children: suggestions === null || suggestions === void 0 ? void 0 : suggestions.map((item)=>/*#__PURE__*/ {\n                                    var _item_image;\n                                    var _item_image_original, _item_name;\n                                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>handleClick(_config_routes__WEBPACK_IMPORTED_MODULE_7__.Routes.product(item === null || item === void 0 ? void 0 : item.slug)),\n                                        className: \"flex w-full cursor-pointer items-center border-b border-border-100 px-5 py-2 transition-colors last:border-b-0 hover:bg-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-8 w-8 overflow-hidden rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                                    className: \"h-full w-full\",\n                                                    src: (_item_image_original = item === null || item === void 0 ? void 0 : (_item_image = item.image) === null || _item_image === void 0 ? void 0 : _item_image.original) !== null && _item_image_original !== void 0 ? _item_image_original : _lib_placeholders__WEBPACK_IMPORTED_MODULE_6__.productPlaceholder,\n                                                    alt: (_item_name = item === null || item === void 0 ? void 0 : item.name) !== null && _item_name !== void 0 ? _item_name : \"\",\n                                                    width: 100,\n                                                    height: 100\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-semibold text-heading ltr:ml-3 rtl:mr-3\",\n                                                children: item === null || item === void 0 ? void 0 : item.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, item === null || item === void 0 ? void 0 : item.slug, true, {\n                                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, undefined),\n                    seeMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full py-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: seeMoreLink,\n                            className: \"text-sm font-semibold text-accent transition-colors hover:text-accent-hover\",\n                            children: t(\"text-see-more\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AutoSuggestion, \"4RnRNJiHpB9q7GSIHCO6Xnv5sUA=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = AutoSuggestion;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AutoSuggestion);\nvar _c;\n$RefreshReg$(_c, \"AutoSuggestion\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/auto-suggestion.tsx\n"));

/***/ })

}]);