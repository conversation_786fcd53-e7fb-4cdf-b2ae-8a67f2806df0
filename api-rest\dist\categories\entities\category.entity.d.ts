import { Model } from 'sequelize-typescript';
import { Product } from 'src/products/entities/product.entity';
import { Type } from 'src/types/entities/type.entity';
export declare class Category extends Model {
    id: number;
    name: string;
    slug: string;
    parent_id?: number;
    parent?: Category;
    children?: Category[];
    details?: string;
    image?: any;
    icon?: string;
    type_id?: number;
    type?: Type;
    products?: Product[];
    language: string;
    translated_languages: string[];
    created_at: Date;
    updated_at: Date;
}
export declare class ProductCategory extends Model {
    product_id: number;
    category_id: number;
}
