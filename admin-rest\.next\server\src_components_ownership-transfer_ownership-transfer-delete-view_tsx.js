"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_ownership-transfer_ownership-transfer-delete-view_tsx";
exports.ids = ["src_components_ownership-transfer_ownership-transfer-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/ownership-transfer/ownership-transfer-delete-view.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/ownership-transfer/ownership-transfer-delete-view.tsx ***!
  \******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_ownership_transfer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/ownership-transfer */ \"./src/data/ownership-transfer.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_ownership_transfer__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_ownership_transfer__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst OwnershipTransferDeleteView = ()=>{\n    const { mutate: deleteOwnershipTransfer, isLoading: loading } = (0,_data_ownership_transfer__WEBPACK_IMPORTED_MODULE_3__.useDeleteOwnerTransferMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteOwnershipTransfer({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\ownership-transfer\\\\ownership-transfer-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OwnershipTransferDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ownership-transfer/ownership-transfer-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/ownership-transfer.ts":
/*!***********************************************!*\
  !*** ./src/data/client/ownership-transfer.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ownershipTransferClient: () => (/* binding */ ownershipTransferClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst ownershipTransferClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER),\n    all: ({ transaction_identifier, shop_id, ...params } = {})=>_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params\n        }),\n    get ({ transaction_identifier, language, shop_id, request_view_type }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER}/${transaction_identifier}`, {\n            language,\n            shop_id,\n            transaction_identifier,\n            request_view_type\n        });\n    },\n    paginated: ({ transaction_identifier, shop_id, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                transaction_identifier\n            })\n        });\n    },\n    approve: (variables)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, variables);\n    },\n    disapprove: (variables)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, variables);\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/ownership-transfer.ts\n");

/***/ }),

/***/ "./src/data/ownership-transfer.ts":
/*!****************************************!*\
  !*** ./src/data/ownership-transfer.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApproveOwnerTransferMutation: () => (/* binding */ useApproveOwnerTransferMutation),\n/* harmony export */   useCreateOwnerTransferMutation: () => (/* binding */ useCreateOwnerTransferMutation),\n/* harmony export */   useDeleteOwnerTransferMutation: () => (/* binding */ useDeleteOwnerTransferMutation),\n/* harmony export */   useDisApproveOwnerTransferMutation: () => (/* binding */ useDisApproveOwnerTransferMutation),\n/* harmony export */   useOwnerShipTransferLoadMoreQuery: () => (/* binding */ useOwnerShipTransferLoadMoreQuery),\n/* harmony export */   useOwnerShipTransferQuery: () => (/* binding */ useOwnerShipTransferQuery),\n/* harmony export */   useOwnerShipTransfersQuery: () => (/* binding */ useOwnerShipTransfersQuery),\n/* harmony export */   useUpdateOwnerTransferMutation: () => (/* binding */ useUpdateOwnerTransferMutation)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\n/* harmony import */ var _data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/data/client/ownership-transfer */ \"./src/data/client/ownership-transfer.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _config__WEBPACK_IMPORTED_MODULE_7__, _data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _config__WEBPACK_IMPORTED_MODULE_7__, _data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// Read Single Ownership transfer request\nconst useOwnerShipTransferQuery = ({ transaction_identifier, language, shop_id, request_view_type })=>{\n    const { data, error, isLoading, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER,\n        {\n            transaction_identifier,\n            language,\n            shop_id\n        }\n    ], ()=>_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.get({\n            transaction_identifier,\n            language,\n            shop_id,\n            request_view_type\n        }));\n    return {\n        ownershipTransfer: data,\n        error,\n        loading: isLoading,\n        refetch\n    };\n};\n// Read All Ownership transfer request\nconst useOwnerShipTransfersQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        ownershipTransfer: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n// Read All Ownership transfer request paginated\nconst useOwnerShipTransferLoadMoreQuery = (options, config)=>{\n    const { data, error, isLoading, isFetchingNextPage, hasNextPage, fetchNextPage } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useInfiniteQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.all(Object.assign({}, queryKey[1], pageParam)), {\n        ...config,\n        getNextPageParam: ({ current_page, last_page })=>last_page > current_page && {\n                page: current_page + 1\n            }\n    });\n    function handleLoadMore() {\n        fetchNextPage();\n    }\n    return {\n        ownershipTransfer: data?.pages.flatMap((page)=>page?.data) ?? [],\n        paginatorInfo: Array.isArray(data?.pages) ? data?.pages[data.pages.length - 1] : null,\n        error,\n        hasNextPage,\n        loading: isLoading,\n        isLoadingMore: isFetchingNextPage,\n        loadMore: handleLoadMore\n    };\n};\n// Create Ownership transfer request\nconst useCreateOwnerTransferMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// Update Ownership transfer request\nconst useUpdateOwnerTransferMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list;\n            await router.push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// Delete Ownership transfer request\nconst useDeleteOwnerTransferMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// approve Ownership transfer request\nconst useApproveOwnerTransferMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.approve, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        }\n    });\n};\n// disapprove Ownership transfer request\nconst useDisApproveOwnerTransferMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.disapprove, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        }\n    });\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/ownership-transfer.ts\n");

/***/ })

};
;