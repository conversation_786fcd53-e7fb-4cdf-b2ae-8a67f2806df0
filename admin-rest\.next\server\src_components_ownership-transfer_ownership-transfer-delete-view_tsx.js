"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_ownership-transfer_ownership-transfer-delete-view_tsx";
exports.ids = ["src_components_ownership-transfer_ownership-transfer-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/ownership-transfer/ownership-transfer-delete-view.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/ownership-transfer/ownership-transfer-delete-view.tsx ***!
  \******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_ownership_transfer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/ownership-transfer */ \"./src/data/ownership-transfer.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_ownership_transfer__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_ownership_transfer__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst OwnershipTransferDeleteView = ()=>{\n    const { mutate: deleteOwnershipTransfer, isLoading: loading } = (0,_data_ownership_transfer__WEBPACK_IMPORTED_MODULE_3__.useDeleteOwnerTransferMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteOwnershipTransfer({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\ownership-transfer\\\\ownership-transfer-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OwnershipTransferDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ownership-transfer/ownership-transfer-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/ownership-transfer.ts":
/*!***********************************************!*\
  !*** ./src/data/client/ownership-transfer.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ownershipTransferClient: () => (/* binding */ ownershipTransferClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst ownershipTransferClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER),\n    all: ({ transaction_identifier, shop_id, ...params } = {})=>_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params\n        }),\n    get ({ transaction_identifier, language, shop_id, request_view_type }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER}/${transaction_identifier}`, {\n            language,\n            shop_id,\n            transaction_identifier,\n            request_view_type\n        });\n    },\n    paginated: ({ transaction_identifier, shop_id, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                transaction_identifier\n            })\n        });\n    },\n    approve: (variables)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, variables);\n    },\n    disapprove: (variables)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.OWNERSHIP_TRANSFER, variables);\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/ownership-transfer.ts\n");

/***/ }),

/***/ "./src/data/ownership-transfer.ts":
/*!****************************************!*\
  !*** ./src/data/ownership-transfer.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApproveOwnerTransferMutation: () => (/* binding */ useApproveOwnerTransferMutation),\n/* harmony export */   useCreateOwnerTransferMutation: () => (/* binding */ useCreateOwnerTransferMutation),\n/* harmony export */   useDeleteOwnerTransferMutation: () => (/* binding */ useDeleteOwnerTransferMutation),\n/* harmony export */   useDisApproveOwnerTransferMutation: () => (/* binding */ useDisApproveOwnerTransferMutation),\n/* harmony export */   useOwnerShipTransferLoadMoreQuery: () => (/* binding */ useOwnerShipTransferLoadMoreQuery),\n/* harmony export */   useOwnerShipTransferQuery: () => (/* binding */ useOwnerShipTransferQuery),\n/* harmony export */   useOwnerShipTransfersQuery: () => (/* binding */ useOwnerShipTransfersQuery),\n/* harmony export */   useUpdateOwnerTransferMutation: () => (/* binding */ useUpdateOwnerTransferMutation)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\n/* harmony import */ var _data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/data/client/ownership-transfer */ \"./src/data/client/ownership-transfer.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _config__WEBPACK_IMPORTED_MODULE_7__, _data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _config__WEBPACK_IMPORTED_MODULE_7__, _data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// Read Single Ownership transfer request\nconst useOwnerShipTransferQuery = ({ transaction_identifier, language, shop_id, request_view_type })=>{\n    const { data, error, isLoading, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER,\n        {\n            transaction_identifier,\n            language,\n            shop_id\n        }\n    ], ()=>_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.get({\n            transaction_identifier,\n            language,\n            shop_id,\n            request_view_type\n        }));\n    return {\n        ownershipTransfer: data,\n        error,\n        loading: isLoading,\n        refetch\n    };\n};\n// Read All Ownership transfer request\nconst useOwnerShipTransfersQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        ownershipTransfer: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n// Read All Ownership transfer request paginated\nconst useOwnerShipTransferLoadMoreQuery = (options, config)=>{\n    const { data, error, isLoading, isFetchingNextPage, hasNextPage, fetchNextPage } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useInfiniteQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.all(Object.assign({}, queryKey[1], pageParam)), {\n        ...config,\n        getNextPageParam: ({ current_page, last_page })=>last_page > current_page && {\n                page: current_page + 1\n            }\n    });\n    function handleLoadMore() {\n        fetchNextPage();\n    }\n    return {\n        ownershipTransfer: data?.pages.flatMap((page)=>page?.data) ?? [],\n        paginatorInfo: Array.isArray(data?.pages) ? data?.pages[data.pages.length - 1] : null,\n        error,\n        hasNextPage,\n        loading: isLoading,\n        isLoadingMore: isFetchingNextPage,\n        loadMore: handleLoadMore\n    };\n};\n// Create Ownership transfer request\nconst useCreateOwnerTransferMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// Update Ownership transfer request\nconst useUpdateOwnerTransferMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.ownershipTransferRequest.list;\n            await router.push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// Delete Ownership transfer request\nconst useDeleteOwnerTransferMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// approve Ownership transfer request\nconst useApproveOwnerTransferMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.approve, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        }\n    });\n};\n// disapprove Ownership transfer request\nconst useDisApproveOwnerTransferMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_ownership_transfer__WEBPACK_IMPORTED_MODULE_8__.ownershipTransferClient.disapprove, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.OWNERSHIP_TRANSFER);\n        }\n    });\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/ownership-transfer.ts\n");

/***/ })

};
;