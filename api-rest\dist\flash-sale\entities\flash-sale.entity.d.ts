import { Model } from 'sequelize-typescript';
import { Attachment } from 'src/common/entities/attachment.entity';
import { Product } from 'src/products/entities/product.entity';
export declare class FlashSale extends Model {
    id: number;
    title: string;
    slug: string;
    description: string;
    start_date: string;
    end_date: string;
    image?: Attachment;
    cover_image?: Attachment;
    type: string;
    rate: string;
    sale_status: boolean;
    sale_builder: any;
    language: string;
    translated_languages: string[];
    products: Product[];
    deleted_at?: string;
}
