/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_payment_stripe-element-modal_tsx"],{

/***/ "./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("(function (global, factory) {\n   true ? factory(exports, __webpack_require__(/*! react */ \"./node_modules/react/index.js\")) :\n  0;\n})(this, (function (exports, React) { 'use strict';\n\n  function ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n\n    if (Object.getOwnPropertySymbols) {\n      var symbols = Object.getOwnPropertySymbols(object);\n\n      if (enumerableOnly) {\n        symbols = symbols.filter(function (sym) {\n          return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n      }\n\n      keys.push.apply(keys, symbols);\n    }\n\n    return keys;\n  }\n\n  function _objectSpread2(target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i] != null ? arguments[i] : {};\n\n      if (i % 2) {\n        ownKeys(Object(source), true).forEach(function (key) {\n          _defineProperty(target, key, source[key]);\n        });\n      } else if (Object.getOwnPropertyDescriptors) {\n        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n      } else {\n        ownKeys(Object(source)).forEach(function (key) {\n          Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n      }\n    }\n\n    return target;\n  }\n\n  function _typeof(obj) {\n    \"@babel/helpers - typeof\";\n\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n      _typeof = function (obj) {\n        return typeof obj;\n      };\n    } else {\n      _typeof = function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n      };\n    }\n\n    return _typeof(obj);\n  }\n\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n\n    return obj;\n  }\n\n  function _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n\n    for (i = 0; i < sourceKeys.length; i++) {\n      key = sourceKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n\n    return target;\n  }\n\n  function _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n\n    var key, i;\n\n    if (Object.getOwnPropertySymbols) {\n      var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n      for (i = 0; i < sourceSymbolKeys.length; i++) {\n        key = sourceSymbolKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n        target[key] = source[key];\n      }\n    }\n\n    return target;\n  }\n\n  function _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n  }\n\n  function _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n  }\n\n  function _iterableToArrayLimit(arr, i) {\n    var _i = arr && (typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]);\n\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n\n    var _s, _e;\n\n    try {\n      for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n        _arr.push(_s.value);\n\n        if (i && _arr.length === i) break;\n      }\n    } catch (err) {\n      _d = true;\n      _e = err;\n    } finally {\n      try {\n        if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n\n    return _arr;\n  }\n\n  function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n  }\n\n  function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n    return arr2;\n  }\n\n  function _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n\n  function getDefaultExportFromCjs (x) {\n  \treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n  }\n\n  var propTypes = {exports: {}};\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  var ReactPropTypesSecret_1;\n  var hasRequiredReactPropTypesSecret;\n\n  function requireReactPropTypesSecret() {\n    if (hasRequiredReactPropTypesSecret) return ReactPropTypesSecret_1;\n    hasRequiredReactPropTypesSecret = 1;\n\n    var ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n    ReactPropTypesSecret_1 = ReactPropTypesSecret;\n    return ReactPropTypesSecret_1;\n  }\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  var factoryWithThrowingShims;\n  var hasRequiredFactoryWithThrowingShims;\n\n  function requireFactoryWithThrowingShims() {\n    if (hasRequiredFactoryWithThrowingShims) return factoryWithThrowingShims;\n    hasRequiredFactoryWithThrowingShims = 1;\n\n    var ReactPropTypesSecret = requireReactPropTypesSecret();\n\n    function emptyFunction() {}\n\n    function emptyFunctionWithReset() {}\n\n    emptyFunctionWithReset.resetWarningCache = emptyFunction;\n\n    factoryWithThrowingShims = function () {\n      function shim(props, propName, componentName, location, propFullName, secret) {\n        if (secret === ReactPropTypesSecret) {\n          // It is still safe when called from React.\n          return;\n        }\n\n        var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');\n        err.name = 'Invariant Violation';\n        throw err;\n      }\n      shim.isRequired = shim;\n\n      function getShim() {\n        return shim;\n      }\n      // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n\n      var ReactPropTypes = {\n        array: shim,\n        bool: shim,\n        func: shim,\n        number: shim,\n        object: shim,\n        string: shim,\n        symbol: shim,\n        any: shim,\n        arrayOf: getShim,\n        element: shim,\n        elementType: shim,\n        instanceOf: getShim,\n        node: shim,\n        objectOf: getShim,\n        oneOf: getShim,\n        oneOfType: getShim,\n        shape: getShim,\n        exact: getShim,\n        checkPropTypes: emptyFunctionWithReset,\n        resetWarningCache: emptyFunction\n      };\n      ReactPropTypes.PropTypes = ReactPropTypes;\n      return ReactPropTypes;\n    };\n\n    return factoryWithThrowingShims;\n  }\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  {\n    // By explicitly using `prop-types` you are opting into new production behavior.\n    // http://fb.me/prop-types-in-prod\n    propTypes.exports = requireFactoryWithThrowingShims()();\n  }\n\n  var propTypesExports = propTypes.exports;\n  var PropTypes = /*@__PURE__*/getDefaultExportFromCjs(propTypesExports);\n\n  var useAttachEvent = function useAttachEvent(element, event, cb) {\n    var cbDefined = !!cb;\n    var cbRef = React.useRef(cb); // In many integrations the callback prop changes on each render.\n    // Using a ref saves us from calling element.on/.off every render.\n\n    React.useEffect(function () {\n      cbRef.current = cb;\n    }, [cb]);\n    React.useEffect(function () {\n      if (!cbDefined || !element) {\n        return function () {};\n      }\n\n      var decoratedCb = function decoratedCb() {\n        if (cbRef.current) {\n          cbRef.current.apply(cbRef, arguments);\n        }\n      };\n\n      element.on(event, decoratedCb);\n      return function () {\n        element.off(event, decoratedCb);\n      };\n    }, [cbDefined, event, element, cbRef]);\n  };\n\n  var usePrevious = function usePrevious(value) {\n    var ref = React.useRef(value);\n    React.useEffect(function () {\n      ref.current = value;\n    }, [value]);\n    return ref.current;\n  };\n\n  var isUnknownObject = function isUnknownObject(raw) {\n    return raw !== null && _typeof(raw) === 'object';\n  };\n  var isPromise = function isPromise(raw) {\n    return isUnknownObject(raw) && typeof raw.then === 'function';\n  }; // We are using types to enforce the `stripe` prop in this lib,\n  // but in an untyped integration `stripe` could be anything, so we need\n  // to do some sanity validation to prevent type errors.\n\n  var isStripe = function isStripe(raw) {\n    return isUnknownObject(raw) && typeof raw.elements === 'function' && typeof raw.createToken === 'function' && typeof raw.createPaymentMethod === 'function' && typeof raw.confirmCardPayment === 'function';\n  };\n\n  var PLAIN_OBJECT_STR = '[object Object]';\n  var isEqual = function isEqual(left, right) {\n    if (!isUnknownObject(left) || !isUnknownObject(right)) {\n      return left === right;\n    }\n\n    var leftArray = Array.isArray(left);\n    var rightArray = Array.isArray(right);\n    if (leftArray !== rightArray) return false;\n    var leftPlainObject = Object.prototype.toString.call(left) === PLAIN_OBJECT_STR;\n    var rightPlainObject = Object.prototype.toString.call(right) === PLAIN_OBJECT_STR;\n    if (leftPlainObject !== rightPlainObject) return false; // not sure what sort of special object this is (regexp is one option), so\n    // fallback to reference check.\n\n    if (!leftPlainObject && !leftArray) return left === right;\n    var leftKeys = Object.keys(left);\n    var rightKeys = Object.keys(right);\n    if (leftKeys.length !== rightKeys.length) return false;\n    var keySet = {};\n\n    for (var i = 0; i < leftKeys.length; i += 1) {\n      keySet[leftKeys[i]] = true;\n    }\n\n    for (var _i = 0; _i < rightKeys.length; _i += 1) {\n      keySet[rightKeys[_i]] = true;\n    }\n\n    var allKeys = Object.keys(keySet);\n\n    if (allKeys.length !== leftKeys.length) {\n      return false;\n    }\n\n    var l = left;\n    var r = right;\n\n    var pred = function pred(key) {\n      return isEqual(l[key], r[key]);\n    };\n\n    return allKeys.every(pred);\n  };\n\n  var extractAllowedOptionsUpdates = function extractAllowedOptionsUpdates(options, prevOptions, immutableKeys) {\n    if (!isUnknownObject(options)) {\n      return null;\n    }\n\n    return Object.keys(options).reduce(function (newOptions, key) {\n      var isUpdated = !isUnknownObject(prevOptions) || !isEqual(options[key], prevOptions[key]);\n\n      if (immutableKeys.includes(key)) {\n        if (isUpdated) {\n          console.warn(\"Unsupported prop change: options.\".concat(key, \" is not a mutable property.\"));\n        }\n\n        return newOptions;\n      }\n\n      if (!isUpdated) {\n        return newOptions;\n      }\n\n      return _objectSpread2(_objectSpread2({}, newOptions || {}), {}, _defineProperty({}, key, options[key]));\n    }, null);\n  };\n\n  var INVALID_STRIPE_ERROR$2 = 'Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.'; // We are using types to enforce the `stripe` prop in this lib, but in a real\n  // integration `stripe` could be anything, so we need to do some sanity\n  // validation to prevent type errors.\n\n  var validateStripe = function validateStripe(maybeStripe) {\n    var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n    if (maybeStripe === null || isStripe(maybeStripe)) {\n      return maybeStripe;\n    }\n\n    throw new Error(errorMsg);\n  };\n\n  var parseStripeProp = function parseStripeProp(raw) {\n    var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n    if (isPromise(raw)) {\n      return {\n        tag: 'async',\n        stripePromise: Promise.resolve(raw).then(function (result) {\n          return validateStripe(result, errorMsg);\n        })\n      };\n    }\n\n    var stripe = validateStripe(raw, errorMsg);\n\n    if (stripe === null) {\n      return {\n        tag: 'empty'\n      };\n    }\n\n    return {\n      tag: 'sync',\n      stripe: stripe\n    };\n  };\n\n  var registerWithStripeJs = function registerWithStripeJs(stripe) {\n    if (!stripe || !stripe._registerWrapper || !stripe.registerAppInfo) {\n      return;\n    }\n\n    stripe._registerWrapper({\n      name: 'react-stripe-js',\n      version: \"2.8.0\"\n    });\n\n    stripe.registerAppInfo({\n      name: 'react-stripe-js',\n      version: \"2.8.0\",\n      url: 'https://stripe.com/docs/stripe-js/react'\n    });\n  };\n\n  var ElementsContext = /*#__PURE__*/React.createContext(null);\n  ElementsContext.displayName = 'ElementsContext';\n  var parseElementsContext = function parseElementsContext(ctx, useCase) {\n    if (!ctx) {\n      throw new Error(\"Could not find Elements context; You need to wrap the part of your app that \".concat(useCase, \" in an <Elements> provider.\"));\n    }\n\n    return ctx;\n  };\n  /**\n   * The `Elements` provider allows you to use [Element components](https://stripe.com/docs/stripe-js/react#element-components) and access the [Stripe object](https://stripe.com/docs/js/initializing) in any nested component.\n   * Render an `Elements` provider at the root of your React app so that it is available everywhere you need it.\n   *\n   * To use the `Elements` provider, call `loadStripe` from `@stripe/stripe-js` with your publishable key.\n   * The `loadStripe` function will asynchronously load the Stripe.js script and initialize a `Stripe` object.\n   * Pass the returned `Promise` to `Elements`.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#elements-provider\n   */\n\n  var Elements = function Elements(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp);\n    }, [rawStripeProp]); // For a sync stripe instance, initialize into context\n\n    var _React$useState = React.useState(function () {\n      return {\n        stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n        elements: parsed.tag === 'sync' ? parsed.stripe.elements(options) : null\n      };\n    }),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        ctx = _React$useState2[0],\n        setContext = _React$useState2[1];\n\n    React.useEffect(function () {\n      var isMounted = true;\n\n      var safeSetContext = function safeSetContext(stripe) {\n        setContext(function (ctx) {\n          // no-op if we already have a stripe instance (https://github.com/stripe/react-stripe-js/issues/296)\n          if (ctx.stripe) return ctx;\n          return {\n            stripe: stripe,\n            elements: stripe.elements(options)\n          };\n        });\n      }; // For an async stripePromise, store it in context once resolved\n\n\n      if (parsed.tag === 'async' && !ctx.stripe) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe && isMounted) {\n            // Only update Elements context if the component is still mounted\n            // and stripe is not null. We allow stripe to be null to make\n            // handling SSR easier.\n            safeSetContext(stripe);\n          }\n        });\n      } else if (parsed.tag === 'sync' && !ctx.stripe) {\n        // Or, handle a sync stripe instance going from null -> populated\n        safeSetContext(parsed.stripe);\n      }\n\n      return function () {\n        isMounted = false;\n      };\n    }, [parsed, ctx, options]); // Warn on changes to stripe prop\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      if (!ctx.elements) {\n        return;\n      }\n\n      var updates = extractAllowedOptionsUpdates(options, prevOptions, ['clientSecret', 'fonts']);\n\n      if (updates) {\n        ctx.elements.update(updates);\n      }\n    }, [options, prevOptions, ctx.elements]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(ctx.stripe);\n    }, [ctx.stripe]);\n    return /*#__PURE__*/React.createElement(ElementsContext.Provider, {\n      value: ctx\n    }, children);\n  };\n  Elements.propTypes = {\n    stripe: PropTypes.any,\n    options: PropTypes.object\n  };\n  var useElementsContextWithUseCase = function useElementsContextWithUseCase(useCaseMessage) {\n    var ctx = React.useContext(ElementsContext);\n    return parseElementsContext(ctx, useCaseMessage);\n  };\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#useelements-hook\n   */\n\n  var useElements = function useElements() {\n    var _useElementsContextWi = useElementsContextWithUseCase('calls useElements()'),\n        elements = _useElementsContextWi.elements;\n\n    return elements;\n  };\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#elements-consumer\n   */\n\n  var ElementsConsumer = function ElementsConsumer(_ref2) {\n    var children = _ref2.children;\n    var ctx = useElementsContextWithUseCase('mounts <ElementsConsumer>'); // Assert to satisfy the busted React.FC return type (it should be ReactNode)\n\n    return children(ctx);\n  };\n  ElementsConsumer.propTypes = {\n    children: PropTypes.func.isRequired\n  };\n\n  var _excluded = [\"on\", \"session\"];\n  var CustomCheckoutSdkContext = /*#__PURE__*/React.createContext(null);\n  CustomCheckoutSdkContext.displayName = 'CustomCheckoutSdkContext';\n  var parseCustomCheckoutSdkContext = function parseCustomCheckoutSdkContext(ctx, useCase) {\n    if (!ctx) {\n      throw new Error(\"Could not find CustomCheckoutProvider context; You need to wrap the part of your app that \".concat(useCase, \" in an <CustomCheckoutProvider> provider.\"));\n    }\n\n    return ctx;\n  };\n  var CustomCheckoutContext = /*#__PURE__*/React.createContext(null);\n  CustomCheckoutContext.displayName = 'CustomCheckoutContext';\n  var extractCustomCheckoutContextValue = function extractCustomCheckoutContextValue(customCheckoutSdk, sessionState) {\n    if (!customCheckoutSdk) {\n      return null;\n    }\n\n    customCheckoutSdk.on;\n        customCheckoutSdk.session;\n        var actions = _objectWithoutProperties(customCheckoutSdk, _excluded);\n\n    if (!sessionState) {\n      return _objectSpread2(_objectSpread2({}, actions), customCheckoutSdk.session());\n    }\n\n    return _objectSpread2(_objectSpread2({}, actions), sessionState);\n  };\n  var INVALID_STRIPE_ERROR$1 = 'Invalid prop `stripe` supplied to `CustomCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\n  var CustomCheckoutProvider = function CustomCheckoutProvider(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR$1);\n    }, [rawStripeProp]); // State used to trigger a re-render when sdk.session is updated\n\n    var _React$useState = React.useState(null),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        session = _React$useState2[0],\n        setSession = _React$useState2[1];\n\n    var _React$useState3 = React.useState(function () {\n      return {\n        stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n        customCheckoutSdk: null\n      };\n    }),\n        _React$useState4 = _slicedToArray(_React$useState3, 2),\n        ctx = _React$useState4[0],\n        setContext = _React$useState4[1];\n\n    var safeSetContext = function safeSetContext(stripe, customCheckoutSdk) {\n      setContext(function (ctx) {\n        if (ctx.stripe && ctx.customCheckoutSdk) {\n          return ctx;\n        }\n\n        return {\n          stripe: stripe,\n          customCheckoutSdk: customCheckoutSdk\n        };\n      });\n    }; // Ref used to avoid calling initCustomCheckout multiple times when options changes\n\n\n    var initCustomCheckoutCalledRef = React.useRef(false);\n    React.useEffect(function () {\n      var isMounted = true;\n\n      if (parsed.tag === 'async' && !ctx.stripe) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe && isMounted && !initCustomCheckoutCalledRef.current) {\n            // Only update context if the component is still mounted\n            // and stripe is not null. We allow stripe to be null to make\n            // handling SSR easier.\n            initCustomCheckoutCalledRef.current = true;\n            stripe.initCustomCheckout(options).then(function (customCheckoutSdk) {\n              if (customCheckoutSdk) {\n                safeSetContext(stripe, customCheckoutSdk);\n                customCheckoutSdk.on('change', setSession);\n              }\n            });\n          }\n        });\n      } else if (parsed.tag === 'sync' && parsed.stripe && !initCustomCheckoutCalledRef.current) {\n        initCustomCheckoutCalledRef.current = true;\n        parsed.stripe.initCustomCheckout(options).then(function (customCheckoutSdk) {\n          if (customCheckoutSdk) {\n            safeSetContext(parsed.stripe, customCheckoutSdk);\n            customCheckoutSdk.on('change', setSession);\n          }\n        });\n      }\n\n      return function () {\n        isMounted = false;\n      };\n    }, [parsed, ctx, options, setSession]); // Warn on changes to stripe prop\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on CustomCheckoutProvider: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      var _prevOptions$elements, _options$elementsOpti;\n\n      if (!ctx.customCheckoutSdk) {\n        return;\n      }\n\n      if (options.clientSecret && !isUnknownObject(prevOptions) && !isEqual(options.clientSecret, prevOptions.clientSecret)) {\n        console.warn('Unsupported prop change: options.client_secret is not a mutable property.');\n      }\n\n      var previousAppearance = prevOptions === null || prevOptions === void 0 ? void 0 : (_prevOptions$elements = prevOptions.elementsOptions) === null || _prevOptions$elements === void 0 ? void 0 : _prevOptions$elements.appearance;\n      var currentAppearance = options === null || options === void 0 ? void 0 : (_options$elementsOpti = options.elementsOptions) === null || _options$elementsOpti === void 0 ? void 0 : _options$elementsOpti.appearance;\n\n      if (currentAppearance && !isEqual(currentAppearance, previousAppearance)) {\n        ctx.customCheckoutSdk.changeAppearance(currentAppearance);\n      }\n    }, [options, prevOptions, ctx.customCheckoutSdk]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(ctx.stripe);\n    }, [ctx.stripe]);\n    var customCheckoutContextValue = React.useMemo(function () {\n      return extractCustomCheckoutContextValue(ctx.customCheckoutSdk, session);\n    }, [ctx.customCheckoutSdk, session]);\n\n    if (!ctx.customCheckoutSdk) {\n      return null;\n    }\n\n    return /*#__PURE__*/React.createElement(CustomCheckoutSdkContext.Provider, {\n      value: ctx\n    }, /*#__PURE__*/React.createElement(CustomCheckoutContext.Provider, {\n      value: customCheckoutContextValue\n    }, children));\n  };\n  CustomCheckoutProvider.propTypes = {\n    stripe: PropTypes.any,\n    options: PropTypes.shape({\n      clientSecret: PropTypes.string.isRequired,\n      elementsOptions: PropTypes.object\n    }).isRequired\n  };\n  var useCustomCheckoutSdkContextWithUseCase = function useCustomCheckoutSdkContextWithUseCase(useCaseString) {\n    var ctx = React.useContext(CustomCheckoutSdkContext);\n    return parseCustomCheckoutSdkContext(ctx, useCaseString);\n  };\n  var useElementsOrCustomCheckoutSdkContextWithUseCase = function useElementsOrCustomCheckoutSdkContextWithUseCase(useCaseString) {\n    var customCheckoutSdkContext = React.useContext(CustomCheckoutSdkContext);\n    var elementsContext = React.useContext(ElementsContext);\n\n    if (customCheckoutSdkContext && elementsContext) {\n      throw new Error(\"You cannot wrap the part of your app that \".concat(useCaseString, \" in both <CustomCheckoutProvider> and <Elements> providers.\"));\n    }\n\n    if (customCheckoutSdkContext) {\n      return parseCustomCheckoutSdkContext(customCheckoutSdkContext, useCaseString);\n    }\n\n    return parseElementsContext(elementsContext, useCaseString);\n  };\n  var useCustomCheckout = function useCustomCheckout() {\n    // ensure it's in CustomCheckoutProvider\n    useCustomCheckoutSdkContextWithUseCase('calls useCustomCheckout()');\n    var ctx = React.useContext(CustomCheckoutContext);\n\n    if (!ctx) {\n      throw new Error('Could not find CustomCheckout Context; You need to wrap the part of your app that calls useCustomCheckout() in an <CustomCheckoutProvider> provider.');\n    }\n\n    return ctx;\n  };\n\n  var capitalized = function capitalized(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n  };\n\n  var createElementComponent = function createElementComponent(type, isServer) {\n    var displayName = \"\".concat(capitalized(type), \"Element\");\n\n    var ClientElement = function ClientElement(_ref) {\n      var id = _ref.id,\n          className = _ref.className,\n          _ref$options = _ref.options,\n          options = _ref$options === void 0 ? {} : _ref$options,\n          onBlur = _ref.onBlur,\n          onFocus = _ref.onFocus,\n          onReady = _ref.onReady,\n          onChange = _ref.onChange,\n          onEscape = _ref.onEscape,\n          onClick = _ref.onClick,\n          onLoadError = _ref.onLoadError,\n          onLoaderStart = _ref.onLoaderStart,\n          onNetworksChange = _ref.onNetworksChange,\n          onConfirm = _ref.onConfirm,\n          onCancel = _ref.onCancel,\n          onShippingAddressChange = _ref.onShippingAddressChange,\n          onShippingRateChange = _ref.onShippingRateChange;\n      var ctx = useElementsOrCustomCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n      var elements = 'elements' in ctx ? ctx.elements : null;\n      var customCheckoutSdk = 'customCheckoutSdk' in ctx ? ctx.customCheckoutSdk : null;\n\n      var _React$useState = React.useState(null),\n          _React$useState2 = _slicedToArray(_React$useState, 2),\n          element = _React$useState2[0],\n          setElement = _React$useState2[1];\n\n      var elementRef = React.useRef(null);\n      var domNode = React.useRef(null); // For every event where the merchant provides a callback, call element.on\n      // with that callback. If the merchant ever changes the callback, removes\n      // the old callback with element.off and then call element.on with the new one.\n\n      useAttachEvent(element, 'blur', onBlur);\n      useAttachEvent(element, 'focus', onFocus);\n      useAttachEvent(element, 'escape', onEscape);\n      useAttachEvent(element, 'click', onClick);\n      useAttachEvent(element, 'loaderror', onLoadError);\n      useAttachEvent(element, 'loaderstart', onLoaderStart);\n      useAttachEvent(element, 'networkschange', onNetworksChange);\n      useAttachEvent(element, 'confirm', onConfirm);\n      useAttachEvent(element, 'cancel', onCancel);\n      useAttachEvent(element, 'shippingaddresschange', onShippingAddressChange);\n      useAttachEvent(element, 'shippingratechange', onShippingRateChange);\n      useAttachEvent(element, 'change', onChange);\n      var readyCallback;\n\n      if (onReady) {\n        if (type === 'expressCheckout') {\n          // Passes through the event, which includes visible PM types\n          readyCallback = onReady;\n        } else {\n          // For other Elements, pass through the Element itself.\n          readyCallback = function readyCallback() {\n            onReady(element);\n          };\n        }\n      }\n\n      useAttachEvent(element, 'ready', readyCallback);\n      React.useLayoutEffect(function () {\n        if (elementRef.current === null && domNode.current !== null && (elements || customCheckoutSdk)) {\n          var newElement = null;\n\n          if (customCheckoutSdk) {\n            newElement = customCheckoutSdk.createElement(type, options);\n          } else if (elements) {\n            newElement = elements.create(type, options);\n          } // Store element in a ref to ensure it's _immediately_ available in cleanup hooks in StrictMode\n\n\n          elementRef.current = newElement; // Store element in state to facilitate event listener attachment\n\n          setElement(newElement);\n\n          if (newElement) {\n            newElement.mount(domNode.current);\n          }\n        }\n      }, [elements, customCheckoutSdk, options]);\n      var prevOptions = usePrevious(options);\n      React.useEffect(function () {\n        if (!elementRef.current) {\n          return;\n        }\n\n        var updates = extractAllowedOptionsUpdates(options, prevOptions, ['paymentRequest']);\n\n        if (updates) {\n          elementRef.current.update(updates);\n        }\n      }, [options, prevOptions]);\n      React.useLayoutEffect(function () {\n        return function () {\n          if (elementRef.current && typeof elementRef.current.destroy === 'function') {\n            try {\n              elementRef.current.destroy();\n              elementRef.current = null;\n            } catch (error) {// Do nothing\n            }\n          }\n        };\n      }, []);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        id: id,\n        className: className,\n        ref: domNode\n      });\n    }; // Only render the Element wrapper in a server environment.\n\n\n    var ServerElement = function ServerElement(props) {\n      useElementsOrCustomCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n      var id = props.id,\n          className = props.className;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        id: id,\n        className: className\n      });\n    };\n\n    var Element = isServer ? ServerElement : ClientElement;\n    Element.propTypes = {\n      id: PropTypes.string,\n      className: PropTypes.string,\n      onChange: PropTypes.func,\n      onBlur: PropTypes.func,\n      onFocus: PropTypes.func,\n      onReady: PropTypes.func,\n      onEscape: PropTypes.func,\n      onClick: PropTypes.func,\n      onLoadError: PropTypes.func,\n      onLoaderStart: PropTypes.func,\n      onNetworksChange: PropTypes.func,\n      onConfirm: PropTypes.func,\n      onCancel: PropTypes.func,\n      onShippingAddressChange: PropTypes.func,\n      onShippingRateChange: PropTypes.func,\n      options: PropTypes.object\n    };\n    Element.displayName = displayName;\n    Element.__elementType = type;\n    return Element;\n  };\n\n  var isServer = typeof window === 'undefined';\n\n  var EmbeddedCheckoutContext = /*#__PURE__*/React.createContext(null);\n  EmbeddedCheckoutContext.displayName = 'EmbeddedCheckoutProviderContext';\n  var useEmbeddedCheckoutContext = function useEmbeddedCheckoutContext() {\n    var ctx = React.useContext(EmbeddedCheckoutContext);\n\n    if (!ctx) {\n      throw new Error('<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>');\n    }\n\n    return ctx;\n  };\n  var INVALID_STRIPE_ERROR = 'Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\n  var EmbeddedCheckoutProvider = function EmbeddedCheckoutProvider(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR);\n    }, [rawStripeProp]);\n    var embeddedCheckoutPromise = React.useRef(null);\n    var loadedStripe = React.useRef(null);\n\n    var _React$useState = React.useState({\n      embeddedCheckout: null\n    }),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        ctx = _React$useState2[0],\n        setContext = _React$useState2[1];\n\n    React.useEffect(function () {\n      // Don't support any ctx updates once embeddedCheckout or stripe is set.\n      if (loadedStripe.current || embeddedCheckoutPromise.current) {\n        return;\n      }\n\n      var setStripeAndInitEmbeddedCheckout = function setStripeAndInitEmbeddedCheckout(stripe) {\n        if (loadedStripe.current || embeddedCheckoutPromise.current) return;\n        loadedStripe.current = stripe;\n        embeddedCheckoutPromise.current = loadedStripe.current.initEmbeddedCheckout(options).then(function (embeddedCheckout) {\n          setContext({\n            embeddedCheckout: embeddedCheckout\n          });\n        });\n      }; // For an async stripePromise, store it once resolved\n\n\n      if (parsed.tag === 'async' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe) {\n            setStripeAndInitEmbeddedCheckout(stripe);\n          }\n        });\n      } else if (parsed.tag === 'sync' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n        // Or, handle a sync stripe instance going from null -> populated\n        setStripeAndInitEmbeddedCheckout(parsed.stripe);\n      }\n    }, [parsed, options, ctx, loadedStripe]);\n    React.useEffect(function () {\n      // cleanup on unmount\n      return function () {\n        // If embedded checkout is fully initialized, destroy it.\n        if (ctx.embeddedCheckout) {\n          embeddedCheckoutPromise.current = null;\n          ctx.embeddedCheckout.destroy();\n        } else if (embeddedCheckoutPromise.current) {\n          // If embedded checkout is still initializing, destroy it once\n          // it's done. This could be caused by unmounting very quickly\n          // after mounting.\n          embeddedCheckoutPromise.current.then(function () {\n            embeddedCheckoutPromise.current = null;\n\n            if (ctx.embeddedCheckout) {\n              ctx.embeddedCheckout.destroy();\n            }\n          });\n        }\n      };\n    }, [ctx.embeddedCheckout]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(loadedStripe);\n    }, [loadedStripe]); // Warn on changes to stripe prop.\n    // The stripe prop value can only go from null to non-null once and\n    // can't be changed after that.\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Warn on changes to options.\n\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      if (prevOptions == null) {\n        return;\n      }\n\n      if (options == null) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.');\n        return;\n      }\n\n      if (options.clientSecret === undefined && options.fetchClientSecret === undefined) {\n        console.warn('Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`.');\n      }\n\n      if (prevOptions.clientSecret != null && options.clientSecret !== prevOptions.clientSecret) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n      }\n\n      if (prevOptions.fetchClientSecret != null && options.fetchClientSecret !== prevOptions.fetchClientSecret) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n      }\n\n      if (prevOptions.onComplete != null && options.onComplete !== prevOptions.onComplete) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it.');\n      }\n\n      if (prevOptions.onShippingDetailsChange != null && options.onShippingDetailsChange !== prevOptions.onShippingDetailsChange) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it.');\n      }\n    }, [prevOptions, options]);\n    return /*#__PURE__*/React.createElement(EmbeddedCheckoutContext.Provider, {\n      value: ctx\n    }, children);\n  };\n\n  var EmbeddedCheckoutClientElement = function EmbeddedCheckoutClientElement(_ref) {\n    var id = _ref.id,\n        className = _ref.className;\n\n    var _useEmbeddedCheckoutC = useEmbeddedCheckoutContext(),\n        embeddedCheckout = _useEmbeddedCheckoutC.embeddedCheckout;\n\n    var isMounted = React.useRef(false);\n    var domNode = React.useRef(null);\n    React.useLayoutEffect(function () {\n      if (!isMounted.current && embeddedCheckout && domNode.current !== null) {\n        embeddedCheckout.mount(domNode.current);\n        isMounted.current = true;\n      } // Clean up on unmount\n\n\n      return function () {\n        if (isMounted.current && embeddedCheckout) {\n          try {\n            embeddedCheckout.unmount();\n            isMounted.current = false;\n          } catch (e) {// Do nothing.\n            // Parent effects are destroyed before child effects, so\n            // in cases where both the EmbeddedCheckoutProvider and\n            // the EmbeddedCheckout component are removed at the same\n            // time, the embeddedCheckout instance will be destroyed,\n            // which causes an error when calling unmount.\n          }\n        }\n      };\n    }, [embeddedCheckout]);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: domNode,\n      id: id,\n      className: className\n    });\n  }; // Only render the wrapper in a server environment.\n\n\n  var EmbeddedCheckoutServerElement = function EmbeddedCheckoutServerElement(_ref2) {\n    var id = _ref2.id,\n        className = _ref2.className;\n    // Validate that we are in the right context by calling useEmbeddedCheckoutContext.\n    useEmbeddedCheckoutContext();\n    return /*#__PURE__*/React.createElement(\"div\", {\n      id: id,\n      className: className\n    });\n  };\n\n  var EmbeddedCheckout = isServer ? EmbeddedCheckoutServerElement : EmbeddedCheckoutClientElement;\n\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#usestripe-hook\n   */\n\n  var useStripe = function useStripe() {\n    var _useElementsOrCustomC = useElementsOrCustomCheckoutSdkContextWithUseCase('calls useStripe()'),\n        stripe = _useElementsOrCustomC.stripe;\n\n    return stripe;\n  };\n\n  /**\n   * Requires beta access:\n   * Contact [Stripe support](https://support.stripe.com/) for more information.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AuBankAccountElement = createElementComponent('auBankAccount', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardElement = createElementComponent('card', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardNumberElement = createElementComponent('cardNumber', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardExpiryElement = createElementComponent('cardExpiry', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardCvcElement = createElementComponent('cardCvc', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var FpxBankElement = createElementComponent('fpxBank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var IbanElement = createElementComponent('iban', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var IdealBankElement = createElementComponent('idealBank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var P24BankElement = createElementComponent('p24Bank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var EpsBankElement = createElementComponent('epsBank', isServer);\n  var PaymentElement = createElementComponent('payment', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var ExpressCheckoutElement = createElementComponent('expressCheckout', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var PaymentRequestButtonElement = createElementComponent('paymentRequestButton', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var LinkAuthenticationElement = createElementComponent('linkAuthentication', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AddressElement = createElementComponent('address', isServer);\n  /**\n   * @deprecated\n   * Use `AddressElement` instead.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var ShippingAddressElement = createElementComponent('shippingAddress', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var PaymentMethodMessagingElement = createElementComponent('paymentMethodMessaging', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AffirmMessageElement = createElementComponent('affirmMessage', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AfterpayClearpayMessageElement = createElementComponent('afterpayClearpayMessage', isServer);\n\n  exports.AddressElement = AddressElement;\n  exports.AffirmMessageElement = AffirmMessageElement;\n  exports.AfterpayClearpayMessageElement = AfterpayClearpayMessageElement;\n  exports.AuBankAccountElement = AuBankAccountElement;\n  exports.CardCvcElement = CardCvcElement;\n  exports.CardElement = CardElement;\n  exports.CardExpiryElement = CardExpiryElement;\n  exports.CardNumberElement = CardNumberElement;\n  exports.CustomCheckoutProvider = CustomCheckoutProvider;\n  exports.Elements = Elements;\n  exports.ElementsConsumer = ElementsConsumer;\n  exports.EmbeddedCheckout = EmbeddedCheckout;\n  exports.EmbeddedCheckoutProvider = EmbeddedCheckoutProvider;\n  exports.EpsBankElement = EpsBankElement;\n  exports.ExpressCheckoutElement = ExpressCheckoutElement;\n  exports.FpxBankElement = FpxBankElement;\n  exports.IbanElement = IbanElement;\n  exports.IdealBankElement = IdealBankElement;\n  exports.LinkAuthenticationElement = LinkAuthenticationElement;\n  exports.P24BankElement = P24BankElement;\n  exports.PaymentElement = PaymentElement;\n  exports.PaymentMethodMessagingElement = PaymentMethodMessagingElement;\n  exports.PaymentRequestButtonElement = PaymentRequestButtonElement;\n  exports.ShippingAddressElement = ShippingAddressElement;\n  exports.useCustomCheckout = useCustomCheckout;\n  exports.useElements = useElements;\n  exports.useStripe = useStripe;\n\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js\n"));

/***/ }),

/***/ "./node_modules/@stripe/stripe-js/dist/stripe.esm.js":
/*!***********************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/dist/stripe.esm.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadStripe: function() { return /* binding */ loadStripe; }\n/* harmony export */ });\nvar V3_URL = 'https://js.stripe.com/v3';\nvar V3_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/v3\\/?(\\?.*)?$/;\nvar EXISTING_SCRIPT_MESSAGE = 'loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used';\nvar findScript = function findScript() {\n  var scripts = document.querySelectorAll(\"script[src^=\\\"\".concat(V3_URL, \"\\\"]\"));\n\n  for (var i = 0; i < scripts.length; i++) {\n    var script = scripts[i];\n\n    if (!V3_URL_REGEX.test(script.src)) {\n      continue;\n    }\n\n    return script;\n  }\n\n  return null;\n};\n\nvar injectScript = function injectScript(params) {\n  var queryString = params && !params.advancedFraudSignals ? '?advancedFraudSignals=false' : '';\n  var script = document.createElement('script');\n  script.src = \"\".concat(V3_URL).concat(queryString);\n  var headOrBody = document.head || document.body;\n\n  if (!headOrBody) {\n    throw new Error('Expected document.body not to be null. Stripe.js requires a <body> element.');\n  }\n\n  headOrBody.appendChild(script);\n  return script;\n};\n\nvar registerWrapper = function registerWrapper(stripe, startTime) {\n  if (!stripe || !stripe._registerWrapper) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'stripe-js',\n    version: \"2.1.11\",\n    startTime: startTime\n  });\n};\n\nvar stripePromise = null;\nvar loadScript = function loadScript(params) {\n  // Ensure that we only attempt to load Stripe.js at most once\n  if (stripePromise !== null) {\n    return stripePromise;\n  }\n\n  stripePromise = new Promise(function (resolve, reject) {\n    if (typeof window === 'undefined' || typeof document === 'undefined') {\n      // Resolve to null when imported server side. This makes the module\n      // safe to import in an isomorphic code base.\n      resolve(null);\n      return;\n    }\n\n    if (window.Stripe && params) {\n      console.warn(EXISTING_SCRIPT_MESSAGE);\n    }\n\n    if (window.Stripe) {\n      resolve(window.Stripe);\n      return;\n    }\n\n    try {\n      var script = findScript();\n\n      if (script && params) {\n        console.warn(EXISTING_SCRIPT_MESSAGE);\n      } else if (!script) {\n        script = injectScript(params);\n      }\n\n      script.addEventListener('load', function () {\n        if (window.Stripe) {\n          resolve(window.Stripe);\n        } else {\n          reject(new Error('Stripe.js not available'));\n        }\n      });\n      script.addEventListener('error', function () {\n        reject(new Error('Failed to load Stripe.js'));\n      });\n    } catch (error) {\n      reject(error);\n      return;\n    }\n  });\n  return stripePromise;\n};\nvar initStripe = function initStripe(maybeStripe, args, startTime) {\n  if (maybeStripe === null) {\n    return null;\n  }\n\n  var stripe = maybeStripe.apply(undefined, args);\n  registerWrapper(stripe, startTime);\n  return stripe;\n}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n\n// own script injection.\n\nvar stripePromise$1 = Promise.resolve().then(function () {\n  return loadScript(null);\n});\nvar loadCalled = false;\nstripePromise$1[\"catch\"](function (err) {\n  if (!loadCalled) {\n    console.warn(err);\n  }\n});\nvar loadStripe = function loadStripe() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  loadCalled = true;\n  var startTime = Date.now();\n  return stripePromise$1.then(function (maybeStripe) {\n    return initStripe(maybeStripe, args, startTime);\n  });\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@stripe/stripe-js/dist/stripe.esm.js\n"));

/***/ }),

/***/ "./src/components/payment/stripe-element-modal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/payment/stripe-element-modal.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _stripe_stripe_element_form__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stripe/stripe-element-form */ \"./src/components/payment/stripe/stripe-element-form.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst PAYMENTS_FORM_COMPONENTS = {\n    STRIPE: {\n        component: _stripe_stripe_element_form__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    }\n};\nconst StripeElementModal = ()=>{\n    _s();\n    const { data: { paymentGateway, paymentIntentInfo, trackingNumber } } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_1__.useModalState)();\n    const PaymentMethod = PAYMENTS_FORM_COMPONENTS[paymentGateway === null || paymentGateway === void 0 ? void 0 : paymentGateway.toUpperCase()];\n    const PaymentComponent = PaymentMethod === null || PaymentMethod === void 0 ? void 0 : PaymentMethod.component;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"payment-modal relative h-full w-full overflow-hidden rounded-[10px] bg-light md:h-auto md:min-h-0 md:max-w-2xl lg:w-screen lg:max-w-[46rem]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PaymentComponent, {\n            paymentIntentInfo: paymentIntentInfo,\n            trackingNumber: trackingNumber,\n            paymentGateway: paymentGateway\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe-element-modal.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe-element-modal.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StripeElementModal, \"/tHZvIjJWaKjSFmH4+sH8ADRh04=\", false, function() {\n    return [\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_1__.useModalState\n    ];\n});\n_c = StripeElementModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StripeElementModal);\nvar _c;\n$RefreshReg$(_c, \"StripeElementModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9wYXltZW50L3N0cmlwZS1lbGVtZW50LW1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRTZDO0FBQ2dCO0FBRzdELE1BQU1FLDJCQUFnQztJQUNwQ0MsUUFBUTtRQUNOQyxXQUFXSCxtRUFBaUJBO0lBQzlCO0FBQ0Y7QUFFQSxNQUFNSSxxQkFBcUI7O0lBQ3pCLE1BQU0sRUFDSkMsTUFBTSxFQUFFQyxjQUFjLEVBQUVDLGlCQUFpQixFQUFFQyxjQUFjLEVBQUUsRUFDNUQsR0FBR1QsaUZBQWFBO0lBQ2pCLE1BQU1VLGdCQUFnQlIsd0JBQXdCLENBQUNLLDJCQUFBQSxxQ0FBQUEsZUFBZ0JJLFdBQVcsR0FBRztJQUM3RSxNQUFNQyxtQkFBbUJGLDBCQUFBQSxvQ0FBQUEsY0FBZU4sU0FBUztJQUVqRCxxQkFDRSw4REFBQ1M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Y7WUFDQ0osbUJBQW1CQTtZQUNuQkMsZ0JBQWdCQTtZQUNoQkYsZ0JBQWdCQTs7Ozs7Ozs7Ozs7QUFJeEI7R0FoQk1GOztRQUdBTCw2RUFBYUE7OztLQUhiSztBQWtCTiwrREFBZUEsa0JBQWtCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3BheW1lbnQvc3RyaXBlLWVsZW1lbnQtbW9kYWwudHN4PzY2MjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgdXNlTW9kYWxTdGF0ZSxcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL21vZGFsL21vZGFsLmNvbnRleHQnO1xuaW1wb3J0IFN0cmlwZUVsZW1lbnRGb3JtIGZyb20gJy4vc3RyaXBlL3N0cmlwZS1lbGVtZW50LWZvcm0nO1xuXG5cbmNvbnN0IFBBWU1FTlRTX0ZPUk1fQ09NUE9ORU5UUzogYW55ID0ge1xuICBTVFJJUEU6IHtcbiAgICBjb21wb25lbnQ6IFN0cmlwZUVsZW1lbnRGb3JtLFxuICB9LFxufTtcblxuY29uc3QgU3RyaXBlRWxlbWVudE1vZGFsID0gKCkgPT4ge1xuICBjb25zdCB7XG4gICAgZGF0YTogeyBwYXltZW50R2F0ZXdheSwgcGF5bWVudEludGVudEluZm8sIHRyYWNraW5nTnVtYmVyIH0sXG4gIH0gPSB1c2VNb2RhbFN0YXRlKCk7XG4gIGNvbnN0IFBheW1lbnRNZXRob2QgPSBQQVlNRU5UU19GT1JNX0NPTVBPTkVOVFNbcGF5bWVudEdhdGV3YXk/LnRvVXBwZXJDYXNlKCldO1xuICBjb25zdCBQYXltZW50Q29tcG9uZW50ID0gUGF5bWVudE1ldGhvZD8uY29tcG9uZW50O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJwYXltZW50LW1vZGFsIHJlbGF0aXZlIGgtZnVsbCB3LWZ1bGwgb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtWzEwcHhdIGJnLWxpZ2h0IG1kOmgtYXV0byBtZDptaW4taC0wIG1kOm1heC13LTJ4bCBsZzp3LXNjcmVlbiBsZzptYXgtdy1bNDZyZW1dXCI+XG4gICAgICA8UGF5bWVudENvbXBvbmVudFxuICAgICAgICBwYXltZW50SW50ZW50SW5mbz17cGF5bWVudEludGVudEluZm99XG4gICAgICAgIHRyYWNraW5nTnVtYmVyPXt0cmFja2luZ051bWJlcn1cbiAgICAgICAgcGF5bWVudEdhdGV3YXk9e3BheW1lbnRHYXRld2F5fVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFN0cmlwZUVsZW1lbnRNb2RhbDtcbiJdLCJuYW1lcyI6WyJ1c2VNb2RhbFN0YXRlIiwiU3RyaXBlRWxlbWVudEZvcm0iLCJQQVlNRU5UU19GT1JNX0NPTVBPTkVOVFMiLCJTVFJJUEUiLCJjb21wb25lbnQiLCJTdHJpcGVFbGVtZW50TW9kYWwiLCJkYXRhIiwicGF5bWVudEdhdGV3YXkiLCJwYXltZW50SW50ZW50SW5mbyIsInRyYWNraW5nTnVtYmVyIiwiUGF5bWVudE1ldGhvZCIsInRvVXBwZXJDYXNlIiwiUGF5bWVudENvbXBvbmVudCIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/payment/stripe-element-modal.tsx\n"));

/***/ }),

/***/ "./src/components/payment/stripe/stripe-element-base-form.tsx":
/*!********************************************************************!*\
  !*** ./src/components/payment/stripe/stripe-element-base-form.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StripeElementBaseForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @stripe/react-stripe-js */ \"./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js\");\n/* harmony import */ var _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _framework_order__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/framework/order */ \"./src/framework/rest/order.ts\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction StripeElementBaseForm(param) {\n    let { paymentIntentInfo, trackingNumber, paymentGateway } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"common\");\n    const stripe = (0,_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_3__.useStripe)();\n    const elements = (0,_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_3__.useElements)();\n    const { openModal, closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__.useModalAction)();\n    const { createOrderPayment } = (0,_framework_order__WEBPACK_IMPORTED_MODULE_6__.useOrderPayment)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!stripe) {\n            return;\n        }\n        const clientSecret = new URLSearchParams(window.location.search).get(\"payment_intent_client_secret\");\n        if (!clientSecret) {\n            return;\n        }\n        stripe.retrievePaymentIntent(clientSecret).then((param)=>{\n            let { paymentIntent } = param;\n            switch(paymentIntent === null || paymentIntent === void 0 ? void 0 : paymentIntent.status){\n                case \"succeeded\":\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.success(t(\"payment-successful\"));\n                    setMessage(\"Your payment is Successful.\");\n                    closeModal();\n                    break;\n                case \"processing\":\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.success(t(\"payment-processing\"));\n                    setMessage(\"Your payment is processing.\");\n                    break;\n                case \"requires_payment_method\":\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.success(t(\"payment-not-successful\"));\n                    setMessage(\"Your payment was not successful, please try again.\");\n                    break;\n                default:\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.success(t(\"something-wrong\"));\n                    setMessage(\"Something went wrong.\");\n                    break;\n            }\n        });\n    }, [\n        stripe\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!stripe || !elements) {\n            // Stripe.js hasn't yet loaded.\n            // Make sure to disable form submission until Stripe.js has loaded.\n            return;\n        }\n        setIsLoading(true);\n        const { error, paymentIntent } = await stripe.confirmPayment({\n            elements,\n            confirmParams: {\n                return_url: \"\".concat(window.location.origin).concat(_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.orders, \"/\").concat(trackingNumber)\n            },\n            redirect: \"if_required\"\n        });\n        // Send card response to the api\n        await createOrderPayment({\n            tracking_number: trackingNumber,\n            payment_gateway: \"stripe\"\n        });\n        // This point will only be reached if there is an immediate error when\n        // confirming the payment. Otherwise, your customer will be redirected to\n        // your `return_url`. For some payment methods like iDEAL, your customer will\n        // be redirected to an intermediate site first to authorize the payment, then\n        // redirected to the `return_url`.\n        if ((error === null || error === void 0 ? void 0 : error.type) === \"card_error\" || (error === null || error === void 0 ? void 0 : error.type) === \"validation_error\") {\n            setMessage(error === null || error === void 0 ? void 0 : error.message);\n        } else if (paymentIntent && paymentIntent.status === \"succeeded\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.success(t(\"payment-successful\"));\n            setMessage(\"Your payment is Successful.\");\n            closeModal();\n        } else if (paymentIntent && paymentIntent.status === \"processing\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.success(t(\"payment-processing\"));\n            setMessage(\"Your payment is Pending.\");\n            closeModal();\n        } else {\n            setMessage(\"An unexpected error occurred.\");\n        }\n        setIsLoading(false);\n    };\n    const paymentElementOptions = {\n        layout: \"tabs\",\n        defaultCollapsed: false\n    };\n    const backModal = ()=>{\n        openModal(\"USE_NEW_PAYMENT\", {\n            paymentGateway,\n            paymentIntentInfo,\n            trackingNumber\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"stripe-payment-modal relative h-full w-screen max-w-md overflow-hidden rounded-[10px] bg-light md:h-auto md:min-h-0 lg:max-w-[46rem]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 lg:p-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                id: \"payment-form\",\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_3__.PaymentElement, {\n                        id: \"payment-element\",\n                        options: paymentElementOptions\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-base-form.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 space-x-4 lg:mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                id: \"submit\",\n                                disabled: isLoading || !stripe || !elements,\n                                type: \"submit\",\n                                className: \"StripePay px-11 text-sm shadow-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    id: \"button-text\",\n                                    children: t(\"text-pay\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-base-form.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-base-form.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                type: \"submit\",\n                                variant: \"outline\",\n                                disabled: !!isLoading,\n                                className: \"px-11 text-sm shadow-none\",\n                                onClick: closeModal,\n                                children: t(\"pay-latter\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-base-form.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                disabled: !!isLoading,\n                                variant: \"outline\",\n                                className: \"cursor-pointer\",\n                                onClick: backModal,\n                                children: t(\"text-back\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-base-form.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-base-form.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this),\n                    message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"payment-message\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-base-form.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 23\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-base-form.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-base-form.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-base-form.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(StripeElementBaseForm, \"iEacIGz0hGn8QyOYVKOgBJuUaVA=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation,\n        _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_3__.useStripe,\n        _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_3__.useElements,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__.useModalAction,\n        _framework_order__WEBPACK_IMPORTED_MODULE_6__.useOrderPayment\n    ];\n});\n_c = StripeElementBaseForm;\nvar _c;\n$RefreshReg$(_c, \"StripeElementBaseForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/payment/stripe/stripe-element-base-form.tsx\n"));

/***/ }),

/***/ "./src/components/payment/stripe/stripe-element-form.tsx":
/*!***************************************************************!*\
  !*** ./src/components/payment/stripe/stripe-element-form.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @stripe/react-stripe-js */ \"./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js\");\n/* harmony import */ var _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_get_stripejs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/get-stripejs */ \"./src/lib/get-stripejs.ts\");\n/* harmony import */ var _components_payment_stripe_stripe_element_base_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/payment/stripe/stripe-element-base-form */ \"./src/components/payment/stripe/stripe-element-base-form.tsx\");\n\n\n\n\nconst StripeElementForm = (param)=>{\n    let { paymentGateway, paymentIntentInfo, trackingNumber } = param;\n    // let onlyCard = false; // eita ashbe settings theke\n    const clientSecret = paymentIntentInfo === null || paymentIntentInfo === void 0 ? void 0 : paymentIntentInfo.client_secret;\n    const options = {\n        clientSecret,\n        appearance: {\n            theme: \"stripe\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: clientSecret && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_1__.Elements, {\n            options: options,\n            stripe: (0,_lib_get_stripejs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_payment_stripe_stripe_element_base_form__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                paymentIntentInfo: paymentIntentInfo,\n                trackingNumber: trackingNumber,\n                paymentGateway: paymentGateway\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-form.tsx\",\n                lineNumber: 34,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-element-form.tsx\",\n            lineNumber: 33,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_c = StripeElementForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StripeElementForm);\nvar _c;\n$RefreshReg$(_c, \"StripeElementForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/payment/stripe/stripe-element-form.tsx\n"));

/***/ }),

/***/ "./src/framework/rest/order.ts":
/*!*************************************!*\
  !*** ./src/framework/rest/order.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateOrder: function() { return /* binding */ useCreateOrder; },\n/* harmony export */   useCreateRefund: function() { return /* binding */ useCreateRefund; },\n/* harmony export */   useDownloadableProducts: function() { return /* binding */ useDownloadableProducts; },\n/* harmony export */   useGenerateDownloadableUrl: function() { return /* binding */ useGenerateDownloadableUrl; },\n/* harmony export */   useGetPaymentIntent: function() { return /* binding */ useGetPaymentIntent; },\n/* harmony export */   useGetPaymentIntentOriginal: function() { return /* binding */ useGetPaymentIntentOriginal; },\n/* harmony export */   useOrder: function() { return /* binding */ useOrder; },\n/* harmony export */   useOrderPayment: function() { return /* binding */ useOrderPayment; },\n/* harmony export */   useOrders: function() { return /* binding */ useOrders; },\n/* harmony export */   useRefunds: function() { return /* binding */ useRefunds; },\n/* harmony export */   useSavePaymentMethod: function() { return /* binding */ useSavePaymentMethod; },\n/* harmony export */   useVerifyOrder: function() { return /* binding */ useVerifyOrder; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"./src/types/index.ts\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/framework/rest/client/api-endpoints.ts\");\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client */ \"./src/framework/rest/client/index.ts\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! jotai */ \"./node_modules/jotai/esm/index.mjs\");\n/* harmony import */ var _store_checkout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/store/checkout */ \"./src/store/checkout.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _framework_utils_data_mappers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/framework/utils/data-mappers */ \"./src/framework/rest/utils/data-mappers.ts\");\n/* harmony import */ var lodash_isArray__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! lodash/isArray */ \"./node_modules/lodash/isArray.js\");\n/* harmony import */ var lodash_isArray__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(lodash_isArray__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var lodash_isObject__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lodash/isObject */ \"./node_modules/lodash/isObject.js\");\n/* harmony import */ var lodash_isObject__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(lodash_isObject__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lodash/isEmpty */ \"./node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_13__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction useOrders(options) {\n    var _data_pages;\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const formattedOptions = {\n        ...options\n    };\n    const { data, isLoading, error, fetchNextPage, hasNextPage, isFetching, isFetchingNextPage } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useInfiniteQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ORDERS,\n        formattedOptions\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.all(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        getNextPageParam: (param)=>{\n            let { current_page, last_page } = param;\n            return last_page > current_page && {\n                page: current_page + 1\n            };\n        },\n        refetchOnWindowFocus: false\n    });\n    function handleLoadMore() {\n        fetchNextPage();\n    }\n    var _data_pages_flatMap;\n    return {\n        orders: (_data_pages_flatMap = data === null || data === void 0 ? void 0 : (_data_pages = data.pages) === null || _data_pages === void 0 ? void 0 : _data_pages.flatMap((page)=>page.data)) !== null && _data_pages_flatMap !== void 0 ? _data_pages_flatMap : [],\n        paginatorInfo: Array.isArray(data === null || data === void 0 ? void 0 : data.pages) ? (0,_framework_utils_data_mappers__WEBPACK_IMPORTED_MODULE_10__.mapPaginatorData)(data === null || data === void 0 ? void 0 : data.pages[data.pages.length - 1]) : null,\n        isLoading,\n        error,\n        isFetching,\n        isLoadingMore: isFetchingNextPage,\n        loadMore: handleLoadMore,\n        hasMore: Boolean(hasNextPage)\n    };\n}\nfunction useOrder(param) {\n    let { tracking_number } = param;\n    const { data, isLoading, error, isFetching, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ORDERS,\n        tracking_number\n    ], ()=>_client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.get(tracking_number), {\n        refetchOnWindowFocus: false\n    });\n    return {\n        order: data,\n        isFetching,\n        isLoading,\n        refetch,\n        error\n    };\n}\nfunction useRefunds(options) {\n    var _data_pages;\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const formattedOptions = {\n        ...options\n    };\n    const { data, isLoading, isFetchingNextPage, fetchNextPage, hasNextPage, error } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useInfiniteQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ORDERS_REFUNDS,\n        formattedOptions\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.refunds(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        getNextPageParam: (param)=>{\n            let { current_page, last_page } = param;\n            return last_page > current_page && {\n                page: current_page + 1\n            };\n        }\n    });\n    function handleLoadMore() {\n        fetchNextPage();\n    }\n    var _data_pages_flatMap;\n    return {\n        refunds: (_data_pages_flatMap = data === null || data === void 0 ? void 0 : (_data_pages = data.pages) === null || _data_pages === void 0 ? void 0 : _data_pages.flatMap((page)=>page.data)) !== null && _data_pages_flatMap !== void 0 ? _data_pages_flatMap : [],\n        paginatorInfo: Array.isArray(data === null || data === void 0 ? void 0 : data.pages) ? (0,_framework_utils_data_mappers__WEBPACK_IMPORTED_MODULE_10__.mapPaginatorData)(data === null || data === void 0 ? void 0 : data.pages[data.pages.length - 1]) : null,\n        isLoading,\n        isLoadingMore: isFetchingNextPage,\n        error,\n        loadMore: handleLoadMore,\n        hasMore: Boolean(hasNextPage)\n    };\n}\nconst useDownloadableProducts = (options)=>{\n    var _data_pages;\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const formattedOptions = {\n        ...options\n    };\n    const { data, isLoading, isFetching, isFetchingNextPage, fetchNextPage, hasNextPage, error } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useInfiniteQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ORDERS_DOWNLOADS,\n        formattedOptions\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.downloadable(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        getNextPageParam: (param)=>{\n            let { current_page, last_page } = param;\n            return last_page > current_page && {\n                page: current_page + 1\n            };\n        },\n        refetchOnWindowFocus: false\n    });\n    function handleLoadMore() {\n        fetchNextPage();\n    }\n    var _data_pages_flatMap;\n    return {\n        downloads: (_data_pages_flatMap = data === null || data === void 0 ? void 0 : (_data_pages = data.pages) === null || _data_pages === void 0 ? void 0 : _data_pages.flatMap((page)=>page.data)) !== null && _data_pages_flatMap !== void 0 ? _data_pages_flatMap : [],\n        paginatorInfo: Array.isArray(data === null || data === void 0 ? void 0 : data.pages) ? (0,_framework_utils_data_mappers__WEBPACK_IMPORTED_MODULE_10__.mapPaginatorData)(data === null || data === void 0 ? void 0 : data.pages[data.pages.length - 1]) : null,\n        isLoading,\n        isFetching,\n        isLoadingMore: isFetchingNextPage,\n        error,\n        loadMore: handleLoadMore,\n        hasMore: Boolean(hasNextPage)\n    };\n};\nfunction useCreateRefund() {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__.useModalAction)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { mutate: createRefundRequest, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.createRefund, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"\".concat(t(\"text-refund-request-submitted\")));\n        },\n        onError: (error)=>{\n            const { response: { data } } = error !== null && error !== void 0 ? error : {};\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"\".concat(t(data === null || data === void 0 ? void 0 : data.message)));\n        },\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ORDERS);\n            closeModal();\n        }\n    });\n    function formatRefundInput(input) {\n        const formattedInputs = {\n            ...input\n        };\n        createRefundRequest(formattedInputs);\n    }\n    return {\n        createRefundRequest: formatRefundInput,\n        isLoading\n    };\n}\nfunction useCreateOrder() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const { locale } = router;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const { mutate: createOrder, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.create, {\n        onSuccess: (param)=>{\n            let { tracking_number, payment_gateway, payment_intent } = param;\n            console.log(tracking_number, payment_gateway, payment_intent, \"create order\");\n            if (tracking_number) {\n                var _payment_intent_payment_intent_info;\n                if ([\n                    _types__WEBPACK_IMPORTED_MODULE_0__.PaymentGateway.COD,\n                    _types__WEBPACK_IMPORTED_MODULE_0__.PaymentGateway.CASH,\n                    _types__WEBPACK_IMPORTED_MODULE_0__.PaymentGateway.FULL_WALLET_PAYMENT\n                ].includes(payment_gateway)) {\n                    return router.push(_config_routes__WEBPACK_IMPORTED_MODULE_9__.Routes.order(tracking_number));\n                }\n                if (payment_intent === null || payment_intent === void 0 ? void 0 : (_payment_intent_payment_intent_info = payment_intent.payment_intent_info) === null || _payment_intent_payment_intent_info === void 0 ? void 0 : _payment_intent_payment_intent_info.is_redirect) {\n                    var _payment_intent_payment_intent_info1;\n                    return router.push(payment_intent === null || payment_intent === void 0 ? void 0 : (_payment_intent_payment_intent_info1 = payment_intent.payment_intent_info) === null || _payment_intent_payment_intent_info1 === void 0 ? void 0 : _payment_intent_payment_intent_info1.redirect_url);\n                } else {\n                    return router.push(\"\".concat(_config_routes__WEBPACK_IMPORTED_MODULE_9__.Routes.order(tracking_number), \"/payment\"));\n                }\n            }\n        },\n        onError: (error)=>{\n            const { response: { data } } = error !== null && error !== void 0 ? error : {};\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(data === null || data === void 0 ? void 0 : data.message);\n        }\n    });\n    function formatOrderInput(input) {\n        const formattedInputs = {\n            ...input,\n            language: locale,\n            invoice_translated_text: {\n                subtotal: t(\"order-sub-total\"),\n                discount: t(\"order-discount\"),\n                tax: t(\"order-tax\"),\n                delivery_fee: t(\"order-delivery-fee\"),\n                total: t(\"order-total\"),\n                products: t(\"text-products\"),\n                quantity: t(\"text-quantity\"),\n                invoice_no: t(\"text-invoice-no\"),\n                date: t(\"text-date\")\n            }\n        };\n        createOrder(formattedInputs);\n    }\n    return {\n        createOrder: formatOrderInput,\n        isLoading\n    };\n}\nfunction useGenerateDownloadableUrl() {\n    const { mutate: getDownloadableUrl } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.generateDownloadLink, {\n        onSuccess: (data)=>{\n            function download(fileUrl, fileName) {\n                var a = document.createElement(\"a\");\n                a.href = fileUrl;\n                a.setAttribute(\"download\", fileName);\n                a.click();\n            }\n            download(data, \"record.name\");\n        }\n    });\n    function generateDownloadableUrl(digital_file_id) {\n        getDownloadableUrl({\n            digital_file_id\n        });\n    }\n    return {\n        generateDownloadableUrl\n    };\n}\nfunction useVerifyOrder() {\n    const [_, setVerifiedResponse] = (0,jotai__WEBPACK_IMPORTED_MODULE_14__.useAtom)(_store_checkout__WEBPACK_IMPORTED_MODULE_7__.verifiedResponseAtom);\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.verify, {\n        onSuccess: (data)=>{\n            //@ts-ignore\n            if (data === null || data === void 0 ? void 0 : data.errors) {\n                var _data_errors_;\n                //@ts-ignore\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(data === null || data === void 0 ? void 0 : (_data_errors_ = data.errors[0]) === null || _data_errors_ === void 0 ? void 0 : _data_errors_.message);\n            } else if (data) {\n                // FIXME\n                //@ts-ignore\n                setVerifiedResponse(data);\n            }\n        },\n        onError: (error)=>{\n            const { response: { data } } = error !== null && error !== void 0 ? error : {};\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(data === null || data === void 0 ? void 0 : data.message);\n        }\n    });\n}\nfunction useOrderPayment() {\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { mutate: createOrderPayment, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.payment, {\n        onSettled: (data)=>{\n            queryClient.refetchQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ORDERS);\n            queryClient.refetchQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ORDERS_DOWNLOADS);\n        },\n        onError: (error)=>{\n            const { response: { data } } = error !== null && error !== void 0 ? error : {};\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(data === null || data === void 0 ? void 0 : data.message);\n        }\n    });\n    function formatOrderInput(input) {\n        const formattedInputs = {\n            ...input\n        };\n        createOrderPayment(formattedInputs);\n    }\n    return {\n        createOrderPayment: formatOrderInput,\n        isLoading\n    };\n}\nfunction useSavePaymentMethod() {\n    const { mutate: savePaymentMethod, isLoading, error, data } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.savePaymentMethod);\n    return {\n        savePaymentMethod,\n        data,\n        isLoading,\n        error\n    };\n}\nfunction useGetPaymentIntentOriginal(param) {\n    let { tracking_number } = param;\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const { openModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__.useModalAction)();\n    const { data, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.PAYMENT_INTENT,\n        {\n            tracking_number\n        }\n    ], ()=>_client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.getPaymentIntent({\n            tracking_number\n        }), // Make it dynamic for both gql and rest\n    {\n        enabled: false,\n        onSuccess: (data)=>{\n            var _data_payment_intent_info;\n            if (data === null || data === void 0 ? void 0 : (_data_payment_intent_info = data.payment_intent_info) === null || _data_payment_intent_info === void 0 ? void 0 : _data_payment_intent_info.is_redirect) {\n                var _data_payment_intent_info1;\n                return router.push(data === null || data === void 0 ? void 0 : (_data_payment_intent_info1 = data.payment_intent_info) === null || _data_payment_intent_info1 === void 0 ? void 0 : _data_payment_intent_info1.redirect_url);\n            } else {\n                openModal(\"PAYMENT_MODAL\", {\n                    paymentGateway: data === null || data === void 0 ? void 0 : data.payment_gateway,\n                    paymentIntentInfo: data === null || data === void 0 ? void 0 : data.payment_intent_info,\n                    trackingNumber: data === null || data === void 0 ? void 0 : data.tracking_number\n                });\n            }\n        }\n    });\n    return {\n        data,\n        getPaymentIntentQueryOriginal: refetch,\n        isLoading,\n        error\n    };\n}\nfunction useGetPaymentIntent(param) {\n    let { tracking_number, payment_gateway, recall_gateway, form_change_gateway } = param;\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const { openModal, closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__.useModalAction)();\n    const { data, isLoading, error, refetch, isFetching } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.PAYMENT_INTENT,\n        {\n            tracking_number,\n            payment_gateway,\n            recall_gateway\n        }\n    ], ()=>_client__WEBPACK_IMPORTED_MODULE_6__[\"default\"].orders.getPaymentIntent({\n            tracking_number,\n            payment_gateway,\n            recall_gateway\n        }), // Make it dynamic for both gql and rest\n    {\n        enabled: false,\n        onSuccess: (item)=>{\n            var _data_payment_intent_info;\n            let data = \"\";\n            if (lodash_isArray__WEBPACK_IMPORTED_MODULE_11___default()(item)) {\n                data = {\n                    ...item\n                };\n                data = lodash_isEmpty__WEBPACK_IMPORTED_MODULE_13___default()(data) ? [] : data[0];\n            } else if (lodash_isObject__WEBPACK_IMPORTED_MODULE_12___default()(item)) {\n                data = item;\n            }\n            if (data === null || data === void 0 ? void 0 : (_data_payment_intent_info = data.payment_intent_info) === null || _data_payment_intent_info === void 0 ? void 0 : _data_payment_intent_info.is_redirect) {\n                var _data_payment_intent_info1;\n                return router.push(data === null || data === void 0 ? void 0 : (_data_payment_intent_info1 = data.payment_intent_info) === null || _data_payment_intent_info1 === void 0 ? void 0 : _data_payment_intent_info1.redirect_url);\n            } else {\n                if (recall_gateway) window.location.reload();\n                openModal(\"PAYMENT_MODAL\", {\n                    paymentGateway: data === null || data === void 0 ? void 0 : data.payment_gateway,\n                    paymentIntentInfo: data === null || data === void 0 ? void 0 : data.payment_intent_info,\n                    trackingNumber: data === null || data === void 0 ? void 0 : data.tracking_number\n                });\n            }\n        }\n    });\n    return {\n        data,\n        getPaymentIntentQuery: refetch,\n        isLoading,\n        fetchAgain: isFetching,\n        error\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/framework/rest/order.ts\n"));

/***/ }),

/***/ "./src/lib/get-stripejs.ts":
/*!*********************************!*\
  !*** ./src/lib/get-stripejs.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @stripe/stripe-js */ \"./node_modules/@stripe/stripe-js/dist/stripe.esm.js\");\n/**\n * This is a singleton to ensure we only instantiate Stripe once.\n */ \nlet stripePromise;\nconst getStripe = ()=>{\n    if (!stripePromise) {\n        stripePromise = (0,_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__.loadStripe)(\"pk_test_placeholder\");\n    }\n    return stripePromise;\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (getStripe);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2dldC1zdHJpcGVqcy50cyIsIm1hcHBpbmdzIjoiOztBQUFBOztDQUVDLEdBQ3NEO0FBRXZELElBQUlDO0FBQ0osTUFBTUMsWUFBWTtJQUNoQixJQUFJLENBQUNELGVBQWU7UUFDbEJBLGdCQUFnQkQsNkRBQVVBLENBQUNHLHFCQUE4QztJQUMzRTtJQUNBLE9BQU9GO0FBQ1Q7QUFFQSwrREFBZUMsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbGliL2dldC1zdHJpcGVqcy50cz9mY2RmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVGhpcyBpcyBhIHNpbmdsZXRvbiB0byBlbnN1cmUgd2Ugb25seSBpbnN0YW50aWF0ZSBTdHJpcGUgb25jZS5cbiAqL1xuaW1wb3J0IHsgU3RyaXBlLCBsb2FkU3RyaXBlIH0gZnJvbSAnQHN0cmlwZS9zdHJpcGUtanMnO1xuXG5sZXQgc3RyaXBlUHJvbWlzZTogUHJvbWlzZTxTdHJpcGUgfCBudWxsPjtcbmNvbnN0IGdldFN0cmlwZSA9ICgpID0+IHtcbiAgaWYgKCFzdHJpcGVQcm9taXNlKSB7XG4gICAgc3RyaXBlUHJvbWlzZSA9IGxvYWRTdHJpcGUocHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1RSSVBFX1BVQkxJU0hBQkxFX0tFWSEpO1xuICB9XG4gIHJldHVybiBzdHJpcGVQcm9taXNlO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgZ2V0U3RyaXBlO1xuIl0sIm5hbWVzIjpbImxvYWRTdHJpcGUiLCJzdHJpcGVQcm9taXNlIiwiZ2V0U3RyaXBlIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NUUklQRV9QVUJMSVNIQUJMRV9LRVkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/lib/get-stripejs.ts\n"));

/***/ })

}]);