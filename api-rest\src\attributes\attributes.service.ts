import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreateAttributeDto } from './dto/create-attribute.dto';
import { UpdateAttributeDto } from './dto/update-attribute.dto';
import { Attribute } from './entities/attribute.entity';

@Injectable()
export class AttributesService {
  constructor(
    @InjectModel(Attribute)
    private attributeModel: typeof Attribute,
  ) {}

  async create(createAttributeDto: CreateAttributeDto): Promise<Attribute> {
    return this.attributeModel.create(createAttributeDto as any);
  }

  async findAll(): Promise<Attribute[]> {
    return this.attributeModel.findAll();
  }

  async findOne(param: string): Promise<Attribute | null> {
    const id = Number(param);
    if (!isNaN(id)) {
      return this.attributeModel.findByPk(id);
    }
    return this.attributeModel.findOne({ where: { slug: param } });
  }

  async update(
    id: number,
    updateAttributeDto: UpdateAttributeDto,
  ): Promise<Attribute | null> {
    await this.attributeModel.update(updateAttributeDto as any, {
      where: { id },
    });
    return this.attributeModel.findByPk(id);
  }

  remove(id: number) {
    return `This action removes a #${id} attribute`;
  }
}
