import { Model } from 'sequelize-typescript';
import { Product } from 'src/products/entities/product.entity';
import { Type } from 'src/types/entities/type.entity';
export declare class Tag extends Model {
    id: number;
    name: string;
    slug: string;
    parent: number;
    details: string;
    image: any;
    icon: string;
    type_id: number;
    type: Type;
    products: Product[];
    language: string;
    translated_languages: string;
    created_at: Date;
    updated_at: Date;
}
