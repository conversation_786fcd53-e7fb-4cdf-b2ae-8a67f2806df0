"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_layouts_mobile-menu_mobile-authorized-menu_tsx";
exports.ids = ["src_components_layouts_mobile-menu_mobile-authorized-menu_tsx"];
exports.modules = {

/***/ "./src/components/layouts/mobile-menu/mobile-authorized-menu.tsx":
/*!***********************************************************************!*\
  !*** ./src/components/layouts/mobile-menu/mobile-authorized-menu.tsx ***!
  \***********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileAuthorizedMenu)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _config_site__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/site */ \"./src/config/site.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/drawer/drawer-wrapper */ \"./src/components/ui/drawer/drawer-wrapper.tsx\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jotai */ \"jotai\");\n/* harmony import */ var _store_drawer_atom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/drawer-atom */ \"./src/store/drawer-atom.ts\");\n/* harmony import */ var _framework_user__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/framework/user */ \"./src/framework/rest/user.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_4__, jotai__WEBPACK_IMPORTED_MODULE_5__, _store_drawer_atom__WEBPACK_IMPORTED_MODULE_6__, _framework_user__WEBPACK_IMPORTED_MODULE_7__]);\n([_components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_4__, jotai__WEBPACK_IMPORTED_MODULE_5__, _store_drawer_atom__WEBPACK_IMPORTED_MODULE_6__, _framework_user__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction MobileAuthorizedMenu() {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { me } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_7__.useUser)();\n    const [_, closeSidebar] = (0,jotai__WEBPACK_IMPORTED_MODULE_5__.useAtom)(_store_drawer_atom__WEBPACK_IMPORTED_MODULE_6__.drawerAtom);\n    const { mutate: logout } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_7__.useLogout)();\n    function handleClick(path) {\n        router.push(path);\n        closeSidebar({\n            display: false,\n            view: \"\"\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            className: \"grow\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex w-full items-center justify-between border-t border-dashed border-border-200 bg-gray-100 px-5 pt-3 text-sm font-semibold capitalize text-body focus:outline-none ltr:text-left rtl:text-right md:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"text-total-points\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: me?.wallet?.total_points ?? 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex w-full items-center justify-between bg-gray-100 px-5 pt-3 text-sm font-semibold capitalize text-body focus:outline-none ltr:text-left rtl:text-right md:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"text-points-used\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: me?.wallet?.points_used ?? 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex w-full items-center justify-between border-b border-dashed border-border-200 bg-gray-100 px-5 py-3 text-sm font-semibold capitalize text-body focus:outline-none ltr:text-left rtl:text-right md:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"text-available-points\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: me?.wallet?.available_points ?? 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                _config_site__WEBPACK_IMPORTED_MODULE_1__.siteSettings.authorizedLinksMobile.map(({ href, label })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"block cursor-pointer px-5 py-3 text-sm font-semibold capitalize text-heading transition duration-200 hover:text-accent md:px-8\",\n                            onClick: ()=>handleClick(href),\n                            children: t(label)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this)\n                    }, `${href}${label}`, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"block cursor-pointer px-5 py-3 text-sm font-semibold capitalize text-heading transition duration-200 hover:text-accent md:px-8\",\n                        onClick: ()=>logout(),\n                        children: t(\"auth-menu-logout\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-authorized-menu.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layouts/mobile-menu/mobile-authorized-menu.tsx\n");

/***/ }),

/***/ "./src/config/site.ts":
/*!****************************!*\
  !*** ./src/config/site.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siteSettings: () => (/* binding */ siteSettings)\n/* harmony export */ });\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"./src/types/index.ts\");\n\n\nconst siteSettings = {\n    name: \"oneKart\",\n    description: \"\",\n    logo: {\n        url: \"/logo.svg\",\n        alt: \"oneKart\",\n        href: \"/grocery\",\n        width: 128,\n        height: 40\n    },\n    defaultLanguage: \"en\",\n    currencyCode: \"USD\",\n    product: {\n        placeholderImage: \"/product-placeholder.svg\",\n        cardMaps: {\n            grocery: \"Krypton\",\n            furniture: \"Radon\",\n            bag: \"Oganesson\",\n            makeup: \"Neon\",\n            book: \"Xenon\",\n            medicine: \"Helium\",\n            default: \"Argon\"\n        }\n    },\n    authorizedLinks: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"auth-menu-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"auth-menu-my-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.checkout,\n            label: \"auth-menu-checkout\"\n        }\n    ],\n    authorizedLinksMobile: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"auth-menu-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.notifyLogs,\n            label: \"profile-sidebar-notifications\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"auth-menu-my-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.cards,\n            label: \"profile-sidebar-my-cards\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.questions,\n            label: \"profile-sidebar-my-questions\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.refunds,\n            label: \"text-my-refunds\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.reports,\n            label: \"profile-sidebar-my-reports\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.checkout,\n            label: \"auth-menu-checkout\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.changePassword,\n            label: \"profile-sidebar-password\"\n        }\n    ],\n    dashboardSidebarMenu: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"profile-sidebar-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.changePassword,\n            label: \"profile-sidebar-password\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.notifyLogs,\n            label: \"profile-sidebar-notifications\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.cards,\n            label: \"profile-sidebar-my-cards\",\n            // MultiPayment: Make it dynamic or from mapper\n            cardsPayment: [\n                _types__WEBPACK_IMPORTED_MODULE_1__.PaymentGateway.STRIPE\n            ]\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"profile-sidebar-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.downloads,\n            label: \"profile-sidebar-downloads\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.questions,\n            label: \"profile-sidebar-my-questions\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.refunds,\n            label: \"text-my-refunds\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.reports,\n            label: \"profile-sidebar-my-reports\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help,\n            label: \"profile-sidebar-help\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.logout,\n            label: \"profile-sidebar-logout\"\n        }\n    ],\n    sellingAdvertisement: {\n        image: {\n            src: \"/selling.png\",\n            alt: \"Selling Advertisement\"\n        }\n    },\n    cta: {\n        mockup_img_src: \"/mockup-img.png\",\n        play_store_link: \"/\",\n        app_store_link: \"/\"\n    },\n    headerLinks: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shops,\n            icon: null,\n            label: \"nav-menu-shops\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.coupons,\n            icon: null,\n            label: \"nav-menu-offer\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.contactUs,\n            label: \"nav-menu-contact\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.becomeSeller,\n            label: \"Become a seller\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.flashSale,\n            label: \"nav-menu-flash-sale\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.manufacturers,\n            label: \"text-manufacturers\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.authors,\n            label: \"text-authors\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help,\n            label: \"nav-menu-faq\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.terms,\n            label: \"nav-menu-terms\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.customerRefundPolicies,\n            label: \"nav-menu-refund-policy\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.vendorRefundPolicies,\n            label: \"nav-menu-vendor-refund-policy\"\n        }\n    ],\n    footer: {\n        // copyright: {\n        //   name: 'RedQ, Inc',\n        //   href: 'https://redq.io/',\n        // },\n        // address: '2429 River Drive, Suite 35 Cottonhall, CA 2296 United Kingdom',\n        // email: '<EMAIL>',\n        // phone: '******-698-0694',\n        menus: [\n            {\n                title: \"text-explore\",\n                links: [\n                    {\n                        name: \"Shops\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shops\n                    },\n                    {\n                        name: \"Authors\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.authors\n                    },\n                    {\n                        name: \"Flash Deals\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes?.flashSale\n                    },\n                    {\n                        name: \"Coupon\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.coupons\n                    }\n                ]\n            },\n            {\n                title: \"text-customer-service\",\n                links: [\n                    {\n                        name: \"text-faq-help\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help\n                    },\n                    {\n                        name: \"Vendor Refund Policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.vendorRefundPolicies\n                    },\n                    {\n                        name: \"Customer Refund Policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.customerRefundPolicies\n                    }\n                ]\n            },\n            {\n                title: \"text-our-information\",\n                links: [\n                    {\n                        name: \"Manufacturers\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes?.manufacturers\n                    },\n                    {\n                        name: \"Privacy policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.privacy\n                    },\n                    {\n                        name: \"text-terms-condition\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.terms\n                    },\n                    {\n                        name: \"text-contact-us\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.contactUs\n                    }\n                ]\n            }\n        ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/config/site.ts\n");

/***/ })

};
;