import { CreateTaxDto } from './dto/create-tax.dto';
import { UpdateTaxDto } from './dto/update-tax.dto';
import { Tax } from './entities/tax.entity';
export declare class TaxesService {
    private taxModel;
    constructor(taxModel: typeof Tax);
    create(createTaxDto: CreateTaxDto): Promise<Tax>;
    findAll(): Promise<Tax[]>;
    findOne(id: number): Promise<Tax | null>;
    update(id: number, updateTaxDto: UpdateTaxDto): Promise<Tax | null>;
    remove(id: number): string;
}
