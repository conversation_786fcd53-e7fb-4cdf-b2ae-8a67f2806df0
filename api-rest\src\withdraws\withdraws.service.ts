import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreateWithdrawDto } from './dto/create-withdraw.dto';
import { ApproveWithdrawDto } from './dto/approve-withdraw.dto';
import { Withdraw } from './entities/withdraw.entity';
import { GetWithdrawsDto, WithdrawPaginator } from './dto/get-withdraw.dto';
import { paginate } from 'src/common/pagination/paginate';

@Injectable()
export class WithdrawsService {
  constructor(
    @InjectModel(Withdraw)
    private withdrawModel: typeof Withdraw,
  ) {}

  async create(createWithdrawDto: CreateWithdrawDto): Promise<Withdraw> {
    return this.withdrawModel.create(createWithdrawDto as any);
  }

  async getWithdraws({
    limit,
    page,
    status,
    shop_id,
  }: GetWithdrawsDto): Promise<WithdrawPaginator> {
    if (!page) page = 1;
    if (!limit) limit = 15;
    const offset = (page - 1) * limit;

    // TODO: Implement proper Sequelize query with status and shop_id filtering
    const { count, rows: data } = await this.withdrawModel.findAndCountAll({
      limit,
      offset,
    });

    const url = `/withdraws?limit=${limit}`;
    return {
      data,
      ...paginate(count, page, limit, data.length, url),
    };
  }

  findOne(id: number) {
    return `This action returns a #${id} withdraw`;
  }

  async update(
    id: number,
    updateWithdrawDto: ApproveWithdrawDto,
  ): Promise<Withdraw | null> {
    await this.withdrawModel.update(updateWithdrawDto as any, {
      where: { id },
    });
    return this.withdrawModel.findByPk(id);
  }

  remove(id: number) {
    return `This action removes a #${id} withdraw`;
  }
}
