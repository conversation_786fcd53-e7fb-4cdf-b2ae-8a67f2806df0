import { CreateWithdrawDto } from './dto/create-withdraw.dto';
import { ApproveWithdrawDto } from './dto/approve-withdraw.dto';
import { Withdraw } from './entities/withdraw.entity';
import { GetWithdrawsDto, WithdrawPaginator } from './dto/get-withdraw.dto';
export declare class WithdrawsService {
    private withdrawModel;
    constructor(withdrawModel: typeof Withdraw);
    create(createWithdrawDto: CreateWithdrawDto): Promise<Withdraw>;
    getWithdraws({ limit, page, status, shop_id, }: GetWithdrawsDto): Promise<WithdrawPaginator>;
    findOne(id: number): string;
    update(id: number, updateWithdrawDto: ApproveWithdrawDto): Promise<Withdraw | null>;
    remove(id: number): string;
}
