const express = require("express");
const { v4: uuidv4 } = require("uuid");
const Joi = require("joi");

const router = express.Router();

// In-memory storage (in production, use a database)
let students = [
  {
    id: uuidv4(),
    firstName: "<PERSON>",
    lastName: "Doe",
    email: "<EMAIL>",
    age: 20,
    course: "Computer Science",
    enrollmentDate: "2023-09-01",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: uuidv4(),
    firstName: "Jane",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    age: 19,
    course: "Mathematics",
    enrollmentDate: "2023-09-01",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Validation schema
const studentSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  age: Joi.number().integer().min(16).max(100).required(),
  course: Joi.string().min(2).max(100).required(),
  enrollmentDate: Joi.date().iso().optional(),
  status: Joi.string()
    .valid("active", "inactive", "graduated")
    .default("active"),
});

// GET /api/students - List all students
router.get("/", (req, res) => {
  try {
    const { page = 1, limit = 10, course, status, search } = req.query;

    let filteredStudents = [...students];

    // Filter by course
    if (course) {
      filteredStudents = filteredStudents.filter((student) =>
        student.course.toLowerCase().includes(course.toLowerCase())
      );
    }

    // Filter by status
    if (status) {
      filteredStudents = filteredStudents.filter(
        (student) => student.status === status
      );
    }

    // Search by name or email
    if (search) {
      filteredStudents = filteredStudents.filter(
        (student) =>
          student.firstName.toLowerCase().includes(search.toLowerCase()) ||
          student.lastName.toLowerCase().includes(search.toLowerCase()) ||
          student.email.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedStudents = filteredStudents.slice(startIndex, endIndex);

    const totalPages = Math.ceil(filteredStudents.length / limit);

    res.status(200).json({
      success: true,
      data: paginatedStudents,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalStudents: filteredStudents.length,
        hasNext: endIndex < filteredStudents.length,
        hasPrev: startIndex > 0,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching students",
      error: error.message,
    });
  }
});

// GET /api/students/:id - Get student by ID
router.get("/:id", (req, res) => {
  try {
    const student = students.find((s) => s.id === req.params.id);

    if (!student) {
      return res.status(404).json({
        success: false,
        message: "Student not found",
      });
    }

    res.status(200).json({
      success: true,
      data: student,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching student",
      error: error.message,
    });
  }
});

// POST /api/students - Create new student
router.post("/", (req, res) => {
  try {
    // Validate request body
    const { error, value } = studentSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: error.details.map((detail) => detail.message),
      });
    }

    // Check if email already exists
    const existingStudent = students.find((s) => s.email === value.email);
    if (existingStudent) {
      return res.status(409).json({
        success: false,
        message: "Student with this email already exists",
      });
    }

    // Create new student
    const newStudent = {
      id: uuidv4(),
      ...value,
      enrollmentDate:
        value.enrollmentDate || new Date().toISOString().split("T")[0],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    students.push(newStudent);

    res.status(201).json({
      success: true,
      message: "Student created successfully",
      data: newStudent,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating student",
      error: error.message,
    });
  }
});

// PUT /api/students/:id - Update student
router.put("/:id", (req, res) => {
  try {
    const studentIndex = students.findIndex((s) => s.id === req.params.id);

    if (studentIndex === -1) {
      return res.status(404).json({
        success: false,
        message: "Student not found",
      });
    }

    // Validate request body
    const { error, value } = studentSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: error.details.map((detail) => detail.message),
      });
    }

    // Check if email already exists (excluding current student)
    const existingStudent = students.find(
      (s) => s.email === value.email && s.id !== req.params.id
    );
    if (existingStudent) {
      return res.status(409).json({
        success: false,
        message: "Student with this email already exists",
      });
    }

    // Update student
    students[studentIndex] = {
      ...students[studentIndex],
      ...value,
      updatedAt: new Date().toISOString(),
    };

    res.status(200).json({
      success: true,
      message: "Student updated successfully",
      data: students[studentIndex],
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating student",
      error: error.message,
    });
  }
});

// DELETE /api/students/:id - Delete student
router.delete("/:id", (req, res) => {
  try {
    const studentIndex = students.findIndex((s) => s.id === req.params.id);

    if (studentIndex === -1) {
      return res.status(404).json({
        success: false,
        message: "Student not found",
      });
    }

    const deletedStudent = students.splice(studentIndex, 1)[0];

    res.status(200).json({
      success: true,
      message: "Student deleted successfully",
      data: deletedStudent,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error deleting student",
      error: error.message,
    });
  }
});

// GET /api/students/stats - Get student statistics
router.get("/stats/overview", (req, res) => {
  try {
    const totalStudents = students.length;
    const activeStudents = students.filter((s) => s.status === "active").length;
    const inactiveStudents = students.filter(
      (s) => s.status === "inactive"
    ).length;
    const graduatedStudents = students.filter(
      (s) => s.status === "graduated"
    ).length;

    // Course distribution
    const courseStats = students.reduce((acc, student) => {
      acc[student.course] = (acc[student.course] || 0) + 1;
      return acc;
    }, {});

    res.status(200).json({
      success: true,
      data: {
        totalStudents,
        activeStudents,
        inactiveStudents,
        graduatedStudents,
        courseDistribution: courseStats,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching statistics",
      error: error.message,
    });
  }
});

module.exports = router;
