"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_question_question-delete-view_tsx"],{

/***/ "./src/components/question/question-delete-view.tsx":
/*!**********************************************************!*\
  !*** ./src/components/question/question-delete-view.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_question__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/question */ \"./src/data/question.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst QuestionDeleteView = ()=>{\n    _s();\n    const { mutate: deleteQuestion, isLoading: loading } = (0,_data_question__WEBPACK_IMPORTED_MODULE_3__.useDeleteQuestionMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteQuestion({\n            id: data === null || data === void 0 ? void 0 : data.id\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\question\\\\question-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QuestionDeleteView, \"41G9fGtzvzgxCC7nkWagq+R5F/g=\", false, function() {\n    return [\n        _data_question__WEBPACK_IMPORTED_MODULE_3__.useDeleteQuestionMutation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction\n    ];\n});\n_c = QuestionDeleteView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (QuestionDeleteView);\nvar _c;\n$RefreshReg$(_c, \"QuestionDeleteView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/question/question-delete-view.tsx\n"));

/***/ }),

/***/ "./src/data/client/question.ts":
/*!*************************************!*\
  !*** ./src/data/client/question.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   questionClient: function() { return /* binding */ questionClient; }\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\n\n\n\nconst questionClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.QUESTIONS),\n    get (param) {\n        let { id } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(\"\".concat(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.QUESTIONS, \"/\").concat(id));\n    },\n    paginated: (param)=>{\n        let { type, shop_id, ...params } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.QUESTIONS, {\n            searchJoin: \"and\",\n            with: \"product;user\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                shop_id\n            })\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/question.ts\n"));

/***/ }),

/***/ "./src/data/question.ts":
/*!******************************!*\
  !*** ./src/data/question.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDeleteQuestionMutation: function() { return /* binding */ useDeleteQuestionMutation; },\n/* harmony export */   useQuestionsQuery: function() { return /* binding */ useQuestionsQuery; },\n/* harmony export */   useReplyQuestionMutation: function() { return /* binding */ useReplyQuestionMutation; }\n/* harmony export */ });\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _data_client_question__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/data/client/question */ \"./src/data/client/question.ts\");\n\n\n\n\n\n\n\nconst useQuestionsQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.QUESTIONS,\n        options\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _data_client_question__WEBPACK_IMPORTED_MODULE_5__.questionClient.paginated(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        keepPreviousData: true\n    });\n    var _data_data;\n    return {\n        questions: (_data_data = data === null || data === void 0 ? void 0 : data.data) !== null && _data_data !== void 0 ? _data_data : [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\nconst useReplyQuestionMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_question__WEBPACK_IMPORTED_MODULE_5__.questionClient.update, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.QUESTIONS);\n        }\n    });\n};\nconst useDeleteQuestionMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_question__WEBPACK_IMPORTED_MODULE_5__.questionClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.QUESTIONS);\n        }\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/question.ts\n"));

/***/ })

}]);