import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { MyWishlistsController } from './my-wishlists.controller';
import { MyWishlistService } from './my-wishlists.service';
import { WishlistsController } from './wishlists.controller';
import { WishlistsService } from './wishlists.service';
import { Wishlist } from './entities/wishlist.entity';
import { Product } from '../products/entities/product.entity';

@Module({
  imports: [SequelizeModule.forFeature([Wishlist, Product])],
  controllers: [WishlistsController, MyWishlistsController],
  providers: [WishlistsService, MyWishlistService],
})
export class WishlistsModule {}
