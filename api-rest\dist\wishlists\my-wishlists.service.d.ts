import { Wishlist } from './entities/wishlist.entity';
import { GetWishlistDto } from './dto/get-wishlists.dto';
import { CreateWishlistDto } from './dto/create-wishlists.dto';
import { UpdateWishlistDto } from './dto/update-wishlists.dto';
import { Product } from '../products/entities/product.entity';
export declare class MyWishlistService {
    private wishlistModel;
    private productModel;
    constructor(wishlistModel: typeof Wishlist, productModel: typeof Product);
    findAMyWishlists({ limit, page, search }: GetWishlistDto): Promise<{
        count: number;
        current_page: number;
        firstItem: number;
        lastItem: number;
        last_page: number;
        per_page: number;
        total: number;
        first_page_url: string;
        last_page_url: string;
        next_page_url: string;
        prev_page_url: string;
        data: Product[];
    }>;
    findAMyWishlist(id: number): Promise<Wishlist | null>;
    create(createWishlistDto: CreateWishlistDto): Promise<Wishlist>;
    update(id: number, updateWishlistDto: UpdateWishlistDto): Promise<Wishlist | null>;
    delete(id: number): Promise<number>;
}
