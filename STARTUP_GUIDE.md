# oneKart E-Commerce Platform - Startup Guide

This guide explains how to start the oneKart e-commerce platform in both development and production modes using the provided startup scripts.

## 🚀 Quick Start

### Development Mode (Recommended for development)

**Cross-Platform (Node.js - Recommended):**

```bash
node start-dev.js
```

**Linux/macOS:**

```bash
./start-dev.sh
```

**Windows (PowerShell):**

```powershell
.\start-dev.ps1
```

**Windows (Batch):**

```cmd
start-dev.bat
```

### Production Mode (Docker-based)

**Linux/macOS:**

```bash
./start-prod.sh
```

**Windows:**

```cmd
start-prod.bat
```

## 📋 Prerequisites

### Development Mode

- **Node.js** 18 or higher
- **npm** (comes with Node.js)
- **curl** (optional, for health checks)

### Production Mode

- **Docker** and **Docker Compose**
- **curl** (optional, for health checks)

## 🛠️ Script Features

### Development Scripts

#### Cross-Platform Node.js Script (`start-dev.js`) - **RECOMMENDED**

**Features:**

- ✅ Works on Windows, macOS, and Linux
- ✅ Reliable process management
- ✅ Automatic prerequisite checking
- ✅ Port conflict resolution
- ✅ Service health monitoring
- ✅ Dependency installation support
- ✅ Graceful shutdown handling
- ✅ Colored output for better UX

**Usage:**

```bash
# Start with existing dependencies
node start-dev.js

# Install dependencies and start
node start-dev.js --install

# Show help
node start-dev.js --help
```

#### Platform-Specific Scripts

**Linux/macOS (`start-dev.sh`):**

```bash
./start-dev.sh --install
```

**Windows PowerShell (`start-dev.ps1`):**

```powershell
.\start-dev.ps1 -Install
```

**Windows Batch (`start-dev.bat`):**

```cmd
start-dev.bat --install
```

**Services Started:**

- **API Server**: http://localhost:9000
- **Admin Dashboard**: http://localhost:3002
- **Shop Frontend**: http://localhost:3005

### Production Scripts (`start-prod.sh` / `start-prod.bat`)

**Features:**

- ✅ Docker containerization
- ✅ Production optimization
- ✅ Health monitoring
- ✅ Automatic environment configuration
- ✅ Service dependency management
- ✅ Restart policies

**Usage:**

```bash
# Build and start all services
./start-prod.sh

# Start without rebuilding images
./start-prod.sh --no-build

# Skip health check waiting
./start-prod.sh --skip-wait

# Windows equivalent
start-prod.bat --no-build
```

**Services Started:**

- **API Server**: http://localhost:9000
- **Admin Dashboard**: http://localhost:3002
- **Shop Frontend**: http://localhost:3005

## 🎨 oneKart Features

Both development and production modes include:

- **🌌 Purple Nebula Dark Theme**: Modern dark theme with purple accents
- **🏷️ oneKart Branding**: Complete rebranding from PickBazar to oneKart
- **📱 Responsive Design**: Mobile-first approach
- **🔒 Authentication**: NextAuth.js integration
- **💳 Payment Processing**: Stripe and PayPal support
- **🌐 Multi-language**: i18n ready
- **📊 Admin Dashboard**: Complete management interface

## 🔧 Troubleshooting

### Common Issues

**Port Already in Use:**

- Development scripts automatically kill processes on required ports
- For manual cleanup: `lsof -ti :PORT | xargs kill -9` (Linux/macOS)

**Docker Issues:**

- Ensure Docker daemon is running
- Check Docker Compose version compatibility
- Try `docker system prune` to clean up

**Node.js Version:**

- Ensure Node.js 18+ is installed
- Use `nvm` to manage Node.js versions

**Permission Issues (Linux/macOS):**

```bash
chmod +x start-dev.sh start-prod.sh
```

### Service Health Checks

**Development Mode:**

```bash
# Check if services are running
curl http://localhost:9000/api  # API
curl http://localhost:3002      # Admin
curl http://localhost:3005      # Shop
```

**Production Mode:**

```bash
# Check container status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Restart services
docker-compose -f docker-compose.prod.yml restart
```

## 📁 Project Structure

```
oneKart/
├── api-rest/           # NestJS API backend
├── admin-rest/         # React admin dashboard
├── shop/              # Next.js shop frontend
├── start-dev.sh       # Development startup script (Linux/macOS)
├── start-dev.bat      # Development startup script (Windows)
├── start-prod.sh      # Production startup script (Linux/macOS)
├── start-prod.bat     # Production startup script (Windows)
├── docker-compose.prod.yml  # Production Docker configuration
└── STARTUP_GUIDE.md   # This guide
```

## 🌐 Environment Configuration

### Development

Environment variables are loaded from individual `.env` files in each service directory.

### Production

The production script creates `.env.production` with default configuration. Update as needed:

```env
# Database
DATABASE_URL=your_database_url

# Security
NEXTAUTH_SECRET=your_secret_key
JWT_SECRET=your_jwt_secret

# URLs
NEXT_PUBLIC_REST_API_ENDPOINT=http://localhost:9000/api
```

## 🔄 Development Workflow

1. **Start Development**: `./start-dev.sh --install`
2. **Make Changes**: Edit code with hot reload
3. **Test**: Access applications at respective URLs
4. **Stop**: Press `Ctrl+C` in terminal

## 🚢 Production Deployment

1. **Build & Start**: `./start-prod.sh`
2. **Monitor**: Check logs and health status
3. **Scale**: Use Docker Compose scaling features
4. **Update**: Rebuild images and restart services

## 📞 Support

For issues or questions:

- Check the troubleshooting section above
- Review service logs for detailed error information
- Ensure all prerequisites are properly installed

## 🎯 Next Steps

After starting the platform:

1. Access the admin dashboard to configure settings
2. Set up your product catalog
3. Configure payment methods
4. Customize the Purple Nebula theme as needed
5. Deploy to your production environment

---

**oneKart E-Commerce Platform** - Your next ecommerce solution with modern Purple Nebula dark theme! 🌌🛍️
