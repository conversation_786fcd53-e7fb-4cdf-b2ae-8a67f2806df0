import { CreateProductDto } from './dto/create-product.dto';
import { GetProductsDto, ProductPaginator } from './dto/get-products.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { Product } from './entities/product.entity';
import { GetPopularProductsDto } from './dto/get-popular-products.dto';
import { GetBestSellingProductsDto } from './dto/get-best-selling-products.dto';
export declare class ProductsService {
    private productModel;
    constructor(productModel: typeof Product);
    create(createProductDto: CreateProductDto): Promise<Product>;
    getProducts({ limit, page, search, }: GetProductsDto): Promise<ProductPaginator>;
    getProductBySlug(slug: string): Promise<Product>;
    getPopularProducts({ limit, type_slug, }: GetPopularProductsDto): Promise<Product[]>;
    getBestSellingProducts({ limit, type_slug, }: GetBestSellingProductsDto): Promise<Product[]>;
    getProductsStock({ limit, page, search }: GetProductsDto): ProductPaginator;
    getDraftProducts({ limit, page, search }: GetProductsDto): ProductPaginator;
    update(id: number, updateProductDto: UpdateProductDto): Promise<[number, Product[]]>;
    remove(id: number): Promise<number>;
}
