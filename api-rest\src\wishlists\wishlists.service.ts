import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import Fuse from 'fuse.js';
import { paginate } from 'src/common/pagination/paginate';
import { Wishlist } from './entities/wishlist.entity';
import { GetWishlistDto } from './dto/get-wishlists.dto';
import { CreateWishlistDto } from './dto/create-wishlists.dto';
import { UpdateWishlistDto } from './dto/update-wishlists.dto';
import { Product } from '../products/entities/product.entity';
@Injectable()
export class WishlistsService {
  constructor(
    @InjectModel(Wishlist)
    private wishlistModel: typeof Wishlist,
    @InjectModel(Product)
    private productModel: typeof Product,
  ) {}

  async findAllWishlists({ limit, page, search }: GetWishlistDto) {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const offset = (page - 1) * limit;

    // TODO: Implement proper Sequelize query
    const { count, rows: data } = await this.wishlistModel.findAndCountAll({
      limit,
      offset,
    });

    // TODO: Implement search functionality

    const url = `/wishlists?with=shop&orderBy=created_at&sortedBy=desc`;
    return {
      data,
      ...paginate(count, page, limit, data.length, url),
    };
  }

  async findWishlist(id: number): Promise<Wishlist | null> {
    return this.wishlistModel.findByPk(id);
  }

  async create(createWishlistDto: CreateWishlistDto): Promise<Wishlist> {
    return this.wishlistModel.create(createWishlistDto as any);
  }

  async update(
    id: number,
    updateWishlistDto: UpdateWishlistDto,
  ): Promise<Wishlist | null> {
    await this.wishlistModel.update(updateWishlistDto as any, {
      where: { id },
    });
    return this.wishlistModel.findByPk(id);
  }

  async delete(id: number): Promise<number> {
    return this.wishlistModel.destroy({ where: { id } });
  }

  async isInWishlist(product_id: number): Promise<boolean> {
    // TODO: Implement proper wishlist check with Sequelize
    const product = await this.productModel.findByPk(product_id);
    return product?.in_wishlist || false;
  }

  async toggle({ product_id }: CreateWishlistDto): Promise<boolean> {
    // TODO: Implement proper wishlist toggle with Sequelize
    const product = await this.productModel.findByPk(product_id);
    if (product) {
      const newWishlistStatus = !product.in_wishlist;
      await this.productModel.update(
        { in_wishlist: newWishlistStatus },
        { where: { id: product_id } },
      );
      return newWishlistStatus;
    }
    return false;
  }
}
