"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadsService = void 0;
const common_1 = require("@nestjs/common");
let UploadsService = class UploadsService {
    async uploadFiles(files) {
        return files.map((file, index) => ({
            id: `temp-${Date.now()}-${index}`,
            original: `http://localhost:9000/ecommerce-uploads/uploads/${file.originalname}`,
            thumbnail: `http://localhost:9000/ecommerce-uploads/uploads/${file.originalname}`,
            file_name: file.originalname,
            size: file.size,
            mime_type: file.mimetype,
        }));
    }
    async deleteFile(fileName) {
        console.log(`Mock delete file: ${fileName}`);
    }
    async getFileUrl(fileName) {
        return `http://localhost:9000/ecommerce-uploads/uploads/${fileName}`;
    }
    getPublicUrl(fileName) {
        return `http://localhost:9000/ecommerce-uploads/uploads/${fileName}`;
    }
};
UploadsService = __decorate([
    (0, common_1.Injectable)()
], UploadsService);
exports.UploadsService = UploadsService;
//# sourceMappingURL=uploads.service.js.map