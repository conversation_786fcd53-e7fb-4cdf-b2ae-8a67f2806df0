import { Product } from '../entities/product.entity';
declare const CreateProductDto_base: import("@nestjs/common").Type<Omit<Product, "id" | "type" | "slug" | "translated_languages" | "created_at" | "updated_at" | "shop" | "categories" | "tags" | "pivot" | "related_products">>;
export declare class CreateProductDto extends CreateProductDto_base {
    categories: number[];
    tags: number[];
}
export {};
