import { Product } from '../entities/product.entity';
declare const CreateProductDto_base: import("@nestjs/common").Type<Omit<Product, "tags" | "id" | "type" | "slug" | "translated_languages" | "created_at" | "updated_at" | "categories" | "pivot" | "shop" | "related_products">>;
export declare class CreateProductDto extends CreateProductDto_base {
    categories: number[];
    tags: number[];
}
export {};
