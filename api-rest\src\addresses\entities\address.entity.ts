import { Column, Model, Table, DataType } from 'sequelize-typescript';

@Table
export class Address extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column
  title: string;

  @Column
  type: string;

  @Column
  default: boolean;

  @Column
  address: string;

  @Column
  city: string;

  @Column
  state: string;

  @Column
  zip: string;

  @Column
  country: string;
}

// Type alias for backward compatibility
export type UserAddress = Address;
