"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttributeValue = void 0;
const openapi = require("@nestjs/swagger");
const sequelize_typescript_1 = require("sequelize-typescript");
const attribute_entity_1 = require("./attribute.entity");
let AttributeValue = class AttributeValue extends sequelize_typescript_1.Model {
    static _OPENAPI_METADATA_FACTORY() {
        return { id: { required: true, type: () => Number }, shop_id: { required: true, type: () => Number }, value: { required: true, type: () => String }, meta: { required: false, type: () => String }, attribute_id: { required: true, type: () => Number }, attribute: { required: true, type: () => require("./attribute.entity").Attribute } };
    }
};
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    }),
    __metadata("design:type", Number)
], AttributeValue.prototype, "id", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        allowNull: false,
    }),
    __metadata("design:type", Number)
], AttributeValue.prototype, "shop_id", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: false,
    }),
    __metadata("design:type", String)
], AttributeValue.prototype, "value", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: true,
    }),
    __metadata("design:type", String)
], AttributeValue.prototype, "meta", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => attribute_entity_1.Attribute),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        allowNull: false,
    }),
    __metadata("design:type", Number)
], AttributeValue.prototype, "attribute_id", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => attribute_entity_1.Attribute),
    __metadata("design:type", attribute_entity_1.Attribute)
], AttributeValue.prototype, "attribute", void 0);
AttributeValue = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: 'attribute_values',
        timestamps: true,
    })
], AttributeValue);
exports.AttributeValue = AttributeValue;
//# sourceMappingURL=attribute-value.entity.js.map