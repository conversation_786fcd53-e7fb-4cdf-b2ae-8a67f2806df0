declare const _default: (() => {
    dialect: string;
    storage: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    models: any[];
    autoLoadModels: boolean;
    synchronize: boolean;
    logging: boolean | {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    dialect: string;
    storage: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    models: any[];
    autoLoadModels: boolean;
    synchronize: boolean;
    logging: boolean | {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
}>;
export default _default;
