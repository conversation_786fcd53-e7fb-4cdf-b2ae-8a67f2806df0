import { User, Permission } from '../users/entities/user.entity';
import { Profile } from '../users/entities/profile.entity';
import { Category, ProductCategory } from '../categories/entities/category.entity';
import { Product, ProductTag } from '../products/entities/product.entity';
import { Shop, Balance } from '../shops/entities/shop.entity';
import { Type } from '../types/entities/type.entity';
import { Tag } from '../tags/entities/tag.entity';
import { Manufacturer } from '../manufacturers/entities/manufacturer.entity';
import { Shipping } from '../shippings/entities/shipping.entity';
import { Tax } from '../taxes/entities/tax.entity';
import { Coupon } from '../coupons/entities/coupon.entity';
import { Review } from '../reviews/entities/review.entity';
import { Address } from '../addresses/entities/address.entity';
import { Analytics } from '../analytics/entities/analytics.entity';
import { Ai } from '../ai/entities/ai.entity';
import { Attribute } from '../attributes/entities/attribute.entity';
import { BecomeSeller } from '../become-seller/entities/become-seller.entity';
import { Wishlist } from '../wishlists/entities/wishlist.entity';
import { Withdraw } from '../withdraws/entities/withdraw.entity';
declare const _default: (() => {
    dialect: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    dialectOptions: {
        ssl: {
            require: boolean;
            rejectUnauthorized: boolean;
        };
    };
    models: (typeof Attribute | typeof Product | typeof Type | typeof Category | typeof ProductCategory | typeof Tag | typeof ProductTag | typeof Shop | typeof Review | typeof User | typeof Analytics | typeof Wishlist | typeof Permission | typeof Profile | typeof Balance | typeof Manufacturer | typeof Shipping | typeof Tax | typeof Coupon | typeof Address | typeof Ai | typeof BecomeSeller | typeof Withdraw)[];
    autoLoadModels: boolean;
    synchronize: boolean;
    logging: boolean | {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    dialect: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    dialectOptions: {
        ssl: {
            require: boolean;
            rejectUnauthorized: boolean;
        };
    };
    models: (typeof Attribute | typeof Product | typeof Type | typeof Category | typeof ProductCategory | typeof Tag | typeof ProductTag | typeof Shop | typeof Review | typeof User | typeof Analytics | typeof Wishlist | typeof Permission | typeof Profile | typeof Balance | typeof Manufacturer | typeof Shipping | typeof Tax | typeof Coupon | typeof Address | typeof Ai | typeof BecomeSeller | typeof Withdraw)[];
    autoLoadModels: boolean;
    synchronize: boolean;
    logging: boolean | {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
}>;
export default _default;
