import { User, Permission } from '../users/entities/user.entity';
import { Profile } from '../users/entities/profile.entity';
import { Category, ProductCategory } from '../categories/entities/category.entity';
import { Product, ProductTag } from '../products/entities/product.entity';
import { Shop, Balance } from '../shops/entities/shop.entity';
import { Type } from '../types/entities/type.entity';
import { Tag } from '../tags/entities/tag.entity';
import { Manufacturer } from '../manufacturers/entities/manufacturer.entity';
import { Shipping } from '../shippings/entities/shipping.entity';
import { Tax } from '../taxes/entities/tax.entity';
import { Coupon } from '../coupons/entities/coupon.entity';
import { Review } from '../reviews/entities/review.entity';
import { Address } from '../addresses/entities/address.entity';
import { Analytics } from '../analytics/entities/analytics.entity';
import { Ai } from '../ai/entities/ai.entity';
declare const _default: (() => {
    dialect: string;
    storage: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    models: (typeof Tag | typeof Type | typeof Product | typeof ProductTag | typeof User | typeof Permission | typeof Profile | typeof Category | typeof ProductCategory | typeof Shop | typeof Balance | typeof Manufacturer | typeof Shipping | typeof Tax | typeof Coupon | typeof Review | typeof Address | typeof Analytics | typeof Ai)[];
    autoLoadModels: boolean;
    synchronize: boolean;
    logging: boolean | {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    dialect: string;
    storage: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    models: (typeof Tag | typeof Type | typeof Product | typeof ProductTag | typeof User | typeof Permission | typeof Profile | typeof Category | typeof ProductCategory | typeof Shop | typeof Balance | typeof Manufacturer | typeof Shipping | typeof Tax | typeof Coupon | typeof Review | typeof Address | typeof Analytics | typeof Ai)[];
    autoLoadModels: boolean;
    synchronize: boolean;
    logging: boolean | {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
}>;
export default _default;
