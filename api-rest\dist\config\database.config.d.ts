import { User, Permission } from '../users/entities/user.entity';
import { Profile } from '../users/entities/profile.entity';
import { Category, ProductCategory } from '../categories/entities/category.entity';
import { Product, ProductTag } from '../products/entities/product.entity';
import { Shop, Balance } from '../shops/entities/shop.entity';
import { Type } from '../types/entities/type.entity';
import { Tag } from '../tags/entities/tag.entity';
import { Manufacturer } from '../manufacturers/entities/manufacturer.entity';
import { Shipping } from '../shippings/entities/shipping.entity';
import { Tax } from '../taxes/entities/tax.entity';
import { Coupon } from '../coupons/entities/coupon.entity';
import { Review } from '../reviews/entities/review.entity';
import { Address } from '../addresses/entities/address.entity';
import { Analytics } from '../analytics/entities/analytics.entity';
import { TotalYearSaleByMonth } from '../analytics/entities/total-year-sale-by-month.entity';
import { CategoryWiseProduct } from '../analytics/entities/category-wise-product.entity';
import { TopRateProduct } from '../analytics/entities/top-rate-product.entity';
import { Ai } from '../ai/entities/ai.entity';
import { Attribute } from '../attributes/entities/attribute.entity';
import { AttributeValue } from '../attributes/entities/attribute-value.entity';
import { BecomeSeller } from '../become-seller/entities/become-seller.entity';
import { Wishlist } from '../wishlists/entities/wishlist.entity';
import { Withdraw } from '../withdraws/entities/withdraw.entity';
import { FlashSale } from '../flash-sale/entities/flash-sale.entity';
declare const _default: (() => {
    dialect: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    dialectOptions: {
        ssl: {
            require: boolean;
            rejectUnauthorized: boolean;
        };
    };
    models: (typeof Product | typeof Type | typeof Category | typeof ProductCategory | typeof Tag | typeof ProductTag | typeof Shop | typeof Review | typeof FlashSale | typeof User | typeof Permission | typeof Profile | typeof Balance | typeof Manufacturer | typeof Shipping | typeof Tax | typeof Coupon | typeof Address | typeof Analytics | typeof TotalYearSaleByMonth | typeof CategoryWiseProduct | typeof TopRateProduct | typeof Ai | typeof Attribute | typeof AttributeValue | typeof BecomeSeller | typeof Wishlist | typeof Withdraw)[];
    autoLoadModels: boolean;
    synchronize: boolean;
    logging: boolean | {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    dialect: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    dialectOptions: {
        ssl: {
            require: boolean;
            rejectUnauthorized: boolean;
        };
    };
    models: (typeof Product | typeof Type | typeof Category | typeof ProductCategory | typeof Tag | typeof ProductTag | typeof Shop | typeof Review | typeof FlashSale | typeof User | typeof Permission | typeof Profile | typeof Balance | typeof Manufacturer | typeof Shipping | typeof Tax | typeof Coupon | typeof Address | typeof Analytics | typeof TotalYearSaleByMonth | typeof CategoryWiseProduct | typeof TopRateProduct | typeof Ai | typeof Attribute | typeof AttributeValue | typeof BecomeSeller | typeof Wishlist | typeof Withdraw)[];
    autoLoadModels: boolean;
    synchronize: boolean;
    logging: boolean | {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
}>;
export default _default;
