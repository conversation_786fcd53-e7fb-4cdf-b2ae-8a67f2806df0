"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-use";
exports.ids = ["vendor-chunks/react-use"];
exports.modules = {

/***/ "./node_modules/react-use/esm/misc/util.js":
/*!*************************************************!*\
  !*** ./node_modules/react-use/esm/misc/util.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isNavigator: () => (/* binding */ isNavigator),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   off: () => (/* binding */ off),\n/* harmony export */   on: () => (/* binding */ on)\n/* harmony export */ });\nvar noop = function () { };\nfunction on(obj) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    if (obj && obj.addEventListener) {\n        obj.addEventListener.apply(obj, args);\n    }\n}\nfunction off(obj) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    if (obj && obj.removeEventListener) {\n        obj.removeEventListener.apply(obj, args);\n    }\n}\nvar isBrowser = typeof window !== 'undefined';\nvar isNavigator = typeof navigator !== 'undefined';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS9taXNjL3V0aWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBTztBQUNBO0FBQ1A7QUFDQSxxQkFBcUIsdUJBQXVCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxxQkFBcUIsdUJBQXVCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab25la2FydC9hZG1pbi1yZXN0Ly4vbm9kZV9tb2R1bGVzL3JlYWN0LXVzZS9lc20vbWlzYy91dGlsLmpzPzZiMmIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBub29wID0gZnVuY3Rpb24gKCkgeyB9O1xuZXhwb3J0IGZ1bmN0aW9uIG9uKG9iaikge1xuICAgIHZhciBhcmdzID0gW107XG4gICAgZm9yICh2YXIgX2kgPSAxOyBfaSA8IGFyZ3VtZW50cy5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgYXJnc1tfaSAtIDFdID0gYXJndW1lbnRzW19pXTtcbiAgICB9XG4gICAgaWYgKG9iaiAmJiBvYmouYWRkRXZlbnRMaXN0ZW5lcikge1xuICAgICAgICBvYmouYWRkRXZlbnRMaXN0ZW5lci5hcHBseShvYmosIGFyZ3MpO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBvZmYob2JqKSB7XG4gICAgdmFyIGFyZ3MgPSBbXTtcbiAgICBmb3IgKHZhciBfaSA9IDE7IF9pIDwgYXJndW1lbnRzLmxlbmd0aDsgX2krKykge1xuICAgICAgICBhcmdzW19pIC0gMV0gPSBhcmd1bWVudHNbX2ldO1xuICAgIH1cbiAgICBpZiAob2JqICYmIG9iai5yZW1vdmVFdmVudExpc3RlbmVyKSB7XG4gICAgICAgIG9iai5yZW1vdmVFdmVudExpc3RlbmVyLmFwcGx5KG9iaiwgYXJncyk7XG4gICAgfVxufVxuZXhwb3J0IHZhciBpc0Jyb3dzZXIgPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJztcbmV4cG9ydCB2YXIgaXNOYXZpZ2F0b3IgPSB0eXBlb2YgbmF2aWdhdG9yICE9PSAndW5kZWZpbmVkJztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/misc/util.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useCopyToClipboard.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-use/esm/useCopyToClipboard.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var copy_to_clipboard__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! copy-to-clipboard */ \"copy-to-clipboard\");\n/* harmony import */ var copy_to_clipboard__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(copy_to_clipboard__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _useMountedState__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useMountedState */ \"./node_modules/react-use/esm/useMountedState.js\");\n/* harmony import */ var _useSetState__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useSetState */ \"./node_modules/react-use/esm/useSetState.js\");\n\n\n\n\nvar useCopyToClipboard = function () {\n    var isMounted = (0,_useMountedState__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    var _a = (0,_useSetState__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n        value: undefined,\n        error: undefined,\n        noUserInteraction: true,\n    }), state = _a[0], setState = _a[1];\n    var copyToClipboard = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (value) {\n        if (!isMounted()) {\n            return;\n        }\n        var noUserInteraction;\n        var normalizedValue;\n        try {\n            // only strings and numbers casted to strings can be copied to clipboard\n            if (typeof value !== 'string' && typeof value !== 'number') {\n                var error = new Error(\"Cannot copy typeof \" + typeof value + \" to clipboard, must be a string\");\n                if (true)\n                    console.error(error);\n                setState({\n                    value: value,\n                    error: error,\n                    noUserInteraction: true,\n                });\n                return;\n            }\n            // empty strings are also considered invalid\n            else if (value === '') {\n                var error = new Error(\"Cannot copy empty string to clipboard.\");\n                if (true)\n                    console.error(error);\n                setState({\n                    value: value,\n                    error: error,\n                    noUserInteraction: true,\n                });\n                return;\n            }\n            normalizedValue = value.toString();\n            noUserInteraction = copy_to_clipboard__WEBPACK_IMPORTED_MODULE_0___default()(normalizedValue);\n            setState({\n                value: normalizedValue,\n                error: undefined,\n                noUserInteraction: noUserInteraction,\n            });\n        }\n        catch (error) {\n            setState({\n                value: normalizedValue,\n                error: error,\n                noUserInteraction: noUserInteraction,\n            });\n        }\n    }, []);\n    return [state, copyToClipboard];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useCopyToClipboard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useCopyToClipboard.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useEffectOnce.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-use/esm/useEffectOnce.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar useEffectOnce = function (effect) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(effect, []);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useEffectOnce);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VFZmZlY3RPbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrQztBQUNsQztBQUNBLElBQUksZ0RBQVM7QUFDYjtBQUNBLGlFQUFlLGFBQWEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BvbmVrYXJ0L2FkbWluLXJlc3QvLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VFZmZlY3RPbmNlLmpzPzFjMzYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xudmFyIHVzZUVmZmVjdE9uY2UgPSBmdW5jdGlvbiAoZWZmZWN0KSB7XG4gICAgdXNlRWZmZWN0KGVmZmVjdCwgW10pO1xufTtcbmV4cG9ydCBkZWZhdWx0IHVzZUVmZmVjdE9uY2U7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useEffectOnce.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useMountedState.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-use/esm/useMountedState.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useMountedState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useMountedState() {\n    var mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var get = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () { return mountedRef.current; }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        mountedRef.current = true;\n        return function () {\n            mountedRef.current = false;\n        };\n    }, []);\n    return get;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VNb3VudGVkU3RhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVEO0FBQ3hDO0FBQ2YscUJBQXFCLDZDQUFNO0FBQzNCLGNBQWMsa0RBQVcsZUFBZSw0QkFBNEI7QUFDcEUsSUFBSSxnREFBUztBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab25la2FydC9hZG1pbi1yZXN0Ly4vbm9kZV9tb2R1bGVzL3JlYWN0LXVzZS9lc20vdXNlTW91bnRlZFN0YXRlLmpzP2YyYmQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlTW91bnRlZFN0YXRlKCkge1xuICAgIHZhciBtb3VudGVkUmVmID0gdXNlUmVmKGZhbHNlKTtcbiAgICB2YXIgZ2V0ID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKCkgeyByZXR1cm4gbW91bnRlZFJlZi5jdXJyZW50OyB9LCBbXSk7XG4gICAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgbW91bnRlZFJlZi5jdXJyZW50ID0gdHJ1ZTtcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIG1vdW50ZWRSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgICAgICB9O1xuICAgIH0sIFtdKTtcbiAgICByZXR1cm4gZ2V0O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useMountedState.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useRafState.js":
/*!***************************************************!*\
  !*** ./node_modules/react-use/esm/useRafState.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useUnmount */ \"./node_modules/react-use/esm/useUnmount.js\");\n\n\nvar useRafState = function (initialState) {\n    var frame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState), state = _a[0], setState = _a[1];\n    var setRafState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (value) {\n        cancelAnimationFrame(frame.current);\n        frame.current = requestAnimationFrame(function () {\n            setState(value);\n        });\n    }, []);\n    (0,_useUnmount__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function () {\n        cancelAnimationFrame(frame.current);\n    });\n    return [state, setRafState];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useRafState);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VSYWZTdGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXNEO0FBQ2hCO0FBQ3RDO0FBQ0EsZ0JBQWdCLDZDQUFNO0FBQ3RCLGFBQWEsK0NBQVE7QUFDckIsc0JBQXNCLGtEQUFXO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0wsSUFBSSx1REFBVTtBQUNkO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxpRUFBZSxXQUFXLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab25la2FydC9hZG1pbi1yZXN0Ly4vbm9kZV9tb2R1bGVzL3JlYWN0LXVzZS9lc20vdXNlUmFmU3RhdGUuanM/MTMzOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDYWxsYmFjaywgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB1c2VVbm1vdW50IGZyb20gJy4vdXNlVW5tb3VudCc7XG52YXIgdXNlUmFmU3RhdGUgPSBmdW5jdGlvbiAoaW5pdGlhbFN0YXRlKSB7XG4gICAgdmFyIGZyYW1lID0gdXNlUmVmKDApO1xuICAgIHZhciBfYSA9IHVzZVN0YXRlKGluaXRpYWxTdGF0ZSksIHN0YXRlID0gX2FbMF0sIHNldFN0YXRlID0gX2FbMV07XG4gICAgdmFyIHNldFJhZlN0YXRlID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKHZhbHVlKSB7XG4gICAgICAgIGNhbmNlbEFuaW1hdGlvbkZyYW1lKGZyYW1lLmN1cnJlbnQpO1xuICAgICAgICBmcmFtZS5jdXJyZW50ID0gcmVxdWVzdEFuaW1hdGlvbkZyYW1lKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHNldFN0YXRlKHZhbHVlKTtcbiAgICAgICAgfSk7XG4gICAgfSwgW10pO1xuICAgIHVzZVVubW91bnQoZnVuY3Rpb24gKCkge1xuICAgICAgICBjYW5jZWxBbmltYXRpb25GcmFtZShmcmFtZS5jdXJyZW50KTtcbiAgICB9KTtcbiAgICByZXR1cm4gW3N0YXRlLCBzZXRSYWZTdGF0ZV07XG59O1xuZXhwb3J0IGRlZmF1bHQgdXNlUmFmU3RhdGU7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useRafState.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useSetState.js":
/*!***************************************************!*\
  !*** ./node_modules/react-use/esm/useSetState.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar useSetState = function (initialState) {\n    if (initialState === void 0) { initialState = {}; }\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState), state = _a[0], set = _a[1];\n    var setState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (patch) {\n        set(function (prevState) {\n            return Object.assign({}, prevState, patch instanceof Function ? patch(prevState) : patch);\n        });\n    }, []);\n    return [state, setState];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSetState);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VTZXRTdGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFDOUM7QUFDQSxtQ0FBbUM7QUFDbkMsYUFBYSwrQ0FBUTtBQUNyQixtQkFBbUIsa0RBQVc7QUFDOUI7QUFDQSxtQ0FBbUM7QUFDbkMsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0EsaUVBQWUsV0FBVyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9uZWthcnQvYWRtaW4tcmVzdC8uL25vZGVfbW9kdWxlcy9yZWFjdC11c2UvZXNtL3VzZVNldFN0YXRlLmpzPzdiOTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xudmFyIHVzZVNldFN0YXRlID0gZnVuY3Rpb24gKGluaXRpYWxTdGF0ZSkge1xuICAgIGlmIChpbml0aWFsU3RhdGUgPT09IHZvaWQgMCkgeyBpbml0aWFsU3RhdGUgPSB7fTsgfVxuICAgIHZhciBfYSA9IHVzZVN0YXRlKGluaXRpYWxTdGF0ZSksIHN0YXRlID0gX2FbMF0sIHNldCA9IF9hWzFdO1xuICAgIHZhciBzZXRTdGF0ZSA9IHVzZUNhbGxiYWNrKGZ1bmN0aW9uIChwYXRjaCkge1xuICAgICAgICBzZXQoZnVuY3Rpb24gKHByZXZTdGF0ZSkge1xuICAgICAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oe30sIHByZXZTdGF0ZSwgcGF0Y2ggaW5zdGFuY2VvZiBGdW5jdGlvbiA/IHBhdGNoKHByZXZTdGF0ZSkgOiBwYXRjaCk7XG4gICAgICAgIH0pO1xuICAgIH0sIFtdKTtcbiAgICByZXR1cm4gW3N0YXRlLCBzZXRTdGF0ZV07XG59O1xuZXhwb3J0IGRlZmF1bHQgdXNlU2V0U3RhdGU7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useSetState.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useUnmount.js":
/*!**************************************************!*\
  !*** ./node_modules/react-use/esm/useUnmount.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useEffectOnce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useEffectOnce */ \"./node_modules/react-use/esm/useEffectOnce.js\");\n\n\nvar useUnmount = function (fn) {\n    var fnRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fn);\n    // update the ref each render so if it change the newest callback will be invoked\n    fnRef.current = fn;\n    (0,_useEffectOnce__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function () { return function () { return fnRef.current(); }; });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useUnmount);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VVbm1vdW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0I7QUFDYTtBQUM1QztBQUNBLGdCQUFnQiw2Q0FBTTtBQUN0QjtBQUNBO0FBQ0EsSUFBSSwwREFBYSxlQUFlLHFCQUFxQiw0QkFBNEI7QUFDakY7QUFDQSxpRUFBZSxVQUFVLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab25la2FydC9hZG1pbi1yZXN0Ly4vbm9kZV9tb2R1bGVzL3JlYWN0LXVzZS9lc20vdXNlVW5tb3VudC5qcz81Njc0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB1c2VFZmZlY3RPbmNlIGZyb20gJy4vdXNlRWZmZWN0T25jZSc7XG52YXIgdXNlVW5tb3VudCA9IGZ1bmN0aW9uIChmbikge1xuICAgIHZhciBmblJlZiA9IHVzZVJlZihmbik7XG4gICAgLy8gdXBkYXRlIHRoZSByZWYgZWFjaCByZW5kZXIgc28gaWYgaXQgY2hhbmdlIHRoZSBuZXdlc3QgY2FsbGJhY2sgd2lsbCBiZSBpbnZva2VkXG4gICAgZm5SZWYuY3VycmVudCA9IGZuO1xuICAgIHVzZUVmZmVjdE9uY2UoZnVuY3Rpb24gKCkgeyByZXR1cm4gZnVuY3Rpb24gKCkgeyByZXR1cm4gZm5SZWYuY3VycmVudCgpOyB9OyB9KTtcbn07XG5leHBvcnQgZGVmYXVsdCB1c2VVbm1vdW50O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useUnmount.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useWindowSize.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-use/esm/useWindowSize.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useRafState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useRafState */ \"./node_modules/react-use/esm/useRafState.js\");\n/* harmony import */ var _misc_util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./misc/util */ \"./node_modules/react-use/esm/misc/util.js\");\n\n\n\nvar useWindowSize = function (initialWidth, initialHeight) {\n    if (initialWidth === void 0) { initialWidth = Infinity; }\n    if (initialHeight === void 0) { initialHeight = Infinity; }\n    var _a = (0,_useRafState__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        width: _misc_util__WEBPACK_IMPORTED_MODULE_2__.isBrowser ? window.innerWidth : initialWidth,\n        height: _misc_util__WEBPACK_IMPORTED_MODULE_2__.isBrowser ? window.innerHeight : initialHeight,\n    }), state = _a[0], setState = _a[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        if (_misc_util__WEBPACK_IMPORTED_MODULE_2__.isBrowser) {\n            var handler_1 = function () {\n                setState({\n                    width: window.innerWidth,\n                    height: window.innerHeight,\n                });\n            };\n            (0,_misc_util__WEBPACK_IMPORTED_MODULE_2__.on)(window, 'resize', handler_1);\n            return function () {\n                (0,_misc_util__WEBPACK_IMPORTED_MODULE_2__.off)(window, 'resize', handler_1);\n            };\n        }\n    }, []);\n    return state;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useWindowSize);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useWindowSize.js\n");

/***/ })

};
;