const http = require('http');

// Test another low stock product creation
const productData = JSON.stringify({
  name: 'Critical Stock Item',
  description: 'Another product with critically low stock',
  slug: 'critical-stock-item',
  type_id: 1,
  shop_id: 3, // Ridwan's shop ID
  product_type: 'simple',
  status: 'publish',
  visibility: 'public',
  price: 29.99,
  sale_price: 24.99,
  sku: 'CRITICAL-001',
  quantity: 2, // Very low stock quantity
  in_stock: true,
  is_taxable: true,
  shipping_class_id: null,
  categories: [1],
  tags: [],
  image: {
    id: 'critical-stock-image-id',
    original: 'https://example.com/critical-stock-image.jpg',
    thumbnail: 'https://example.com/critical-stock-image-thumb.jpg'
  },
  gallery: [],
  variations: [],
  variation_options: []
});

const options = {
  hostname: 'localhost',
  port: 9000,
  path: '/api/products',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': productData.length
  }
};

console.log('Creating another low stock product...');

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });
  
  res.on('end', () => {
    if (res.statusCode >= 400) {
      console.log('❌ Failed to create product');
    } else {
      console.log('✅ Critical stock item created successfully!');
      console.log('Product ID:', JSON.parse(body).id);
    }
  });
});

req.on('error', (e) => {
  console.error(`❌ Request error: ${e.message}`);
});

req.write(productData);
req.end();
