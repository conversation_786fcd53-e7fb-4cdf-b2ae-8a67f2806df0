"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OwnershipTransferModule = void 0;
const common_1 = require("@nestjs/common");
const ownership_transfer_controller_1 = require("./ownership-transfer.controller");
const ownership_transfer_service_1 = require("./ownership-transfer.service");
let OwnershipTransferModule = class OwnershipTransferModule {
};
OwnershipTransferModule = __decorate([
    (0, common_1.Module)({
        controllers: [ownership_transfer_controller_1.OwnershipTransferController],
        providers: [ownership_transfer_service_1.OwnershipTransferService],
    })
], OwnershipTransferModule);
exports.OwnershipTransferModule = OwnershipTransferModule;
//# sourceMappingURL=ownership-transfer.module.js.map