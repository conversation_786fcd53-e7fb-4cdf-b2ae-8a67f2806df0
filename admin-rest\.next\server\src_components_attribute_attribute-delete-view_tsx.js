"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_attribute_attribute-delete-view_tsx";
exports.ids = ["src_components_attribute_attribute-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/attribute/attribute-delete-view.tsx":
/*!************************************************************!*\
  !*** ./src/components/attribute/attribute-delete-view.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_attributes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/attributes */ \"./src/data/attributes.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_attributes__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_attributes__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst AttributeDeleteView = ()=>{\n    const { mutate: deleteAttributeByID, isLoading: loading } = (0,_data_attributes__WEBPACK_IMPORTED_MODULE_3__.useDeleteAttributeMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    async function handleDelete() {\n        deleteAttributeByID({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\attribute\\\\attribute-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AttributeDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/attribute/attribute-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/attributes.ts":
/*!********************************!*\
  !*** ./src/data/attributes.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAttributeQuery: () => (/* binding */ useAttributeQuery),\n/* harmony export */   useAttributesQuery: () => (/* binding */ useAttributesQuery),\n/* harmony export */   useCreateAttributeMutation: () => (/* binding */ useCreateAttributeMutation),\n/* harmony export */   useDeleteAttributeMutation: () => (/* binding */ useDeleteAttributeMutation),\n/* harmony export */   useUpdateAttributeMutation: () => (/* binding */ useUpdateAttributeMutation)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _data_client_attribute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/client/attribute */ \"./src/data/client/attribute.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _data_client_attribute__WEBPACK_IMPORTED_MODULE_6__, _config__WEBPACK_IMPORTED_MODULE_7__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _data_client_attribute__WEBPACK_IMPORTED_MODULE_6__, _config__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst useCreateAttributeMutation = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_attribute__WEBPACK_IMPORTED_MODULE_6__.attributeClient.create, {\n        onSuccess: ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.attribute.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.attribute.list;\n            next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ATTRIBUTES);\n        }\n    });\n};\nconst useUpdateAttributeMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_attribute__WEBPACK_IMPORTED_MODULE_6__.attributeClient.update, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ATTRIBUTES);\n        }\n    });\n};\nconst useDeleteAttributeMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_attribute__WEBPACK_IMPORTED_MODULE_6__.attributeClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ATTRIBUTES);\n        }\n    });\n};\nconst useAttributeQuery = ({ slug, language })=>{\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ATTRIBUTES,\n        {\n            slug,\n            language\n        }\n    ], ()=>_data_client_attribute__WEBPACK_IMPORTED_MODULE_6__.attributeClient.get({\n            slug,\n            language\n        }));\n};\nconst useAttributesQuery = (params, options = {})=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ATTRIBUTES,\n        params\n    ], ({ queryKey, pageParam })=>_data_client_attribute__WEBPACK_IMPORTED_MODULE_6__.attributeClient.all(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true,\n        ...options\n    });\n    return {\n        attributes: data ?? [],\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/attributes.ts\n");

/***/ }),

/***/ "./src/data/client/attribute.ts":
/*!**************************************!*\
  !*** ./src/data/client/attribute.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attributeClient: () => (/* binding */ attributeClient)\n/* harmony export */ });\n/* harmony import */ var _data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/client/curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst attributeClient = {\n    ...(0,_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__.crudFactory)(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ATTRIBUTES),\n    paginated: ({ type, name, shop_id, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ATTRIBUTES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                name,\n                shop_id\n            })\n        });\n    },\n    all: ({ type, name, shop_id, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ATTRIBUTES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                name,\n                shop_id\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/attribute.ts\n");

/***/ })

};
;