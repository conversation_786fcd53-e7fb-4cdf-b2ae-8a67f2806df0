import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { AbusiveReportsController } from './reports.controller';
import { AbusiveReportService } from './reports.service';
import { ReviewController } from './reviews.controller';
import { ReviewService } from './reviews.service';
import { Review } from './entities/review.entity';

@Module({
  imports: [SequelizeModule.forFeature([Review])],
  controllers: [ReviewController, AbusiveReportsController],
  providers: [ReviewService, AbusiveReportService],
  exports: [ReviewService, AbusiveReportService],
})
export class ReviewModule {}
