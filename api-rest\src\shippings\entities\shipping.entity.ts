import { Column, Model, Table, DataType } from 'sequelize-typescript';

@Table({
  tableName: 'shippings',
  timestamps: true,
})
export class Shipping extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  amount: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  is_global: boolean;

  @Column({
    type: DataType.ENUM('fixed', 'percentage', 'free'),
    allowNull: false,
    defaultValue: 'fixed',
  })
  type: ShippingType;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}

export enum ShippingType {
  FIXED = 'fixed',
  PERCENTAGE = 'percentage',
  FREE = 'free',
}
