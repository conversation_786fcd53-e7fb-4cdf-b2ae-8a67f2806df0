"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_layouts_mobile-menu_mobile-main-menu_tsx";
exports.ids = ["src_components_layouts_mobile-menu_mobile-main-menu_tsx"];
exports.modules = {

/***/ "./src/components/layouts/mobile-menu/mobile-main-menu.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/layouts/mobile-menu/mobile-main-menu.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileMainMenu)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/drawer/drawer-wrapper */ \"./src/components/ui/drawer/drawer-wrapper.tsx\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jotai */ \"jotai\");\n/* harmony import */ var _store_drawer_atom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/drawer-atom */ \"./src/store/drawer-atom.ts\");\n/* harmony import */ var _config_site__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/config/site */ \"./src/config/site.ts\");\n/* harmony import */ var _components_ui_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/link */ \"./src/components/ui/link.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_4__, _store_drawer_atom__WEBPACK_IMPORTED_MODULE_5__]);\n([_components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_4__, _store_drawer_atom__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction MobileMainMenu() {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [_, closeSidebar] = (0,jotai__WEBPACK_IMPORTED_MODULE_4__.useAtom)(_store_drawer_atom__WEBPACK_IMPORTED_MODULE_5__.drawerAtom);\n    const { headerLinks } = _config_site__WEBPACK_IMPORTED_MODULE_6__.siteSettings;\n    // function handleClick(path: string) {\n    //   router.push(path);\n    //   closeSidebar({ display: false, view: '' });\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            className: \"grow\",\n            children: headerLinks?.map(({ href, label })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        href: href,\n                        className: \"flex items-center px-5 py-3 text-sm font-semibold capitalize transition duration-200 cursor-pointer text-heading hover:text-accent md:px-6\",\n                        title: t(label),\n                        onClick: ()=>closeSidebar({\n                                display: false,\n                                view: \"\"\n                            }),\n                        children: t(label)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, this)\n                }, `${href}${label}`, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layouts/mobile-menu/mobile-main-menu.tsx\n");

/***/ }),

/***/ "./src/config/site.ts":
/*!****************************!*\
  !*** ./src/config/site.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siteSettings: () => (/* binding */ siteSettings)\n/* harmony export */ });\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"./src/types/index.ts\");\n\n\nconst siteSettings = {\n    name: \"oneKart\",\n    description: \"\",\n    logo: {\n        url: \"/logo.svg\",\n        alt: \"oneKart\",\n        href: \"/grocery\",\n        width: 128,\n        height: 40\n    },\n    defaultLanguage: \"en\",\n    currencyCode: \"USD\",\n    product: {\n        placeholderImage: \"/product-placeholder.svg\",\n        cardMaps: {\n            grocery: \"Krypton\",\n            furniture: \"Radon\",\n            bag: \"Oganesson\",\n            makeup: \"Neon\",\n            book: \"Xenon\",\n            medicine: \"Helium\",\n            default: \"Argon\"\n        }\n    },\n    authorizedLinks: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"auth-menu-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"auth-menu-my-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.checkout,\n            label: \"auth-menu-checkout\"\n        }\n    ],\n    authorizedLinksMobile: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"auth-menu-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.notifyLogs,\n            label: \"profile-sidebar-notifications\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"auth-menu-my-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.cards,\n            label: \"profile-sidebar-my-cards\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.questions,\n            label: \"profile-sidebar-my-questions\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.refunds,\n            label: \"text-my-refunds\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.reports,\n            label: \"profile-sidebar-my-reports\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.checkout,\n            label: \"auth-menu-checkout\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.changePassword,\n            label: \"profile-sidebar-password\"\n        }\n    ],\n    dashboardSidebarMenu: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"profile-sidebar-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.changePassword,\n            label: \"profile-sidebar-password\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.notifyLogs,\n            label: \"profile-sidebar-notifications\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.cards,\n            label: \"profile-sidebar-my-cards\",\n            // MultiPayment: Make it dynamic or from mapper\n            cardsPayment: [\n                _types__WEBPACK_IMPORTED_MODULE_1__.PaymentGateway.STRIPE\n            ]\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"profile-sidebar-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.downloads,\n            label: \"profile-sidebar-downloads\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.questions,\n            label: \"profile-sidebar-my-questions\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.refunds,\n            label: \"text-my-refunds\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.reports,\n            label: \"profile-sidebar-my-reports\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help,\n            label: \"profile-sidebar-help\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.logout,\n            label: \"profile-sidebar-logout\"\n        }\n    ],\n    sellingAdvertisement: {\n        image: {\n            src: \"/selling.png\",\n            alt: \"Selling Advertisement\"\n        }\n    },\n    cta: {\n        mockup_img_src: \"/mockup-img.png\",\n        play_store_link: \"/\",\n        app_store_link: \"/\"\n    },\n    headerLinks: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shops,\n            icon: null,\n            label: \"nav-menu-shops\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.coupons,\n            icon: null,\n            label: \"nav-menu-offer\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.contactUs,\n            label: \"nav-menu-contact\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.becomeSeller,\n            label: \"Become a seller\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.flashSale,\n            label: \"nav-menu-flash-sale\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.manufacturers,\n            label: \"text-manufacturers\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.authors,\n            label: \"text-authors\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help,\n            label: \"nav-menu-faq\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.terms,\n            label: \"nav-menu-terms\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.customerRefundPolicies,\n            label: \"nav-menu-refund-policy\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.vendorRefundPolicies,\n            label: \"nav-menu-vendor-refund-policy\"\n        }\n    ],\n    footer: {\n        // copyright: {\n        //   name: 'RedQ, Inc',\n        //   href: 'https://redq.io/',\n        // },\n        // address: '2429 River Drive, Suite 35 Cottonhall, CA 2296 United Kingdom',\n        // email: '<EMAIL>',\n        // phone: '******-698-0694',\n        menus: [\n            {\n                title: \"text-explore\",\n                links: [\n                    {\n                        name: \"Shops\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shops\n                    },\n                    {\n                        name: \"Authors\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.authors\n                    },\n                    {\n                        name: \"Flash Deals\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes?.flashSale\n                    },\n                    {\n                        name: \"Coupon\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.coupons\n                    }\n                ]\n            },\n            {\n                title: \"text-customer-service\",\n                links: [\n                    {\n                        name: \"text-faq-help\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help\n                    },\n                    {\n                        name: \"Vendor Refund Policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.vendorRefundPolicies\n                    },\n                    {\n                        name: \"Customer Refund Policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.customerRefundPolicies\n                    }\n                ]\n            },\n            {\n                title: \"text-our-information\",\n                links: [\n                    {\n                        name: \"Manufacturers\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes?.manufacturers\n                    },\n                    {\n                        name: \"Privacy policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.privacy\n                    },\n                    {\n                        name: \"text-terms-condition\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.terms\n                    },\n                    {\n                        name: \"text-contact-us\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.contactUs\n                    }\n                ]\n            }\n        ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29uZmlnL3NpdGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXlDO0FBQ0E7QUFFbEMsTUFBTUUsZUFBZTtJQUMxQkMsTUFBTTtJQUNOQyxhQUFhO0lBQ2JDLE1BQU07UUFDSkMsS0FBSztRQUNMQyxLQUFLO1FBQ0xDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxRQUFRO0lBQ1Y7SUFDQUMsaUJBQWlCO0lBQ2pCQyxjQUFjO0lBQ2RDLFNBQVM7UUFDUEMsa0JBQWtCO1FBQ2xCQyxVQUFVO1lBQ1JDLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxLQUFLO1lBQ0xDLFFBQVE7WUFDUkMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLFNBQVM7UUFDWDtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmO1lBQUVmLE1BQU1SLGtEQUFNQSxDQUFDd0IsT0FBTztZQUFFQyxPQUFPO1FBQW9CO1FBQ25EO1lBQUVqQixNQUFNUixrREFBTUEsQ0FBQzBCLE1BQU07WUFBRUQsT0FBTztRQUFzQjtRQUNwRDtZQUFFakIsTUFBTVIsa0RBQU1BLENBQUMyQixTQUFTO1lBQUVGLE9BQU87UUFBOEI7UUFDL0Q7WUFBRWpCLE1BQU1SLGtEQUFNQSxDQUFDNEIsUUFBUTtZQUFFSCxPQUFPO1FBQXFCO0tBQ3REO0lBQ0RJLHVCQUF1QjtRQUNyQjtZQUFFckIsTUFBTVIsa0RBQU1BLENBQUN3QixPQUFPO1lBQUVDLE9BQU87UUFBb0I7UUFDbkQ7WUFBRWpCLE1BQU1SLGtEQUFNQSxDQUFDOEIsVUFBVTtZQUFFTCxPQUFPO1FBQWdDO1FBQ2xFO1lBQUVqQixNQUFNUixrREFBTUEsQ0FBQzBCLE1BQU07WUFBRUQsT0FBTztRQUFzQjtRQUNwRDtZQUFFakIsTUFBTVIsa0RBQU1BLENBQUMrQixLQUFLO1lBQUVOLE9BQU87UUFBMkI7UUFDeEQ7WUFBRWpCLE1BQU1SLGtEQUFNQSxDQUFDMkIsU0FBUztZQUFFRixPQUFPO1FBQThCO1FBQy9EO1lBQUVqQixNQUFNUixrREFBTUEsQ0FBQ2dDLFNBQVM7WUFBRVAsT0FBTztRQUErQjtRQUNoRTtZQUFFakIsTUFBTVIsa0RBQU1BLENBQUNpQyxPQUFPO1lBQUVSLE9BQU87UUFBa0I7UUFDakQ7WUFBRWpCLE1BQU1SLGtEQUFNQSxDQUFDa0MsT0FBTztZQUFFVCxPQUFPO1FBQTZCO1FBQzVEO1lBQUVqQixNQUFNUixrREFBTUEsQ0FBQzRCLFFBQVE7WUFBRUgsT0FBTztRQUFxQjtRQUNyRDtZQUFFakIsTUFBTVIsa0RBQU1BLENBQUNtQyxjQUFjO1lBQUVWLE9BQU87UUFBMkI7S0FDbEU7SUFDRFcsc0JBQXNCO1FBQ3BCO1lBQ0U1QixNQUFNUixrREFBTUEsQ0FBQ3dCLE9BQU87WUFDcEJDLE9BQU87UUFDVDtRQUNBO1lBQ0VqQixNQUFNUixrREFBTUEsQ0FBQ21DLGNBQWM7WUFDM0JWLE9BQU87UUFDVDtRQUNBO1lBQ0VqQixNQUFNUixrREFBTUEsQ0FBQzhCLFVBQVU7WUFDdkJMLE9BQU87UUFDVDtRQUNBO1lBQ0VqQixNQUFNUixrREFBTUEsQ0FBQytCLEtBQUs7WUFDbEJOLE9BQU87WUFDUCwrQ0FBK0M7WUFDL0NZLGNBQWM7Z0JBQUNwQyxrREFBY0EsQ0FBQ3FDLE1BQU07YUFBQztRQUN2QztRQUNBO1lBQ0U5QixNQUFNUixrREFBTUEsQ0FBQzBCLE1BQU07WUFDbkJELE9BQU87UUFDVDtRQUNBO1lBQ0VqQixNQUFNUixrREFBTUEsQ0FBQ3VDLFNBQVM7WUFDdEJkLE9BQU87UUFDVDtRQUNBO1lBQ0VqQixNQUFNUixrREFBTUEsQ0FBQzJCLFNBQVM7WUFDdEJGLE9BQU87UUFDVDtRQUNBO1lBQ0VqQixNQUFNUixrREFBTUEsQ0FBQ2dDLFNBQVM7WUFDdEJQLE9BQU87UUFDVDtRQUNBO1lBQ0VqQixNQUFNUixrREFBTUEsQ0FBQ2lDLE9BQU87WUFDcEJSLE9BQU87UUFDVDtRQUNBO1lBQ0VqQixNQUFNUixrREFBTUEsQ0FBQ2tDLE9BQU87WUFDcEJULE9BQU87UUFDVDtRQUNBO1lBQ0VqQixNQUFNUixrREFBTUEsQ0FBQ3dDLElBQUk7WUFDakJmLE9BQU87UUFDVDtRQUNBO1lBQ0VqQixNQUFNUixrREFBTUEsQ0FBQ3lDLE1BQU07WUFDbkJoQixPQUFPO1FBQ1Q7S0FDRDtJQUNEaUIsc0JBQXNCO1FBQ3BCQyxPQUFPO1lBQ0xDLEtBQUs7WUFDTHJDLEtBQUs7UUFDUDtJQUNGO0lBQ0FzQyxLQUFLO1FBQ0hDLGdCQUFnQjtRQUNoQkMsaUJBQWlCO1FBQ2pCQyxnQkFBZ0I7SUFDbEI7SUFDQUMsYUFBYTtRQUNYO1lBQUV6QyxNQUFNUixrREFBTUEsQ0FBQ2tELEtBQUs7WUFBRUMsTUFBTTtZQUFNMUIsT0FBTztRQUFpQjtRQUMxRDtZQUFFakIsTUFBTVIsa0RBQU1BLENBQUNvRCxPQUFPO1lBQUVELE1BQU07WUFBTTFCLE9BQU87UUFBaUI7UUFDNUQ7WUFBRWpCLE1BQU1SLGtEQUFNQSxDQUFDcUQsU0FBUztZQUFFNUIsT0FBTztRQUFtQjtRQUNwRDtZQUNFakIsTUFBTVIsa0RBQU1BLENBQUNzRCxZQUFZO1lBQ3pCN0IsT0FBTztRQUNUO1FBQ0E7WUFBRWpCLE1BQU1SLGtEQUFNQSxDQUFDdUQsU0FBUztZQUFFOUIsT0FBTztRQUFzQjtRQUN2RDtZQUFFakIsTUFBTVIsa0RBQU1BLENBQUN3RCxhQUFhO1lBQUUvQixPQUFPO1FBQXFCO1FBQzFEO1lBQUVqQixNQUFNUixrREFBTUEsQ0FBQ3lELE9BQU87WUFBRWhDLE9BQU87UUFBZTtRQUM5QztZQUFFakIsTUFBTVIsa0RBQU1BLENBQUN3QyxJQUFJO1lBQUVmLE9BQU87UUFBZTtRQUMzQztZQUFFakIsTUFBTVIsa0RBQU1BLENBQUMwRCxLQUFLO1lBQUVqQyxPQUFPO1FBQWlCO1FBQzlDO1lBQUVqQixNQUFNUixrREFBTUEsQ0FBQzJELHNCQUFzQjtZQUFFbEMsT0FBTztRQUF5QjtRQUN2RTtZQUNFakIsTUFBTVIsa0RBQU1BLENBQUM0RCxvQkFBb0I7WUFDakNuQyxPQUFPO1FBQ1Q7S0FDRDtJQUNEb0MsUUFBUTtRQUNOLGVBQWU7UUFDZix1QkFBdUI7UUFDdkIsOEJBQThCO1FBQzlCLEtBQUs7UUFDTCw0RUFBNEU7UUFDNUUsNEJBQTRCO1FBQzVCLDRCQUE0QjtRQUM1QkMsT0FBTztZQUNMO2dCQUNFQyxPQUFPO2dCQUNQQyxPQUFPO29CQUNMO3dCQUNFN0QsTUFBTTt3QkFDTkssTUFBTVIsa0RBQU1BLENBQUNrRCxLQUFLO29CQUNwQjtvQkFDQTt3QkFDRS9DLE1BQU07d0JBQ05LLE1BQU1SLGtEQUFNQSxDQUFDeUQsT0FBTztvQkFDdEI7b0JBQ0E7d0JBQ0V0RCxNQUFNO3dCQUNOSyxNQUFNUixrREFBTUEsRUFBRXVEO29CQUNoQjtvQkFDQTt3QkFDRXBELE1BQU07d0JBQ05LLE1BQU1SLGtEQUFNQSxDQUFDb0QsT0FBTztvQkFDdEI7aUJBQ0Q7WUFDSDtZQUNBO2dCQUNFVyxPQUFPO2dCQUNQQyxPQUFPO29CQUNMO3dCQUNFN0QsTUFBTTt3QkFDTkssTUFBTVIsa0RBQU1BLENBQUN3QyxJQUFJO29CQUNuQjtvQkFDQTt3QkFDRXJDLE1BQU07d0JBQ05LLE1BQU1SLGtEQUFNQSxDQUFDNEQsb0JBQW9CO29CQUNuQztvQkFDQTt3QkFDRXpELE1BQU07d0JBQ05LLE1BQU1SLGtEQUFNQSxDQUFDMkQsc0JBQXNCO29CQUNyQztpQkFDRDtZQUNIO1lBQ0E7Z0JBQ0VJLE9BQU87Z0JBQ1BDLE9BQU87b0JBQ0w7d0JBQ0U3RCxNQUFNO3dCQUNOSyxNQUFNUixrREFBTUEsRUFBRXdEO29CQUNoQjtvQkFDQTt3QkFDRXJELE1BQU07d0JBQ05LLE1BQU1SLGtEQUFNQSxDQUFDaUUsT0FBTztvQkFDdEI7b0JBQ0E7d0JBQ0U5RCxNQUFNO3dCQUNOSyxNQUFNUixrREFBTUEsQ0FBQzBELEtBQUs7b0JBQ3BCO29CQUNBO3dCQUNFdkQsTUFBTTt3QkFDTkssTUFBTVIsa0RBQU1BLENBQUNxRCxTQUFTO29CQUN4QjtpQkFDRDtZQUNIO1NBQ0Q7SUF1Qkg7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9uZWthcnQvc2hvcC8uL3NyYy9jb25maWcvc2l0ZS50cz9iZWRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJvdXRlcyB9IGZyb20gJ0AvY29uZmlnL3JvdXRlcyc7XG5pbXBvcnQgeyBQYXltZW50R2F0ZXdheSB9IGZyb20gJ0AvdHlwZXMnO1xuXG5leHBvcnQgY29uc3Qgc2l0ZVNldHRpbmdzID0ge1xuICBuYW1lOiAnb25lS2FydCcsXG4gIGRlc2NyaXB0aW9uOiAnJyxcbiAgbG9nbzoge1xuICAgIHVybDogJy9sb2dvLnN2ZycsXG4gICAgYWx0OiAnb25lS2FydCcsXG4gICAgaHJlZjogJy9ncm9jZXJ5JyxcbiAgICB3aWR0aDogMTI4LFxuICAgIGhlaWdodDogNDAsXG4gIH0sXG4gIGRlZmF1bHRMYW5ndWFnZTogJ2VuJyxcbiAgY3VycmVuY3lDb2RlOiAnVVNEJyxcbiAgcHJvZHVjdDoge1xuICAgIHBsYWNlaG9sZGVySW1hZ2U6ICcvcHJvZHVjdC1wbGFjZWhvbGRlci5zdmcnLFxuICAgIGNhcmRNYXBzOiB7XG4gICAgICBncm9jZXJ5OiAnS3J5cHRvbicsXG4gICAgICBmdXJuaXR1cmU6ICdSYWRvbicsXG4gICAgICBiYWc6ICdPZ2FuZXNzb24nLFxuICAgICAgbWFrZXVwOiAnTmVvbicsXG4gICAgICBib29rOiAnWGVub24nLFxuICAgICAgbWVkaWNpbmU6ICdIZWxpdW0nLFxuICAgICAgZGVmYXVsdDogJ0FyZ29uJyxcbiAgICB9LFxuICB9LFxuICBhdXRob3JpemVkTGlua3M6IFtcbiAgICB7IGhyZWY6IFJvdXRlcy5wcm9maWxlLCBsYWJlbDogJ2F1dGgtbWVudS1wcm9maWxlJyB9LFxuICAgIHsgaHJlZjogUm91dGVzLm9yZGVycywgbGFiZWw6ICdhdXRoLW1lbnUtbXktb3JkZXJzJyB9LFxuICAgIHsgaHJlZjogUm91dGVzLndpc2hsaXN0cywgbGFiZWw6ICdwcm9maWxlLXNpZGViYXItbXktd2lzaGxpc3QnIH0sXG4gICAgeyBocmVmOiBSb3V0ZXMuY2hlY2tvdXQsIGxhYmVsOiAnYXV0aC1tZW51LWNoZWNrb3V0JyB9LFxuICBdLFxuICBhdXRob3JpemVkTGlua3NNb2JpbGU6IFtcbiAgICB7IGhyZWY6IFJvdXRlcy5wcm9maWxlLCBsYWJlbDogJ2F1dGgtbWVudS1wcm9maWxlJyB9LFxuICAgIHsgaHJlZjogUm91dGVzLm5vdGlmeUxvZ3MsIGxhYmVsOiAncHJvZmlsZS1zaWRlYmFyLW5vdGlmaWNhdGlvbnMnIH0sXG4gICAgeyBocmVmOiBSb3V0ZXMub3JkZXJzLCBsYWJlbDogJ2F1dGgtbWVudS1teS1vcmRlcnMnIH0sXG4gICAgeyBocmVmOiBSb3V0ZXMuY2FyZHMsIGxhYmVsOiAncHJvZmlsZS1zaWRlYmFyLW15LWNhcmRzJyB9LFxuICAgIHsgaHJlZjogUm91dGVzLndpc2hsaXN0cywgbGFiZWw6ICdwcm9maWxlLXNpZGViYXItbXktd2lzaGxpc3QnIH0sXG4gICAgeyBocmVmOiBSb3V0ZXMucXVlc3Rpb25zLCBsYWJlbDogJ3Byb2ZpbGUtc2lkZWJhci1teS1xdWVzdGlvbnMnIH0sXG4gICAgeyBocmVmOiBSb3V0ZXMucmVmdW5kcywgbGFiZWw6ICd0ZXh0LW15LXJlZnVuZHMnIH0sXG4gICAgeyBocmVmOiBSb3V0ZXMucmVwb3J0cywgbGFiZWw6ICdwcm9maWxlLXNpZGViYXItbXktcmVwb3J0cycgfSxcbiAgICB7IGhyZWY6IFJvdXRlcy5jaGVja291dCwgbGFiZWw6ICdhdXRoLW1lbnUtY2hlY2tvdXQnIH0sXG4gICAgeyBocmVmOiBSb3V0ZXMuY2hhbmdlUGFzc3dvcmQsIGxhYmVsOiAncHJvZmlsZS1zaWRlYmFyLXBhc3N3b3JkJyB9LFxuICBdLFxuICBkYXNoYm9hcmRTaWRlYmFyTWVudTogW1xuICAgIHtcbiAgICAgIGhyZWY6IFJvdXRlcy5wcm9maWxlLFxuICAgICAgbGFiZWw6ICdwcm9maWxlLXNpZGViYXItcHJvZmlsZScsXG4gICAgfSxcbiAgICB7XG4gICAgICBocmVmOiBSb3V0ZXMuY2hhbmdlUGFzc3dvcmQsXG4gICAgICBsYWJlbDogJ3Byb2ZpbGUtc2lkZWJhci1wYXNzd29yZCcsXG4gICAgfSxcbiAgICB7XG4gICAgICBocmVmOiBSb3V0ZXMubm90aWZ5TG9ncyxcbiAgICAgIGxhYmVsOiAncHJvZmlsZS1zaWRlYmFyLW5vdGlmaWNhdGlvbnMnLFxuICAgIH0sXG4gICAge1xuICAgICAgaHJlZjogUm91dGVzLmNhcmRzLFxuICAgICAgbGFiZWw6ICdwcm9maWxlLXNpZGViYXItbXktY2FyZHMnLFxuICAgICAgLy8gTXVsdGlQYXltZW50OiBNYWtlIGl0IGR5bmFtaWMgb3IgZnJvbSBtYXBwZXJcbiAgICAgIGNhcmRzUGF5bWVudDogW1BheW1lbnRHYXRld2F5LlNUUklQRV0sXG4gICAgfSxcbiAgICB7XG4gICAgICBocmVmOiBSb3V0ZXMub3JkZXJzLFxuICAgICAgbGFiZWw6ICdwcm9maWxlLXNpZGViYXItb3JkZXJzJyxcbiAgICB9LFxuICAgIHtcbiAgICAgIGhyZWY6IFJvdXRlcy5kb3dubG9hZHMsXG4gICAgICBsYWJlbDogJ3Byb2ZpbGUtc2lkZWJhci1kb3dubG9hZHMnLFxuICAgIH0sXG4gICAge1xuICAgICAgaHJlZjogUm91dGVzLndpc2hsaXN0cyxcbiAgICAgIGxhYmVsOiAncHJvZmlsZS1zaWRlYmFyLW15LXdpc2hsaXN0JyxcbiAgICB9LFxuICAgIHtcbiAgICAgIGhyZWY6IFJvdXRlcy5xdWVzdGlvbnMsXG4gICAgICBsYWJlbDogJ3Byb2ZpbGUtc2lkZWJhci1teS1xdWVzdGlvbnMnLFxuICAgIH0sXG4gICAge1xuICAgICAgaHJlZjogUm91dGVzLnJlZnVuZHMsXG4gICAgICBsYWJlbDogJ3RleHQtbXktcmVmdW5kcycsXG4gICAgfSxcbiAgICB7XG4gICAgICBocmVmOiBSb3V0ZXMucmVwb3J0cyxcbiAgICAgIGxhYmVsOiAncHJvZmlsZS1zaWRlYmFyLW15LXJlcG9ydHMnLFxuICAgIH0sXG4gICAge1xuICAgICAgaHJlZjogUm91dGVzLmhlbHAsXG4gICAgICBsYWJlbDogJ3Byb2ZpbGUtc2lkZWJhci1oZWxwJyxcbiAgICB9LFxuICAgIHtcbiAgICAgIGhyZWY6IFJvdXRlcy5sb2dvdXQsXG4gICAgICBsYWJlbDogJ3Byb2ZpbGUtc2lkZWJhci1sb2dvdXQnLFxuICAgIH0sXG4gIF0sXG4gIHNlbGxpbmdBZHZlcnRpc2VtZW50OiB7XG4gICAgaW1hZ2U6IHtcbiAgICAgIHNyYzogJy9zZWxsaW5nLnBuZycsXG4gICAgICBhbHQ6ICdTZWxsaW5nIEFkdmVydGlzZW1lbnQnLFxuICAgIH0sXG4gIH0sXG4gIGN0YToge1xuICAgIG1vY2t1cF9pbWdfc3JjOiAnL21vY2t1cC1pbWcucG5nJyxcbiAgICBwbGF5X3N0b3JlX2xpbms6ICcvJyxcbiAgICBhcHBfc3RvcmVfbGluazogJy8nLFxuICB9LFxuICBoZWFkZXJMaW5rczogW1xuICAgIHsgaHJlZjogUm91dGVzLnNob3BzLCBpY29uOiBudWxsLCBsYWJlbDogJ25hdi1tZW51LXNob3BzJyB9LFxuICAgIHsgaHJlZjogUm91dGVzLmNvdXBvbnMsIGljb246IG51bGwsIGxhYmVsOiAnbmF2LW1lbnUtb2ZmZXInIH0sXG4gICAgeyBocmVmOiBSb3V0ZXMuY29udGFjdFVzLCBsYWJlbDogJ25hdi1tZW51LWNvbnRhY3QnIH0sXG4gICAge1xuICAgICAgaHJlZjogUm91dGVzLmJlY29tZVNlbGxlcixcbiAgICAgIGxhYmVsOiAnQmVjb21lIGEgc2VsbGVyJyxcbiAgICB9LFxuICAgIHsgaHJlZjogUm91dGVzLmZsYXNoU2FsZSwgbGFiZWw6ICduYXYtbWVudS1mbGFzaC1zYWxlJyB9LFxuICAgIHsgaHJlZjogUm91dGVzLm1hbnVmYWN0dXJlcnMsIGxhYmVsOiAndGV4dC1tYW51ZmFjdHVyZXJzJyB9LFxuICAgIHsgaHJlZjogUm91dGVzLmF1dGhvcnMsIGxhYmVsOiAndGV4dC1hdXRob3JzJyB9LFxuICAgIHsgaHJlZjogUm91dGVzLmhlbHAsIGxhYmVsOiAnbmF2LW1lbnUtZmFxJyB9LFxuICAgIHsgaHJlZjogUm91dGVzLnRlcm1zLCBsYWJlbDogJ25hdi1tZW51LXRlcm1zJyB9LFxuICAgIHsgaHJlZjogUm91dGVzLmN1c3RvbWVyUmVmdW5kUG9saWNpZXMsIGxhYmVsOiAnbmF2LW1lbnUtcmVmdW5kLXBvbGljeScgfSxcbiAgICB7XG4gICAgICBocmVmOiBSb3V0ZXMudmVuZG9yUmVmdW5kUG9saWNpZXMsXG4gICAgICBsYWJlbDogJ25hdi1tZW51LXZlbmRvci1yZWZ1bmQtcG9saWN5JyxcbiAgICB9LFxuICBdLFxuICBmb290ZXI6IHtcbiAgICAvLyBjb3B5cmlnaHQ6IHtcbiAgICAvLyAgIG5hbWU6ICdSZWRRLCBJbmMnLFxuICAgIC8vICAgaHJlZjogJ2h0dHBzOi8vcmVkcS5pby8nLFxuICAgIC8vIH0sXG4gICAgLy8gYWRkcmVzczogJzI0MjkgUml2ZXIgRHJpdmUsIFN1aXRlIDM1IENvdHRvbmhhbGwsIENBIDIyOTYgVW5pdGVkIEtpbmdkb20nLFxuICAgIC8vIGVtYWlsOiAnZHVtbXlAZHVtbXkuY29tJyxcbiAgICAvLyBwaG9uZTogJysxIDI1Ni02OTgtMDY5NCcsXG4gICAgbWVudXM6IFtcbiAgICAgIHtcbiAgICAgICAgdGl0bGU6ICd0ZXh0LWV4cGxvcmUnLFxuICAgICAgICBsaW5rczogW1xuICAgICAgICAgIHtcbiAgICAgICAgICAgIG5hbWU6ICdTaG9wcycsXG4gICAgICAgICAgICBocmVmOiBSb3V0ZXMuc2hvcHMsXG4gICAgICAgICAgfSxcbiAgICAgICAgICB7XG4gICAgICAgICAgICBuYW1lOiAnQXV0aG9ycycsXG4gICAgICAgICAgICBocmVmOiBSb3V0ZXMuYXV0aG9ycyxcbiAgICAgICAgICB9LFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIG5hbWU6ICdGbGFzaCBEZWFscycsXG4gICAgICAgICAgICBocmVmOiBSb3V0ZXM/LmZsYXNoU2FsZSxcbiAgICAgICAgICB9LFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIG5hbWU6ICdDb3Vwb24nLFxuICAgICAgICAgICAgaHJlZjogUm91dGVzLmNvdXBvbnMsXG4gICAgICAgICAgfSxcbiAgICAgICAgXSxcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHRpdGxlOiAndGV4dC1jdXN0b21lci1zZXJ2aWNlJyxcbiAgICAgICAgbGlua3M6IFtcbiAgICAgICAgICB7XG4gICAgICAgICAgICBuYW1lOiAndGV4dC1mYXEtaGVscCcsXG4gICAgICAgICAgICBocmVmOiBSb3V0ZXMuaGVscCxcbiAgICAgICAgICB9LFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIG5hbWU6ICdWZW5kb3IgUmVmdW5kIFBvbGljaWVzJyxcbiAgICAgICAgICAgIGhyZWY6IFJvdXRlcy52ZW5kb3JSZWZ1bmRQb2xpY2llcyxcbiAgICAgICAgICB9LFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIG5hbWU6ICdDdXN0b21lciBSZWZ1bmQgUG9saWNpZXMnLFxuICAgICAgICAgICAgaHJlZjogUm91dGVzLmN1c3RvbWVyUmVmdW5kUG9saWNpZXMsXG4gICAgICAgICAgfSxcbiAgICAgICAgXSxcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHRpdGxlOiAndGV4dC1vdXItaW5mb3JtYXRpb24nLFxuICAgICAgICBsaW5rczogW1xuICAgICAgICAgIHtcbiAgICAgICAgICAgIG5hbWU6ICdNYW51ZmFjdHVyZXJzJyxcbiAgICAgICAgICAgIGhyZWY6IFJvdXRlcz8ubWFudWZhY3R1cmVycyxcbiAgICAgICAgICB9LFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIG5hbWU6ICdQcml2YWN5IHBvbGljaWVzJyxcbiAgICAgICAgICAgIGhyZWY6IFJvdXRlcy5wcml2YWN5LFxuICAgICAgICAgIH0sXG4gICAgICAgICAge1xuICAgICAgICAgICAgbmFtZTogJ3RleHQtdGVybXMtY29uZGl0aW9uJyxcbiAgICAgICAgICAgIGhyZWY6IFJvdXRlcy50ZXJtcyxcbiAgICAgICAgICB9LFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIG5hbWU6ICd0ZXh0LWNvbnRhY3QtdXMnLFxuICAgICAgICAgICAgaHJlZjogUm91dGVzLmNvbnRhY3RVcyxcbiAgICAgICAgICB9LFxuICAgICAgICBdLFxuICAgICAgfSxcbiAgICBdLFxuICAgIC8vIHBheW1lbnRfbWV0aG9kczogW1xuICAgIC8vICAge1xuICAgIC8vICAgICBpbWc6ICcvcGF5bWVudC9tYXN0ZXIucG5nJyxcbiAgICAvLyAgICAgdXJsOiAnLycsXG4gICAgLy8gICB9LFxuICAgIC8vICAge1xuICAgIC8vICAgICBpbWc6ICcvcGF5bWVudC9za3JpbGwucG5nJyxcbiAgICAvLyAgICAgdXJsOiAnLycsXG4gICAgLy8gICB9LFxuICAgIC8vICAge1xuICAgIC8vICAgICBpbWc6ICcvcGF5bWVudC9wYXlwYWwucG5nJyxcbiAgICAvLyAgICAgdXJsOiAnLycsXG4gICAgLy8gICB9LFxuICAgIC8vICAge1xuICAgIC8vICAgICBpbWc6ICcvcGF5bWVudC92aXNhLnBuZycsXG4gICAgLy8gICAgIHVybDogJy8nLFxuICAgIC8vICAgfSxcbiAgICAvLyAgIHtcbiAgICAvLyAgICAgaW1nOiAnL3BheW1lbnQvZGlzY292ZXIucG5nJyxcbiAgICAvLyAgICAgdXJsOiAnLycsXG4gICAgLy8gICB9LFxuICAgIC8vIF0sXG4gIH0sXG59O1xuIl0sIm5hbWVzIjpbIlJvdXRlcyIsIlBheW1lbnRHYXRld2F5Iiwic2l0ZVNldHRpbmdzIiwibmFtZSIsImRlc2NyaXB0aW9uIiwibG9nbyIsInVybCIsImFsdCIsImhyZWYiLCJ3aWR0aCIsImhlaWdodCIsImRlZmF1bHRMYW5ndWFnZSIsImN1cnJlbmN5Q29kZSIsInByb2R1Y3QiLCJwbGFjZWhvbGRlckltYWdlIiwiY2FyZE1hcHMiLCJncm9jZXJ5IiwiZnVybml0dXJlIiwiYmFnIiwibWFrZXVwIiwiYm9vayIsIm1lZGljaW5lIiwiZGVmYXVsdCIsImF1dGhvcml6ZWRMaW5rcyIsInByb2ZpbGUiLCJsYWJlbCIsIm9yZGVycyIsIndpc2hsaXN0cyIsImNoZWNrb3V0IiwiYXV0aG9yaXplZExpbmtzTW9iaWxlIiwibm90aWZ5TG9ncyIsImNhcmRzIiwicXVlc3Rpb25zIiwicmVmdW5kcyIsInJlcG9ydHMiLCJjaGFuZ2VQYXNzd29yZCIsImRhc2hib2FyZFNpZGViYXJNZW51IiwiY2FyZHNQYXltZW50IiwiU1RSSVBFIiwiZG93bmxvYWRzIiwiaGVscCIsImxvZ291dCIsInNlbGxpbmdBZHZlcnRpc2VtZW50IiwiaW1hZ2UiLCJzcmMiLCJjdGEiLCJtb2NrdXBfaW1nX3NyYyIsInBsYXlfc3RvcmVfbGluayIsImFwcF9zdG9yZV9saW5rIiwiaGVhZGVyTGlua3MiLCJzaG9wcyIsImljb24iLCJjb3Vwb25zIiwiY29udGFjdFVzIiwiYmVjb21lU2VsbGVyIiwiZmxhc2hTYWxlIiwibWFudWZhY3R1cmVycyIsImF1dGhvcnMiLCJ0ZXJtcyIsImN1c3RvbWVyUmVmdW5kUG9saWNpZXMiLCJ2ZW5kb3JSZWZ1bmRQb2xpY2llcyIsImZvb3RlciIsIm1lbnVzIiwidGl0bGUiLCJsaW5rcyIsInByaXZhY3kiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/config/site.ts\n");

/***/ })

};
;