"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WithdrawsModule = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const withdraws_service_1 = require("./withdraws.service");
const withdraws_controller_1 = require("./withdraws.controller");
const withdraw_entity_1 = require("./entities/withdraw.entity");
let WithdrawsModule = class WithdrawsModule {
};
WithdrawsModule = __decorate([
    (0, common_1.Module)({
        imports: [sequelize_1.SequelizeModule.forFeature([withdraw_entity_1.Withdraw])],
        controllers: [withdraws_controller_1.WithdrawsController],
        providers: [withdraws_service_1.WithdrawsService],
    })
], WithdrawsModule);
exports.WithdrawsModule = WithdrawsModule;
//# sourceMappingURL=withdraws.module.js.map