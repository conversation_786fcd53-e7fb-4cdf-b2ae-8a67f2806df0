import type {
  CategoryQueryOptions,
  HomePageProps,
  PopularProductQueryOptions,
  SettingsQueryOptions,
  TypeQueryOptions,
  BestSellingProductQueryOptions,
} from '@/types';
import type { GetStaticPaths, GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { QueryClient } from 'react-query';
import { dehydrate } from 'react-query/hydration';
import invariant from 'tiny-invariant';
import client from './client';
import { API_ENDPOINTS } from './client/api-endpoints';
import {
  CATEGORIES_PER_PAGE,
  PRODUCTS_PER_PAGE,
  TYPES_PER_PAGE,
} from './client/variables';

type ParsedQueryParams = {
  pages: string[];
};

// This function gets called at build time
export const getStaticPaths: GetStaticPaths<ParsedQueryParams> = async ({
  locales,
}) => {
  invariant(locales, 'locales is not defined');

  try {
    const data = await client.types.all({ limit: 100 });
    const paths =
      data?.flatMap((type) =>
        locales?.map((locale) => ({ params: { pages: [type.slug] }, locale })),
      ) || [];

    // We'll pre-render only these paths at build time also with the slash route.
    return {
      paths: paths.concat(
        locales?.map((locale) => ({ params: { pages: [] }, locale })) || [],
      ),
      fallback: 'blocking',
    };
  } catch (error) {
    console.warn(
      'Failed to fetch types for static paths, using fallback:',
      error,
    );
    // Return minimal paths if API fails
    return {
      paths:
        locales?.map((locale) => ({ params: { pages: [] }, locale })) || [],
      fallback: 'blocking',
    };
  }
};

export const getStaticProps: GetStaticProps<
  HomePageProps,
  ParsedQueryParams
> = async ({ locale, params }) => {
  const queryClient = new QueryClient();
  await queryClient.prefetchQuery(
    [API_ENDPOINTS.SETTINGS, { language: locale }],
    ({ queryKey }) => client.settings.all(queryKey[1] as SettingsQueryOptions),
  );
  const types = await queryClient.fetchQuery(
    [API_ENDPOINTS.TYPES, { limit: TYPES_PER_PAGE, language: locale }],
    ({ queryKey }) => client.types.all(queryKey[1] as TypeQueryOptions),
  );

  const { pages } = params!;
  let pageType: string | undefined;

  // Handle case when no types exist in database
  if (!types || types.length === 0) {
    pageType = 'default'; // Use a default type when no types exist
  } else if (!pages) {
    pageType =
      types.find((type) => type?.settings?.isHome)?.slug ?? types?.[0]?.slug;
  } else {
    pageType = pages[0];
  }

  // Only return notFound if we have types but the requested type doesn't exist
  if (
    types &&
    types.length > 0 &&
    pageType !== 'default' &&
    !types?.some((t) => t.slug === pageType)
  ) {
    return {
      notFound: true,
      // This is require to regenerate the page
      revalidate: 120,
    };
  }

  // Only fetch type details if it's not the default fallback
  if (pageType !== 'default') {
    try {
      await queryClient.prefetchQuery(
        [API_ENDPOINTS.TYPES, { slug: pageType, language: locale }],
        ({ queryKey }: any) => client.types.get(queryKey[1]),
      );
    } catch (error) {
      console.warn('Failed to fetch type details:', error);
    }
  }

  const productVariables = {
    type: pageType === 'default' ? undefined : pageType,
    limit: PRODUCTS_PER_PAGE,
  };

  try {
    await queryClient.prefetchInfiniteQuery(
      [
        API_ENDPOINTS.PRODUCTS,
        {
          limit: PRODUCTS_PER_PAGE,
          type: pageType === 'default' ? undefined : pageType,
          language: locale,
        },
      ],
      ({ queryKey }) => client.products.all(queryKey[1] as any),
    );
  } catch (error) {
    console.warn('Failed to fetch products:', error);
  }

  const popularProductVariables = {
    type_slug: pageType === 'default' ? undefined : pageType,
    limit: 10,
    with: 'type;author',
    language: locale,
  };

  // Only prefetch popular products for `book` demo
  if (pageType === 'book') {
    try {
      await queryClient.prefetchQuery(
        [API_ENDPOINTS.PRODUCTS_POPULAR, popularProductVariables],
        ({ queryKey }) =>
          client.products.popular(queryKey[1] as PopularProductQueryOptions),
      );

      await queryClient.prefetchQuery(
        [API_ENDPOINTS.BEST_SELLING_PRODUCTS, popularProductVariables],
        ({ queryKey }) =>
          client.products.bestSelling(
            queryKey[1] as BestSellingProductQueryOptions,
          ),
      );
    } catch (error) {
      console.warn('Failed to fetch popular/bestselling products:', error);
    }
  }

  const currentType = types?.find((t) => t.slug === pageType);
  const categoryVariables = {
    type: pageType === 'default' ? undefined : pageType,
    limit: CATEGORIES_PER_PAGE,
    language: locale,
    parent: currentType?.settings?.layoutType === 'minimal' ? 'all' : 'null',
  };

  try {
    await queryClient.prefetchInfiniteQuery(
      [API_ENDPOINTS.CATEGORIES, categoryVariables],
      ({ queryKey }) =>
        client.categories.all(queryKey[1] as CategoryQueryOptions),
    );
  } catch (error) {
    console.warn('Failed to fetch categories:', error);
  }
  const currentType = types?.find((t) => t.slug === pageType);

  return {
    props: {
      variables: {
        popularProducts: popularProductVariables,
        products: productVariables,
        categories: categoryVariables,
        bestSellingProducts: popularProductVariables,
        layoutSettings: {
          ...currentType?.settings,
        },
        types: {
          type: pageType,
        },
      },
      layout: currentType?.settings?.layoutType ?? 'classic',
      ...(await serverSideTranslations(locale!, ['common', 'banner'])),
      dehydratedState: JSON.parse(JSON.stringify(dehydrate(queryClient))),
    },
    revalidate: 120,
  };
};

/* Fix : locales: 14kB,
popularProducts: 30kB,
category: 22kB,
groups: 8kB,
group: 2kB,
settings: 2kB,
perProduct: 4.2 * 30 = 120kB,
total = 14 + 30 + 22 + 8 + 2 + 2 + 120 = 198kB
others: 225 - 198 = 27kB

 */
