import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreateUserDto } from './dto/create-user.dto';
import { GetUsersDto, UserPaginator } from './dto/get-users.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User, Permission } from './entities/user.entity';
import { Profile } from './entities/profile.entity';
import { Op } from 'sequelize';
import { paginate } from '../common/pagination/paginate';
import Fuse from 'fuse.js';
import { Op } from 'sequelize';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(Permission)
    private permissionModel: typeof Permission,
    @InjectModel(Profile)
    private profileModel: typeof Profile,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    return this.userModel.create({ ...createUserDto });
  }

  async getUsers({
    text,
    limit,
    page,
    search,
  }: GetUsersDto): Promise<UserPaginator> {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const offset = (page - 1) * limit;

    const whereClause: any = {};

    if (text?.replace(/%/g, '')) {
      whereClause.name = {
        [Op.iLike]: `%${text}%`,
      };
    }

    if (search) {
      const parseSearchParams = search.split(';');
      for (const searchParam of parseSearchParams) {
        const [key, value] = searchParam.split(':');
        if (key !== 'slug' && key in this.userModel.rawAttributes) {
          whereClause[key] = {
            [Op.iLike]: `%${value}%`,
          };
        }
      }
    }

    const { count, rows } = await this.userModel.findAndCountAll({
      where: whereClause,
      include: [
        { model: Profile, as: 'profile' },
        { model: Permission, as: 'permissions' },
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']],
    });

    const url = `/users?limit=${limit}`;
    const totalPages = Math.ceil(count / limit);

    return {
      data: rows,
      count,
      current_page: page,
      firstItem: offset + 1,
      lastItem: Math.min(offset + limit, count),
      last_page: totalPages,
      per_page: limit,
      total: count,
      first_page_url: `${url}&page=1`,
      last_page_url: `${url}&page=${totalPages}`,
      next_page_url: page < totalPages ? `${url}&page=${page + 1}` : null,
      prev_page_url: page > 1 ? `${url}&page=${page - 1}` : null,
    };
  }

  async getUsersNotify({ limit }: GetUsersDto): Promise<User[]> {
    return this.userModel.findAll({
      limit,
      order: [['created_at', 'DESC']],
      include: [{ model: Profile, as: 'profile' }],
    });
  }

  async findOne(id: number): Promise<User> {
    return this.userModel.findByPk(id, {
      include: [
        { model: Profile, as: 'profile' },
        { model: Permission, as: 'permissions' },
      ],
    });
  }

  async update(
    id: number,
    updateUserDto: UpdateUserDto,
  ): Promise<[number, User[]]> {
    const [affectedCount, updatedUsers] = await this.userModel.update(
      { ...updateUserDto },
      { where: { id }, returning: true },
    );
    return [affectedCount, updatedUsers];
  }

  async remove(id: number): Promise<number> {
    return this.userModel.destroy({ where: { id } });
  }

  async makeAdmin(user_id: string): Promise<User> {
    return this.userModel.findByPk(Number(user_id));
  }

  async banUser(id: number): Promise<User> {
    const user = await this.userModel.findByPk(id);
    if (user) {
      user.is_active = !user.is_active;
      await user.save();
    }
    return user;
  }

  activeUser(id: number) {
    const user = this.users.find((u) => u.id === Number(id));

    user.is_active = !user.is_active;

    return user;
  }

  async getAdmin({
    text,
    limit,
    page,
    search,
  }: GetUsersDto): Promise<UserPaginator> {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    let data: User[] = this.users.filter(function (element) {
      return element.permissions.some(function (subElement) {
        return subElement.name === 'super_admin';
      });
    });

    if (text?.replace(/%/g, '')) {
      data = fuse.search(text)?.map(({ item }) => item);
    }
    const results = data.slice(startIndex, endIndex);
    const url = `/admin/list?limit=${limit}`;

    return {
      data: results,
      ...paginate(data.length, page, limit, results.length, url),
    };
  }

  async getVendors({
    text,
    limit,
    page,
    search,
  }: GetUsersDto): Promise<UserPaginator> {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    let data: User[] = this.users.filter(function (element) {
      return element.permissions.some(function (subElement) {
        return subElement.name === 'store_owner';
      });
    });

    if (text?.replace(/%/g, '')) {
      data = fuse.search(text)?.map(({ item }) => item);
    }
    const results = data.slice(startIndex, endIndex);
    const url = `/vendors/list?limit=${limit}`;

    return {
      data: results,
      ...paginate(data.length, page, limit, results.length, url),
    };
  }

  async getAllCustomers({
    text,
    limit,
    page,
    search,
  }: GetUsersDto): Promise<UserPaginator> {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    let data: User[] = this.users.filter(function (element) {
      return element.permissions.some(function (subElement) {
        return subElement.name === 'customer';
      });
    });

    if (text?.replace(/%/g, '')) {
      data = fuse.search(text)?.map(({ item }) => item);
    }
    const results = data.slice(startIndex, endIndex);
    const url = `/customers/list?limit=${limit}`;

    return {
      data: results,
      ...paginate(data.length, page, limit, results.length, url),
    };
  }

  async getMyStaffs({
    text,
    limit,
    page,
    search,
  }: GetUsersDto): Promise<UserPaginator> {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    let data: User[] = [];

    if (text?.replace(/%/g, '')) {
      data = fuse.search(text)?.map(({ item }) => item);
    }
    const results = data.slice(startIndex, endIndex);
    const url = `/my-staffs/list?limit=${limit}`;

    return {
      data: results,
      ...paginate(data.length, page, limit, results.length, url),
    };
  }

  async getAllStaffs({
    text,
    limit,
    page,
    search,
  }: GetUsersDto): Promise<UserPaginator> {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    let data: User[] = [];

    if (text?.replace(/%/g, '')) {
      data = fuse.search(text)?.map(({ item }) => item);
    }
    const results = data.slice(startIndex, endIndex);
    const url = `/all-staffs/list?limit=${limit}`;

    return {
      data: results,
      ...paginate(data.length, page, limit, results.length, url),
    };
  }
}
