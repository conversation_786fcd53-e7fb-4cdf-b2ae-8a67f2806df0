import { CreateUserDto } from './dto/create-user.dto';
import { GetUsersDto, UserPaginator } from './dto/get-users.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User, Permission } from './entities/user.entity';
import { Profile } from './entities/profile.entity';
export declare class UsersService {
    private userModel;
    private permissionModel;
    private profileModel;
    constructor(userModel: typeof User, permissionModel: typeof Permission, profileModel: typeof Profile);
    create(createUserDto: CreateUserDto): Promise<User>;
    getUsers({ text, limit, page, search, }: GetUsersDto): Promise<UserPaginator>;
    getUsersNotify({ limit }: GetUsersDto): Promise<User[]>;
    findOne(id: number): Promise<User>;
    update(id: number, updateUserDto: UpdateUserDto): Promise<[number, User[]]>;
    remove(id: number): Promise<number>;
    makeAdmin(user_id: string): Promise<User>;
    banUser(id: number): Promise<User>;
    activeUser(id: number): Promise<User>;
    getAdmin({ text, limit, page, search, }: GetUsersDto): Promise<UserPaginator>;
    getVendors({ text, limit, page, search, }: GetUsersDto): Promise<UserPaginator>;
    getAllCustomers({ text, limit, page, search, }: GetUsersDto): Promise<UserPaginator>;
    getMyStaffs({ text, limit, page, search, }: GetUsersDto): Promise<UserPaginator>;
    getAllStaffs({ text, limit, page, search, }: GetUsersDto): Promise<UserPaginator>;
}
