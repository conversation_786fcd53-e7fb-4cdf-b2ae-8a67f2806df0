import {
  Column,
  Model,
  Table,
  DataType,
  BelongsTo,
  BelongsToMany,
  ForeignKey,
} from 'sequelize-typescript';
import { Product, ProductTag } from 'src/products/entities/product.entity';
import { Type } from 'src/types/entities/type.entity';

@Table({
  tableName: 'tags',
  timestamps: true,
})
export class Tag extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  slug: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  parent: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  details: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  image: any; // Attachment

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  icon: string;

  @ForeignKey(() => Type)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  type_id: number;

  @BelongsTo(() => Type)
  type: Type;

  @BelongsToMany(() => Product, () => ProductTag)
  products: Product[];

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'en',
  })
  language: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  translated_languages: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;
}
