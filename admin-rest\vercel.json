{"env": {"NEXT_PUBLIC_REST_API_ENDPOINT": "https://mock.redq.io/api", "NEXT_PUBLIC_SHOP_URL": "https://pickbazar-react-rest.vercel.app", "NEXT_PUBLIC_GOOGLE_MAP_API_KEY": "", "NEXT_PUBLIC_AUTH_TOKEN_KEY": "AUTH_CRED", "APPLICATION_MODE": "production", "NEXT_PUBLIC_DEFAULT_LANGUAGE": "en", "NEXT_PUBLIC_ENABLE_MULTI_LANG": "false", "NEXT_PUBLIC_AVAILABLE_LANGUAGES": "en,de,es", "NEXT_PUBLIC_PUSHER_DEV_MOOD": "false", "NEXT_PUBLIC_PUSHER_APP_KEY": "", "NEXT_PUBLIC_PUSHER_APP_CLUSTER": "", "NEXT_PUBLIC_STORE_NOTICE_CREATED_CHANNEL_PRIVATE": "private-store_notice.created", "NEXT_PUBLIC_ORDER_CREATED_CHANNEL_PRIVATE": "private-order.created", "NEXT_PUBLIC_MESSAGE_CHANNEL_PRIVATE": "private-message.created", "NEXT_PUBLIC_STORE_NOTICE_CREATED_EVENT": "store.notice.event", "NEXT_PUBLIC_ORDER_CREATED_EVENT": "order.create.event", "NEXT_PUBLIC_MESSAGE_EVENT": "message.event", "NEXT_PUBLIC_VERSION": "11.10.0"}, "build": {"env": {"NEXT_PUBLIC_REST_API_ENDPOINT": "https://mock.redq.io/api", "NEXT_PUBLIC_SHOP_URL": "https://onekart-shop.vercel.app", "NEXT_PUBLIC_GOOGLE_MAP_API_KEY": "", "NEXT_PUBLIC_AUTH_TOKEN_KEY": "AUTH_CRED", "APPLICATION_MODE": "production", "NEXT_PUBLIC_DEFAULT_LANGUAGE": "en", "NEXT_PUBLIC_ENABLE_MULTI_LANG": "false", "NEXT_PUBLIC_AVAILABLE_LANGUAGES": "en,de,es", "NEXT_PUBLIC_PUSHER_DEV_MOOD": "false", "NEXT_PUBLIC_PUSHER_APP_KEY": "", "NEXT_PUBLIC_PUSHER_APP_CLUSTER": "", "NEXT_PUBLIC_STORE_NOTICE_CREATED_CHANNEL_PRIVATE": "private-store_notice.created", "NEXT_PUBLIC_ORDER_CREATED_CHANNEL_PRIVATE": "private-order.created", "NEXT_PUBLIC_MESSAGE_CHANNEL_PRIVATE": "private-message.created", "NEXT_PUBLIC_STORE_NOTICE_CREATED_EVENT": "store.notice.event", "NEXT_PUBLIC_ORDER_CREATED_EVENT": "order.create.event", "NEXT_PUBLIC_MESSAGE_EVENT": "message.event", "NEXT_PUBLIC_VERSION": "11.10.0"}}}