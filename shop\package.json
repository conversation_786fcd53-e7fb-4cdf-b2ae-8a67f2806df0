{"name": "@onekart/shop", "version": "11.10.0", "private": true, "scripts": {"clean": "rimraf -g \"{.next,node_modules,__generated__,.cache,src/framework/graphql/**/*.d.ts}\"", "gql-clean": "rimraf -g \"{__generated__,.next,.cache,src/framework/graphql/**/*.d.ts}\"", "dev:rest": "next dev -p 3003", "build:rest": "next build", "codegen": "node -r dotenv/config $(yarn bin)/graphql-let", "dev:gql": "yarn codegen && next dev -p 3001", "build:gql": "yarn codegen && next build", "start": "next start", "lint": "next lint", "postbuild:rest": "next-sitemap", "prepare": "husky install"}, "dependencies": {"@apollo/client": "3.11.8", "@floating-ui/react": "0.26.25", "@headlessui/react": "1.7.17", "@hookform/resolvers": "3.9.1", "@react-google-maps/api": "2.20.3", "@stripe/react-stripe-js": "2.8.1", "@stripe/stripe-js": "2.1.11", "apollo-upload-client": "17.0.0", "axios": "1.7.7", "body-scroll-lock": "4.0.0-beta.0", "camelcase-keys": "9.1.3", "classnames": "2.5.1", "cookie": "0.5.0", "date-fns": "2.30.0", "dayjs": "1.11.13", "deepmerge": "4.3.1", "framer-motion": "10.16.4", "i18next": "23.16.4", "jotai": "2.10.1", "js-cookie": "3.0.5", "little-state-machine": "4.8.0", "lodash": "4.17.21", "next": "13.5.6", "next-auth": "4.24.10", "next-i18next": "15.3.1", "next-seo": "6.6.0", "overlayscrollbars": "2.10.0", "overlayscrollbars-react": "0.5.6", "rc-collapse": "3.9.0", "rc-pagination": "3.7.0", "rc-rate": "2.13.0", "rc-slider": "10.4.0", "rc-table": "7.22.2", "react": "18.3.1", "react-content-loader": "6.2.1", "react-copy-to-clipboard": "5.1.0", "react-countdown": "2.3.6", "react-device-detect": "2.2.3", "react-dom": "18.3.1", "react-dropzone": "14.2.10", "react-hook-form": "7.53.1", "react-i18next": "13.3.1", "react-otp-input": "3.1.1", "react-phone-input-2": "2.15.1", "react-player": "2.16.0", "react-query": "3.39.3", "react-scroll": "1.9.0", "react-select": "5.8.2", "react-sticky-box": "2.0.5", "react-toastify": "9.1.3", "react-use": "17.5.1", "sanitize-html": "2.13.1", "screenfull": "^6.0.2", "sharp": "0.32.6", "swiper": "11.1.14", "tailwind-merge": "2.5.4", "tiny-invariant": "1.3.3", "yup": "1.4.0"}, "devDependencies": {"@graphql-codegen/cli": "5.0.3", "@graphql-codegen/import-types-preset": "3.0.0", "@graphql-codegen/typescript": "4.1.1", "@graphql-codegen/typescript-operations": "4.3.1", "@graphql-codegen/typescript-react-apollo": "4.3.2", "@graphql-codegen/typescript-resolvers": "4.4.0", "@tailwindcss/forms": "0.5.9", "@tailwindcss/typography": "0.5.15", "@types/apollo-upload-client": "17.0.5", "@types/body-scroll-lock": "3.1.2", "@types/classnames": "2.3.4", "@types/cookie": "0.5.4", "@types/html-entities": "1.3.4", "@types/js-cookie": "3.0.6", "@types/lodash": "4.17.13", "@types/node": "20.8.10", "@types/react": "18.3.12", "@types/react-copy-to-clipboard": "5.0.7", "@types/react-dom": "18.3.1", "@types/react-mailchimp-subscribe": "2.1.4", "@types/react-scroll": "1.8.10", "@types/react-select": "5.0.1", "@types/sanitize-html": "2.13.0", "autoprefixer": "10.4.20", "eslint": "8.53.0", "eslint-config-next": "14.0.1", "eslint-config-prettier": "9.1.0", "graphql": "16.9.0", "graphql-let": "0.18.6", "husky": "8.0.3", "lint-staged": "15.2.10", "next-sitemap": "4.2.3", "postcss": "8.4.47", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "0.5.6", "rimraf": "5.0.5", "tailwindcss": "3.4.14", "ts-node": "10.9.2", "typescript": "5.6.3", "yaml-loader": "0.8.1"}}