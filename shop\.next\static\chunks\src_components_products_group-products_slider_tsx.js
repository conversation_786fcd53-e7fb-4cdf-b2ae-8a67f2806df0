"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_products_group-products_slider_tsx"],{

/***/ "./src/components/icons/check-icon.tsx":
/*!*********************************************!*\
  !*** ./src/components/icons/check-icon.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CheckIcon = (param)=>{\n    let { width = 24, height = 24, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: width,\n        height: height,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M20 6L9 17L4 12\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\check-icon.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\check-icon.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\n_c = CheckIcon;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CheckIcon);\nvar _c;\n$RefreshReg$(_c, \"CheckIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9jaGVjay1pY29uLnRzeCIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsTUFBTUEsWUFBK0M7UUFBQyxFQUNwREMsUUFBUSxFQUFFLEVBQ1ZDLFNBQVMsRUFBRSxFQUNYLEdBQUdDLE9BQ0o7SUFDQyxxQkFDRSw4REFBQ0M7UUFDQ0gsT0FBT0E7UUFDUEMsUUFBUUE7UUFDUkcsU0FBUTtRQUNSQyxNQUFLO1FBQ0xDLFFBQU87UUFDTixHQUFHSixLQUFLO2tCQUVULDRFQUFDSztZQUNDQyxHQUFFO1lBQ0ZDLGFBQVk7WUFDWkMsZUFBYztZQUNkQyxnQkFBZTs7Ozs7Ozs7Ozs7QUFJdkI7S0F0Qk1aO0FBd0JOLCtEQUFlQSxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2ljb25zL2NoZWNrLWljb24udHN4P2Q2NGQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgQ2hlY2tJY29uOiBSZWFjdC5GQzxSZWFjdC5TVkdBdHRyaWJ1dGVzPHt9Pj4gPSAoe1xuICB3aWR0aCA9IDI0LFxuICBoZWlnaHQgPSAyNCxcbiAgLi4ucHJvcHNcbn0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8c3ZnXG4gICAgICB3aWR0aD17d2lkdGh9XG4gICAgICBoZWlnaHQ9e2hlaWdodH1cbiAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgIHsuLi5wcm9wc31cbiAgICA+XG4gICAgICA8cGF0aFxuICAgICAgICBkPVwiTTIwIDZMOSAxN0w0IDEyXCJcbiAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcbiAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAvPlxuICAgIDwvc3ZnPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hlY2tJY29uO1xuIl0sIm5hbWVzIjpbIkNoZWNrSWNvbiIsIndpZHRoIiwiaGVpZ2h0IiwicHJvcHMiLCJzdmciLCJ2aWV3Qm94IiwiZmlsbCIsInN0cm9rZSIsInBhdGgiLCJkIiwic3Ryb2tlV2lkdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/check-icon.tsx\n"));

/***/ }),

/***/ "./src/components/icons/index.ts":
/*!***************************************!*\
  !*** ./src/components/icons/index.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowNext: function() { return /* reexport safe */ _arrow_next__WEBPACK_IMPORTED_MODULE_1__.ArrowNextIcon; },\n/* harmony export */   ArrowPrev: function() { return /* reexport safe */ _arrow_prev__WEBPACK_IMPORTED_MODULE_2__.ArrowPrevIcon; },\n/* harmony export */   Check: function() { return /* reexport safe */ _check_icon__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _check_icon__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-icon */ \"./src/components/icons/check-icon.tsx\");\n/* harmony import */ var _arrow_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./arrow-next */ \"./src/components/icons/arrow-next.tsx\");\n/* harmony import */ var _arrow_prev__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./arrow-prev */ \"./src/components/icons/arrow-prev.tsx\");\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBZ0Q7QUFDVTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2ljb25zL2luZGV4LnRzPzdmNjQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVjayB9IGZyb20gXCIuL2NoZWNrLWljb25cIjtcbmV4cG9ydCB7IEFycm93TmV4dEljb24gYXMgQXJyb3dOZXh0IH0gZnJvbSBcIi4vYXJyb3ctbmV4dFwiO1xuZXhwb3J0IHsgQXJyb3dQcmV2SWNvbiBhcyBBcnJvd1ByZXYgfSBmcm9tIFwiLi9hcnJvdy1wcmV2XCI7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIkNoZWNrIiwiQXJyb3dOZXh0SWNvbiIsIkFycm93TmV4dCIsIkFycm93UHJldkljb24iLCJBcnJvd1ByZXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/index.ts\n"));

/***/ }),

/***/ "./src/components/products/group-products/slider.tsx":
/*!***********************************************************!*\
  !*** ./src/components/products/group-products/slider.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_slider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/slider */ \"./src/components/ui/slider.tsx\");\n/* harmony import */ var _components_icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons */ \"./src/components/icons/index.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_products_cards_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/products/cards/card */ \"./src/components/products/cards/card.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst offerSliderBreakpoints = {\n    320: {\n        slidesPerView: 1,\n        spaceBetween: 0\n    },\n    580: {\n        slidesPerView: 2,\n        spaceBetween: 16\n    },\n    1024: {\n        slidesPerView: 3,\n        spaceBetween: 16\n    },\n    1920: {\n        slidesPerView: 5,\n        spaceBetween: 24\n    }\n};\nconst ProductsSlider = (param)=>{\n    let { products } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_1__.Swiper, {\n                id: \"handPicked_products\",\n                breakpoints: offerSliderBreakpoints,\n                modules: [\n                    _components_ui_slider__WEBPACK_IMPORTED_MODULE_1__.Navigation\n                ],\n                navigation: {\n                    nextEl: \".next\",\n                    prevEl: \".prev\"\n                },\n                children: products === null || products === void 0 ? void 0 : products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_1__.SwiperSlide, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_cards_card__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            product: product\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, undefined)\n                    }, product === null || product === void 0 ? void 0 : product.id, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prev absolute top-2/4 z-10 -mt-4 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-border-200 border-opacity-70 bg-light text-heading shadow-xl transition-all duration-200 hover:border-accent hover:bg-accent hover:text-light ltr:-left-4 rtl:-right-4 md:-mt-5 md:h-9 md:w-9 ltr:md:-left-5 rtl:md:-right-5\",\n                role: \"button\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"common:text-previous\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_2__.ArrowPrev, {\n                        width: 18,\n                        height: 18\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"next absolute top-2/4 z-10 -mt-4 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-border-200 border-opacity-70 bg-light text-heading shadow-xl transition-all duration-200 hover:border-accent hover:bg-accent hover:text-light ltr:-right-4 rtl:-left-4 md:-mt-5 md:h-9 md:w-9 ltr:md:-right-5\",\n                role: \"button\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"common:text-next\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_2__.ArrowNext, {\n                        width: 18,\n                        height: 18\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductsSlider, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = ProductsSlider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProductsSlider);\nvar _c;\n$RefreshReg$(_c, \"ProductsSlider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/group-products/slider.tsx\n"));

/***/ })

}]);