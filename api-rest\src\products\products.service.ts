import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreateProductDto } from './dto/create-product.dto';
import { GetProductsDto, ProductPaginator } from './dto/get-products.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { Product } from './entities/product.entity';
import { GetPopularProductsDto } from './dto/get-popular-products.dto';
import { GetBestSellingProductsDto } from './dto/get-best-selling-products.dto';
import { paginate } from '../common/pagination/paginate';
import Fuse from 'fuse.js';
import { Op } from 'sequelize';
import { Category } from 'src/categories/entities/category.entity';
import { Tag } from 'src/tags/entities/tag.entity';
import { Type } from 'src/types/entities/type.entity';
import { Shop } from 'src/shops/entities/shop.entity';
import { Op } from 'sequelize';

@Injectable()
export class ProductsService {
  constructor(
    @InjectModel(Product)
    private productModel: typeof Product,
  ) {}

  async create(createProductDto: CreateProductDto): Promise<Product> {
    return this.productModel.create({ ...createProductDto });
  }

  async getProducts({
    limit,
    page,
    search,
  }: GetProductsDto): Promise<ProductPaginator> {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const offset = (page - 1) * limit;

    const whereClause: any = {};

    if (search) {
      const parseSearchParams = search.split(';');
      for (const searchParam of parseSearchParams) {
        const [key, value] = searchParam.split(':');
        if (key === 'name') {
          whereClause.name = {
            [Op.iLike]: `%${value}%`,
          };
        } else if (key === 'shop_id') {
          whereClause.shop_id = parseInt(value, 10);
        } else if (key in this.productModel.rawAttributes) {
          whereClause[key] = value;
        }
      }
    }

    const { count, rows } = await this.productModel.findAndCountAll({
      where: whereClause,
      include: [
        { model: Category, as: 'categories' },
        { model: Tag, as: 'tags' },
        { model: Type, as: 'type' },
        { model: Shop, as: 'shop' },
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']],
    });

    const url = `/products?search=${search}&limit=${limit}`;
    const totalPages = Math.ceil(count / limit);

    return {
      data: rows,
      count,
      current_page: page,
      firstItem: offset + 1,
      lastItem: Math.min(offset + limit, count),
      last_page: totalPages,
      per_page: limit,
      total: count,
      first_page_url: `${url}&page=1`,
      last_page_url: `${url}&page=${totalPages}`,
      next_page_url: page < totalPages ? `${url}&page=${page + 1}` : null,
      prev_page_url: page > 1 ? `${url}&page=${page - 1}` : null,
    };
  }

  async getProductBySlug(slug: string): Promise<Product> {
    const product = await this.productModel.findOne({
      where: { slug },
      include: [
        { model: Category, as: 'categories' },
        { model: Tag, as: 'tags' },
        { model: Type, as: 'type' },
        { model: Shop, as: 'shop' },
      ],
    });

    if (product) {
      const related_products = await this.productModel.findAll({
        where: { type_id: product.type_id },
        limit: 20,
        include: [{ model: Type, as: 'type' }],
      });

      // Note: Sequelize doesn't support dynamic properties like this
      // You might want to return this as a separate field or handle it differently
      (product as any).related_products = related_products;
    }

    return product;
  }

  async getPopularProducts({
    limit,
    type_slug,
  }: GetPopularProductsDto): Promise<Product[]> {
    const whereClause: any = {};

    if (type_slug) {
      // Join with Type table to filter by slug
      const typeRecord = await this.productModel.sequelize.models.Type.findOne({
        where: { slug: type_slug },
      });
      if (typeRecord) {
        whereClause.type_id = (typeRecord as any).id;
      }
    }

    return this.productModel.findAll({
      where: whereClause,
      include: [
        { model: Type, as: 'type' },
        { model: Shop, as: 'shop' },
      ],
      limit,
      order: [['ratings', 'DESC']], // Assuming popular means high ratings
    });
  }
  async getBestSellingProducts({
    limit,
    type_slug,
  }: GetBestSellingProductsDto): Promise<Product[]> {
    const whereClause: any = {};

    if (type_slug) {
      const typeRecord = await this.productModel.sequelize.models.Type.findOne({
        where: { slug: type_slug },
      });
      if (typeRecord) {
        whereClause.type_id = (typeRecord as any).id;
      }
    }

    return this.productModel.findAll({
      where: whereClause,
      include: [
        { model: Type, as: 'type' },
        { model: Shop, as: 'shop' },
      ],
      limit,
      order: [['quantity', 'ASC']], // Assuming best selling means low stock
    });
  }

  getProductsStock({ limit, page, search }: GetProductsDto): ProductPaginator {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    let data: Product[] = this.products.filter((item) => item.quantity <= 9);

    if (search) {
      const parseSearchParams = search.split(';');
      const searchText: any = [];
      for (const searchParam of parseSearchParams) {
        const [key, value] = searchParam.split(':');
        // TODO: Temp Solution
        if (key !== 'slug') {
          searchText.push({
            [key]: value,
          });
        }
      }

      data = fuse
        .search({
          $and: searchText,
        })
        ?.map(({ item }) => item);
    }

    const results = data.slice(startIndex, endIndex);
    const url = `/products-stock?search=${search}&limit=${limit}`;
    return {
      data: results,
      ...paginate(data.length, page, limit, results.length, url),
    };
  }

  getDraftProducts({ limit, page, search }: GetProductsDto): ProductPaginator {
    if (!page) page = 1;
    if (!limit) limit = 30;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    let data: Product[] = this.products.filter(
      (item) => item.status === 'draft',
    );

    if (search) {
      const parseSearchParams = search.split(';');
      const searchText: any = [];
      for (const searchParam of parseSearchParams) {
        const [key, value] = searchParam.split(':');
        // TODO: Temp Solution
        if (key !== 'slug') {
          searchText.push({
            [key]: value,
          });
        }
      }

      data = fuse
        .search({
          $and: searchText,
        })
        ?.map(({ item }) => item);
    }

    const results = data.slice(startIndex, endIndex);
    const url = `/draft-products?search=${search}&limit=${limit}`;
    return {
      data: results,
      ...paginate(data.length, page, limit, results.length, url),
    };
  }

  async update(
    id: number,
    updateProductDto: UpdateProductDto,
  ): Promise<[number, Product[]]> {
    const [affectedCount, updatedProducts] = await this.productModel.update(
      { ...updateProductDto },
      { where: { id }, returning: true },
    );
    return [affectedCount, updatedProducts];
  }

  async remove(id: number): Promise<number> {
    return this.productModel.destroy({ where: { id } });
  }
}
