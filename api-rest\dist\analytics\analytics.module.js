"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsModule = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const analytics_controller_1 = require("./analytics.controller");
const analytics_service_1 = require("./analytics.service");
const analytics_entity_1 = require("./entities/analytics.entity");
const total_year_sale_by_month_entity_1 = require("./entities/total-year-sale-by-month.entity");
const category_wise_product_entity_1 = require("./entities/category-wise-product.entity");
const top_rate_product_entity_1 = require("./entities/top-rate-product.entity");
const product_entity_1 = require("../products/entities/product.entity");
let AnalyticsModule = class AnalyticsModule {
};
AnalyticsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            sequelize_1.SequelizeModule.forFeature([
                analytics_entity_1.Analytics,
                total_year_sale_by_month_entity_1.TotalYearSaleByMonth,
                category_wise_product_entity_1.CategoryWiseProduct,
                top_rate_product_entity_1.TopRateProduct,
                product_entity_1.Product,
            ]),
        ],
        controllers: [
            analytics_controller_1.AnalyticsController,
            analytics_controller_1.CategoryWiseProductController,
            analytics_controller_1.LowStockProductsController,
            analytics_controller_1.TopRateProductController,
        ],
        providers: [analytics_service_1.AnalyticsService],
    })
], AnalyticsModule);
exports.AnalyticsModule = AnalyticsModule;
//# sourceMappingURL=analytics.module.js.map