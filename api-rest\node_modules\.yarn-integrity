{"systemParams": "win32-x64-115", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@nestjs/cli@^9.0.0", "@nestjs/common@^9.0.11", "@nestjs/config@^2.2.0", "@nestjs/core@^9.0.11", "@nestjs/mapped-types@1.1.0", "@nestjs/platform-express@^9.0.11", "@nestjs/schematics@^9.0.1", "@nestjs/sequelize@^11.0.0", "@nestjs/swagger@^6.0.5", "@nestjs/testing@^9.0.11", "@paypal/checkout-server-sdk@^1.0.3", "@types/express@^4.17.13", "@types/jest@^28.1.7", "@types/multer@^1.4.7", "@types/node@^18.7.6", "@types/sequelize@^4.28.20", "@types/supertest@^2.0.12", "@typescript-eslint/eslint-plugin@^5.33.1", "@typescript-eslint/parser@^5.33.1", "axios@^1.2.1", "class-transformer@^0.5.1", "class-validator@^0.13.2", "eslint-config-prettier@^8.5.0", "eslint-plugin-prettier@^4.2.1", "eslint@^8.22.0", "fuse.js@^6.6.2", "jest@28.1.3", "nestjs-stripe@^1.0.0", "paypal-rest-sdk@^1.8.1", "pg-hstore@^2.3.4", "pg@^8.16.0", "prettier@^2.7.1", "qs@^6.11.0", "reflect-metadata@^0.1.13", "rimraf@^3.0.2", "rxjs@^7.5.6", "sequelize-cli@^6.6.3", "sequelize-typescript@^2.1.6", "sequelize@^6.37.7", "sqlite3@^5.1.7", "stripe@^11.2.0", "supertest@^6.2.4", "ts-jest@^28.0.8", "ts-loader@^9.3.1", "ts-node@^10.9.1", "tsconfig-paths@^4.1.0", "typescript@^4.7.4"], "lockfileEntries": {"@ampproject/remapping@^2.1.0": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.0.tgz", "@angular-devkit/core@14.0.5": "https://registry.npmjs.org/@angular-devkit/core/-/core-14.0.5.tgz", "@angular-devkit/schematics-cli@14.0.5": "https://registry.npmjs.org/@angular-devkit/schematics-cli/-/schematics-cli-14.0.5.tgz", "@angular-devkit/schematics@14.0.5": "https://registry.npmjs.org/@angular-devkit/schematics/-/schematics-14.0.5.tgz", "@babel/code-frame@^7.0.0": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.14.5.tgz", "@babel/code-frame@^7.12.13": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.14.5.tgz", "@babel/code-frame@^7.14.5": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.14.5.tgz", "@babel/code-frame@^7.16.7": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.18.6.tgz", "@babel/code-frame@^7.18.6": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.18.6.tgz", "@babel/compat-data@^7.18.8": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.18.8.tgz", "@babel/core@^7.11.6": "https://registry.npmjs.org/@babel/core/-/core-7.18.10.tgz", "@babel/core@^7.12.3": "https://registry.npmjs.org/@babel/core/-/core-7.18.10.tgz", "@babel/generator@^7.15.0": "https://registry.npmjs.org/@babel/generator/-/generator-7.15.0.tgz", "@babel/generator@^7.18.10": "https://registry.npmjs.org/@babel/generator/-/generator-7.18.12.tgz", "@babel/generator@^7.7.2": "https://registry.npmjs.org/@babel/generator/-/generator-7.15.0.tgz", "@babel/helper-compilation-targets@^7.18.9": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.18.9.tgz", "@babel/helper-environment-visitor@^7.18.9": "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz", "@babel/helper-function-name@^7.14.5": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.14.5.tgz", "@babel/helper-function-name@^7.18.9": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.18.9.tgz", "@babel/helper-get-function-arity@^7.14.5": "https://registry.npmjs.org/@babel/helper-get-function-arity/-/helper-get-function-arity-7.14.5.tgz", "@babel/helper-hoist-variables@^7.14.5": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.14.5.tgz", "@babel/helper-hoist-variables@^7.18.6": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz", "@babel/helper-module-imports@^7.18.6": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "@babel/helper-module-transforms@^7.18.9": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.18.9.tgz", "@babel/helper-plugin-utils@^7.0.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "@babel/helper-plugin-utils@^7.10.4": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "@babel/helper-plugin-utils@^7.12.13": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "@babel/helper-plugin-utils@^7.14.5": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "@babel/helper-plugin-utils@^7.8.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "@babel/helper-simple-access@^7.18.6": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.18.6.tgz", "@babel/helper-split-export-declaration@^7.14.5": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.14.5.tgz", "@babel/helper-split-export-declaration@^7.18.6": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz", "@babel/helper-string-parser@^7.18.10": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.18.10.tgz", "@babel/helper-validator-identifier@^7.14.5": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.14.9.tgz", "@babel/helper-validator-identifier@^7.14.9": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.14.9.tgz", "@babel/helper-validator-identifier@^7.18.6": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.18.6.tgz", "@babel/helper-validator-option@^7.18.6": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz", "@babel/helpers@^7.18.9": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.18.9.tgz", "@babel/highlight@^7.14.5": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.14.5.tgz", "@babel/highlight@^7.18.6": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.18.6.tgz", "@babel/parser@^7.1.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.15.3.tgz", "@babel/parser@^7.14.5": "https://registry.npmjs.org/@babel/parser/-/parser-7.15.3.tgz", "@babel/parser@^7.14.7": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.11.tgz", "@babel/parser@^7.15.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.15.3.tgz", "@babel/parser@^7.18.10": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.11.tgz", "@babel/parser@^7.18.11": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.11.tgz", "@babel/plugin-syntax-async-generators@^7.8.4": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "@babel/plugin-syntax-bigint@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "@babel/plugin-syntax-class-properties@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "@babel/plugin-syntax-import-meta@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "@babel/plugin-syntax-json-strings@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "@babel/plugin-syntax-logical-assignment-operators@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-syntax-numeric-separator@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "@babel/plugin-syntax-object-rest-spread@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "@babel/plugin-syntax-optional-catch-binding@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "@babel/plugin-syntax-optional-chaining@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "@babel/plugin-syntax-top-level-await@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "@babel/plugin-syntax-typescript@^7.7.2": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.14.5.tgz", "@babel/template@^7.14.5": "https://registry.npmjs.org/@babel/template/-/template-7.14.5.tgz", "@babel/template@^7.18.10": "https://registry.npmjs.org/@babel/template/-/template-7.18.10.tgz", "@babel/template@^7.18.6": "https://registry.npmjs.org/@babel/template/-/template-7.18.10.tgz", "@babel/template@^7.3.3": "https://registry.npmjs.org/@babel/template/-/template-7.14.5.tgz", "@babel/traverse@^7.18.10": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.18.11.tgz", "@babel/traverse@^7.18.9": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.18.11.tgz", "@babel/traverse@^7.7.2": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.15.0.tgz", "@babel/types@^7.0.0": "https://registry.npmjs.org/@babel/types/-/types-7.15.0.tgz", "@babel/types@^7.14.5": "https://registry.npmjs.org/@babel/types/-/types-7.15.0.tgz", "@babel/types@^7.15.0": "https://registry.npmjs.org/@babel/types/-/types-7.15.0.tgz", "@babel/types@^7.18.10": "https://registry.npmjs.org/@babel/types/-/types-7.18.10.tgz", "@babel/types@^7.18.6": "https://registry.npmjs.org/@babel/types/-/types-7.18.10.tgz", "@babel/types@^7.18.9": "https://registry.npmjs.org/@babel/types/-/types-7.18.10.tgz", "@babel/types@^7.3.0": "https://registry.npmjs.org/@babel/types/-/types-7.15.0.tgz", "@babel/types@^7.3.3": "https://registry.npmjs.org/@babel/types/-/types-7.15.0.tgz", "@bcoe/v8-coverage@^0.2.3": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz", "@colors/colors@1.5.0": "https://registry.npmjs.org/@colors/colors/-/colors-1.5.0.tgz", "@cspotcode/source-map-support@^0.8.0": "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz", "@eslint/eslintrc@^1.3.0": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.3.0.tgz", "@gar/promisify@^1.0.1": "https://registry.npmjs.org/@gar/promisify/-/promisify-1.1.3.tgz", "@humanwhocodes/config-array@^0.10.4": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.10.4.tgz", "@humanwhocodes/gitignore-to-minimatch@^1.0.2": "https://registry.npmjs.org/@humanwhocodes/gitignore-to-minimatch/-/gitignore-to-minimatch-1.0.2.tgz", "@humanwhocodes/object-schema@^1.2.1": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz", "@isaacs/cliui@^8.0.2": "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz", "@istanbuljs/load-nyc-config@^1.0.0": "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz", "@istanbuljs/schema@^0.1.2": "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz", "@jest/console@^28.1.3": "https://registry.npmjs.org/@jest/console/-/console-28.1.3.tgz", "@jest/core@^28.1.3": "https://registry.npmjs.org/@jest/core/-/core-28.1.3.tgz", "@jest/environment@^28.1.3": "https://registry.npmjs.org/@jest/environment/-/environment-28.1.3.tgz", "@jest/expect-utils@^28.1.3": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.1.3.tgz", "@jest/expect@^28.1.3": "https://registry.npmjs.org/@jest/expect/-/expect-28.1.3.tgz", "@jest/fake-timers@^28.1.3": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.1.3.tgz", "@jest/globals@^28.1.3": "https://registry.npmjs.org/@jest/globals/-/globals-28.1.3.tgz", "@jest/reporters@^28.1.3": "https://registry.npmjs.org/@jest/reporters/-/reporters-28.1.3.tgz", "@jest/schemas@^28.1.3": "https://registry.npmjs.org/@jest/schemas/-/schemas-28.1.3.tgz", "@jest/source-map@^28.1.2": "https://registry.npmjs.org/@jest/source-map/-/source-map-28.1.2.tgz", "@jest/test-result@^28.1.3": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.1.3.tgz", "@jest/test-sequencer@^28.1.3": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.1.3.tgz", "@jest/transform@^28.1.3": "https://registry.npmjs.org/@jest/transform/-/transform-28.1.3.tgz", "@jest/types@^28.1.3": "https://registry.npmjs.org/@jest/types/-/types-28.1.3.tgz", "@jridgewell/gen-mapping@^0.1.0": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz", "@jridgewell/gen-mapping@^0.3.2": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "@jridgewell/resolve-uri@^3.0.3": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "@jridgewell/set-array@^1.0.0": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "@jridgewell/set-array@^1.0.1": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "@jridgewell/trace-mapping@0.3.9": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz", "@jridgewell/trace-mapping@^0.3.12": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.15.tgz", "@jridgewell/trace-mapping@^0.3.13": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.15.tgz", "@jridgewell/trace-mapping@^0.3.9": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.15.tgz", "@nestjs/cli@^9.0.0": "https://registry.npmjs.org/@nestjs/cli/-/cli-9.0.0.tgz", "@nestjs/common@^9.0.11": "https://registry.npmjs.org/@nestjs/common/-/common-9.0.11.tgz", "@nestjs/config@^2.2.0": "https://registry.npmjs.org/@nestjs/config/-/config-2.2.0.tgz", "@nestjs/core@^9.0.11": "https://registry.npmjs.org/@nestjs/core/-/core-9.0.11.tgz", "@nestjs/mapped-types@1.1.0": "https://registry.npmjs.org/@nestjs/mapped-types/-/mapped-types-1.1.0.tgz", "@nestjs/platform-express@^9.0.11": "https://registry.npmjs.org/@nestjs/platform-express/-/platform-express-9.0.11.tgz", "@nestjs/schematics@^9.0.0": "https://registry.npmjs.org/@nestjs/schematics/-/schematics-9.0.1.tgz", "@nestjs/schematics@^9.0.1": "https://registry.npmjs.org/@nestjs/schematics/-/schematics-9.0.1.tgz", "@nestjs/sequelize@^11.0.0": "https://registry.npmjs.org/@nestjs/sequelize/-/sequelize-11.0.0.tgz", "@nestjs/swagger@^6.0.5": "https://registry.npmjs.org/@nestjs/swagger/-/swagger-6.0.5.tgz", "@nestjs/testing@^9.0.11": "https://registry.npmjs.org/@nestjs/testing/-/testing-9.0.11.tgz", "@nodelib/fs.scandir@2.1.5": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "@nodelib/fs.stat@2.0.5": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.stat@^2.0.2": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.walk@^1.2.3": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@npmcli/fs@^1.0.0": "https://registry.npmjs.org/@npmcli/fs/-/fs-1.1.1.tgz", "@npmcli/move-file@^1.0.1": "https://registry.npmjs.org/@npmcli/move-file/-/move-file-1.1.2.tgz", "@nuxtjs/opencollective@0.3.2": "https://registry.npmjs.org/@nuxtjs/opencollective/-/opencollective-0.3.2.tgz", "@one-ini/wasm@0.1.1": "https://registry.npmjs.org/@one-ini/wasm/-/wasm-0.1.1.tgz", "@paypal/checkout-server-sdk@^1.0.3": "https://registry.npmjs.org/@paypal/checkout-server-sdk/-/checkout-server-sdk-1.0.3.tgz", "@paypal/paypalhttp@^1.0.1": "https://registry.npmjs.org/@paypal/paypalhttp/-/paypalhttp-1.0.1.tgz", "@pkgjs/parseargs@^0.11.0": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "@sinclair/typebox@^0.24.1": "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.24.28.tgz", "@sinonjs/commons@^1.7.0": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.3.tgz", "@sinonjs/fake-timers@^9.1.2": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-9.1.2.tgz", "@tootallnate/once@1": "https://registry.npmjs.org/@tootallnate/once/-/once-1.1.2.tgz", "@tsconfig/node10@^1.0.7": "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.8.tgz", "@tsconfig/node12@^1.0.7": "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.9.tgz", "@tsconfig/node14@^1.0.0": "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.1.tgz", "@tsconfig/node16@^1.0.2": "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.2.tgz", "@types/babel__core@^7.1.14": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.15.tgz", "@types/babel__generator@*": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.3.tgz", "@types/babel__template@*": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.1.tgz", "@types/babel__traverse@*": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.14.2.tgz", "@types/babel__traverse@^7.0.6": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.14.2.tgz", "@types/bluebird@*": "https://registry.npmjs.org/@types/bluebird/-/bluebird-3.5.42.tgz", "@types/body-parser@*": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.1.tgz", "@types/connect@*": "https://registry.npmjs.org/@types/connect/-/connect-3.4.35.tgz", "@types/continuation-local-storage@*": "https://registry.npmjs.org/@types/continuation-local-storage/-/continuation-local-storage-3.2.7.tgz", "@types/cookiejar@*": "https://registry.npmjs.org/@types/cookiejar/-/cookiejar-2.1.2.tgz", "@types/debug@^4.1.8": "https://registry.npmjs.org/@types/debug/-/debug-4.1.12.tgz", "@types/eslint-scope@^3.7.3": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.4.tgz", "@types/eslint@*": "https://registry.npmjs.org/@types/eslint/-/eslint-7.28.0.tgz", "@types/estree@*": "https://registry.npmjs.org/@types/estree/-/estree-0.0.50.tgz", "@types/estree@^0.0.51": "https://registry.npmjs.org/@types/estree/-/estree-0.0.51.tgz", "@types/express-serve-static-core@^4.17.18": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.24.tgz", "@types/express@*": "https://registry.npmjs.org/@types/express/-/express-4.17.13.tgz", "@types/express@^4.17.13": "https://registry.npmjs.org/@types/express/-/express-4.17.13.tgz", "@types/graceful-fs@^4.1.3": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.5.tgz", "@types/istanbul-lib-coverage@*": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.3.tgz", "@types/istanbul-lib-coverage@^2.0.0": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.3.tgz", "@types/istanbul-lib-coverage@^2.0.1": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.3.tgz", "@types/istanbul-lib-report@*": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz", "@types/istanbul-reports@^3.0.0": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz", "@types/jest@^28.1.7": "https://registry.npmjs.org/@types/jest/-/jest-28.1.7.tgz", "@types/json-schema@*": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.9.tgz", "@types/json-schema@^7.0.8": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.9.tgz", "@types/json-schema@^7.0.9": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.9.tgz", "@types/json5@^0.0.29": "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz", "@types/lodash@*": "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.17.tgz", "@types/mime@^1": "https://registry.npmjs.org/@types/mime/-/mime-1.3.2.tgz", "@types/ms@*": "https://registry.npmjs.org/@types/ms/-/ms-2.1.0.tgz", "@types/multer@^1.4.7": "https://registry.npmjs.org/@types/multer/-/multer-1.4.7.tgz", "@types/node@*": "https://registry.npmjs.org/@types/node/-/node-16.7.9.tgz", "@types/node@>=8.1.0": "https://registry.npmjs.org/@types/node/-/node-18.11.11.tgz", "@types/node@^18.7.6": "https://registry.npmjs.org/@types/node/-/node-18.7.6.tgz", "@types/parse-json@^4.0.0": "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.0.tgz", "@types/prettier@^2.1.5": "https://registry.npmjs.org/@types/prettier/-/prettier-2.3.2.tgz", "@types/qs@*": "https://registry.npmjs.org/@types/qs/-/qs-6.9.7.tgz", "@types/range-parser@*": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.4.tgz", "@types/sequelize@^4.28.20": "https://registry.npmjs.org/@types/sequelize/-/sequelize-4.28.20.tgz", "@types/serve-static@*": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.10.tgz", "@types/stack-utils@^2.0.0": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.1.tgz", "@types/superagent@*": "https://registry.npmjs.org/@types/superagent/-/superagent-4.1.12.tgz", "@types/supertest@^2.0.12": "https://registry.npmjs.org/@types/supertest/-/supertest-2.0.12.tgz", "@types/validator@*": "https://registry.npmjs.org/@types/validator/-/validator-13.15.1.tgz", "@types/validator@^13.7.17": "https://registry.npmjs.org/@types/validator/-/validator-13.15.1.tgz", "@types/yargs-parser@*": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-20.2.1.tgz", "@types/yargs@^17.0.8": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.11.tgz", "@typescript-eslint/eslint-plugin@^5.33.1": "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.33.1.tgz", "@typescript-eslint/parser@^5.33.1": "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-5.33.1.tgz", "@typescript-eslint/scope-manager@5.33.1": "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-5.33.1.tgz", "@typescript-eslint/type-utils@5.33.1": "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-5.33.1.tgz", "@typescript-eslint/types@5.33.1": "https://registry.npmjs.org/@typescript-eslint/types/-/types-5.33.1.tgz", "@typescript-eslint/typescript-estree@5.33.1": "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-5.33.1.tgz", "@typescript-eslint/utils@5.33.1": "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-5.33.1.tgz", "@typescript-eslint/visitor-keys@5.33.1": "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-5.33.1.tgz", "@webassemblyjs/ast@1.11.1": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.11.1.tgz", "@webassemblyjs/floating-point-hex-parser@1.11.1": "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.1.tgz", "@webassemblyjs/helper-api-error@1.11.1": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.1.tgz", "@webassemblyjs/helper-buffer@1.11.1": "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.11.1.tgz", "@webassemblyjs/helper-numbers@1.11.1": "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.1.tgz", "@webassemblyjs/helper-wasm-bytecode@1.11.1": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.1.tgz", "@webassemblyjs/helper-wasm-section@1.11.1": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.1.tgz", "@webassemblyjs/ieee754@1.11.1": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.1.tgz", "@webassemblyjs/leb128@1.11.1": "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.11.1.tgz", "@webassemblyjs/utf8@1.11.1": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.1.tgz", "@webassemblyjs/wasm-edit@1.11.1": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.1.tgz", "@webassemblyjs/wasm-gen@1.11.1": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.1.tgz", "@webassemblyjs/wasm-opt@1.11.1": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.1.tgz", "@webassemblyjs/wasm-parser@1.11.1": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.1.tgz", "@webassemblyjs/wast-printer@1.11.1": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.11.1.tgz", "@xtuc/ieee754@^1.2.0": "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "@xtuc/long@4.2.2": "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz", "abbrev@1": "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz", "abbrev@^2.0.0": "https://registry.npmjs.org/abbrev/-/abbrev-2.0.0.tgz", "accepts@~1.3.8": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "acorn-import-assertions@^1.7.6": "https://registry.npmjs.org/acorn-import-assertions/-/acorn-import-assertions-1.8.0.tgz", "acorn-jsx@^5.3.2": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "acorn-walk@^8.1.1": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.1.1.tgz", "acorn@^8.4.1": "https://registry.npmjs.org/acorn/-/acorn-8.4.1.tgz", "acorn@^8.8.0": "https://registry.npmjs.org/acorn/-/acorn-8.8.0.tgz", "agent-base@6": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz", "agent-base@^6.0.2": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz", "agentkeepalive@^4.1.3": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.6.0.tgz", "aggregate-error@^3.0.0": "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz", "ajv-formats@2.1.1": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz", "ajv-keywords@^3.5.2": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv@8.11.0": "https://registry.npmjs.org/ajv/-/ajv-8.11.0.tgz", "ajv@^6.10.0": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.4": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.5": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^8.0.0": "https://registry.npmjs.org/ajv/-/ajv-8.6.2.tgz", "ansi-colors@4.1.1": "https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.1.tgz", "ansi-escapes@^4.2.1": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-regex@^5.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-regex@^6.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz", "ansi-styles@^3.2.1": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "ansi-styles@^4.0.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^5.0.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz", "ansi-styles@^6.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz", "anymatch@^3.0.3": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz", "anymatch@~3.1.2": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz", "append-field@^1.0.0": "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz", "aproba@^1.0.3 || ^2.0.0": "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz", "are-we-there-yet@^3.0.0": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-3.0.1.tgz", "arg@^4.1.0": "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz", "argparse@^1.0.7": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "argparse@^2.0.1": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "array-flatten@1.1.1": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "array-union@^2.1.0": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz", "asap@^2.0.0": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz", "asynckit@^0.4.0": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "at-least-node@^1.0.0": "https://registry.npmjs.org/at-least-node/-/at-least-node-1.0.0.tgz", "axios@^1.2.1": "https://registry.npmjs.org/axios/-/axios-1.2.1.tgz", "babel-jest@^28.1.3": "https://registry.npmjs.org/babel-jest/-/babel-jest-28.1.3.tgz", "babel-plugin-istanbul@^6.1.1": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "babel-plugin-jest-hoist@^28.1.3": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-28.1.3.tgz", "babel-preset-current-node-syntax@^1.0.0": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz", "babel-preset-jest@^28.1.3": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-28.1.3.tgz", "balanced-match@^1.0.0": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "base64-js@^1.3.1": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "binary-extensions@^2.0.0": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz", "bindings@^1.5.0": "https://registry.npmjs.org/bindings/-/bindings-1.5.0.tgz", "bl@^4.0.3": "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz", "bl@^4.1.0": "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz", "bluebird@^3.7.2": "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz", "body-parser@1.20.0": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.0.tgz", "brace-expansion@^1.1.7": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "brace-expansion@^2.0.1": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz", "braces@^3.0.1": "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz", "braces@~3.0.2": "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz", "browserslist@^4.14.5": "https://registry.npmjs.org/browserslist/-/browserslist-4.16.8.tgz", "browserslist@^4.20.2": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.3.tgz", "bs-logger@0.x": "https://registry.npmjs.org/bs-logger/-/bs-logger-0.2.6.tgz", "bser@2.1.1": "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz", "buffer-crc32@^0.2.3": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "buffer-from@^1.0.0": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "buffer@^5.5.0": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "busboy@^1.0.0": "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz", "bytes@3.1.2": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "cacache@^15.2.0": "https://registry.npmjs.org/cacache/-/cacache-15.3.0.tgz", "call-bind@^1.0.0": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz", "callsites@^3.0.0": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "camelcase@^5.3.1": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "camelcase@^6.2.0": "https://registry.npmjs.org/camelcase/-/camelcase-6.2.0.tgz", "caniuse-lite@^1.0.30001251": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001252.tgz", "caniuse-lite@^1.0.30001370": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001377.tgz", "chalk@3.0.0": "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz", "chalk@^2.0.0": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chalk@^4.0.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.1": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.2": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "char-regex@^1.0.2": "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz", "chardet@^0.7.0": "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz", "chokidar@3.5.3": "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz", "chokidar@^3.5.3": "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz", "chownr@^1.1.1": "https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz", "chownr@^2.0.0": "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz", "chrome-trace-event@^1.0.2": "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz", "ci-info@^3.2.0": "https://registry.npmjs.org/ci-info/-/ci-info-3.2.0.tgz", "cjs-module-lexer@^1.0.0": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.2.2.tgz", "class-transformer@^0.5.1": "https://registry.npmjs.org/class-transformer/-/class-transformer-0.5.1.tgz", "class-validator@^0.13.2": "https://registry.npmjs.org/class-validator/-/class-validator-0.13.2.tgz", "clean-stack@^2.0.0": "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz", "cli-cursor@^3.1.0": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz", "cli-spinners@^2.5.0": "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.6.0.tgz", "cli-table3@0.6.2": "https://registry.npmjs.org/cli-table3/-/cli-table3-0.6.2.tgz", "cli-width@^3.0.0": "https://registry.npmjs.org/cli-width/-/cli-width-3.0.0.tgz", "cliui@^7.0.2": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "clone@^1.0.2": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "co@^4.6.0": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "collect-v8-coverage@^1.0.0": "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.1.tgz", "color-convert@^1.9.0": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "color-convert@^2.0.1": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "color-name@1.1.3": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "color-name@~1.1.4": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "color-support@^1.1.3": "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz", "colorette@^1.3.0": "https://registry.npmjs.org/colorette/-/colorette-1.3.0.tgz", "combined-stream@^1.0.8": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "commander@4.1.1": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz", "commander@^10.0.0": "https://registry.npmjs.org/commander/-/commander-10.0.1.tgz", "commander@^2.20.0": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "component-emitter@^1.3.0": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.0.tgz", "concat-map@0.0.1": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "concat-stream@^1.5.2": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "config-chain@^1.1.13": "https://registry.npmjs.org/config-chain/-/config-chain-1.1.13.tgz", "consola@^2.15.0": "https://registry.npmjs.org/consola/-/consola-2.15.3.tgz", "console-control-strings@^1.1.0": "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz", "content-disposition@0.5.4": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz", "content-type@~1.0.4": "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz", "convert-source-map@^1.4.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.8.0.tgz", "convert-source-map@^1.6.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.8.0.tgz", "convert-source-map@^1.7.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.8.0.tgz", "cookie-signature@1.0.6": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "cookie@0.5.0": "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz", "cookiejar@^2.1.3": "https://registry.npmjs.org/cookiejar/-/cookiejar-2.1.3.tgz", "core-util-is@~1.0.0": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz", "cors@2.8.5": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "cosmiconfig@^7.0.1": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.0.1.tgz", "create-require@^1.1.0": "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz", "cross-spawn@^7.0.0": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.2": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.3": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.6": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "debug@2.6.9": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@4": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.1.0": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.1.1": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.2": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.3": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.4": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "decompress-response@^6.0.0": "https://registry.npmjs.org/decompress-response/-/decompress-response-6.0.0.tgz", "dedent@^0.7.0": "https://registry.npmjs.org/dedent/-/dedent-0.7.0.tgz", "deep-extend@^0.6.0": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz", "deep-is@^0.1.3": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz", "deepmerge@^4.2.2": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.2.2.tgz", "defaults@^1.0.3": "https://registry.npmjs.org/defaults/-/defaults-1.0.3.tgz", "delayed-stream@~1.0.0": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "delegates@^1.0.0": "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz", "depd@2.0.0": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "destroy@1.2.0": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "detect-libc@^2.0.0": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz", "detect-newline@^3.0.0": "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz", "dezalgo@1.0.3": "https://registry.npmjs.org/dezalgo/-/dezalgo-1.0.3.tgz", "diff-sequences@^28.1.1": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-28.1.1.tgz", "diff@^4.0.1": "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz", "dir-glob@^3.0.1": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz", "doctrine@^3.0.0": "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz", "dotenv-expand@8.0.3": "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-8.0.3.tgz", "dotenv@16.0.1": "https://registry.npmjs.org/dotenv/-/dotenv-16.0.1.tgz", "dottie@^2.0.6": "https://registry.npmjs.org/dottie/-/dottie-2.0.6.tgz", "eastasianwidth@^0.2.0": "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "editorconfig@^1.0.4": "https://registry.npmjs.org/editorconfig/-/editorconfig-1.0.4.tgz", "ee-first@1.1.1": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "electron-to-chromium@^1.3.811": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.3.824.tgz", "electron-to-chromium@^1.4.202": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.222.tgz", "emittery@^0.10.2": "https://registry.npmjs.org/emittery/-/emittery-0.10.2.tgz", "emoji-regex@^8.0.0": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "emoji-regex@^9.2.2": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz", "encodeurl@~1.0.2": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "encoding@^0.1.12": "https://registry.npmjs.org/encoding/-/encoding-0.1.13.tgz", "end-of-stream@^1.1.0": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz", "end-of-stream@^1.4.1": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz", "enhanced-resolve@^5.0.0": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.8.2.tgz", "enhanced-resolve@^5.7.0": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.8.2.tgz", "enhanced-resolve@^5.9.3": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.10.0.tgz", "env-paths@^2.2.0": "https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz", "err-code@^2.0.2": "https://registry.npmjs.org/err-code/-/err-code-2.0.3.tgz", "error-ex@^1.3.1": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "es-module-lexer@^0.9.0": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.9.3.tgz", "escalade@^3.1.1": "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz", "escape-html@~1.0.3": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "escape-string-regexp@^1.0.5": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^2.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "escape-string-regexp@^4.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "eslint-config-prettier@^8.5.0": "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-8.5.0.tgz", "eslint-plugin-prettier@^4.2.1": "https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz", "eslint-scope@5.1.1": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-scope@^5.1.1": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-scope@^7.1.1": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.1.1.tgz", "eslint-utils@^3.0.0": "https://registry.npmjs.org/eslint-utils/-/eslint-utils-3.0.0.tgz", "eslint-visitor-keys@^2.0.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "eslint-visitor-keys@^3.3.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.3.0.tgz", "eslint@^8.22.0": "https://registry.npmjs.org/eslint/-/eslint-8.22.0.tgz", "espree@^9.3.2": "https://registry.npmjs.org/espree/-/espree-9.3.3.tgz", "espree@^9.3.3": "https://registry.npmjs.org/espree/-/espree-9.3.3.tgz", "esprima@^4.0.0": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "esquery@^1.4.0": "https://registry.npmjs.org/esquery/-/esquery-1.4.0.tgz", "esrecurse@^4.3.0": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz", "estraverse@^4.1.1": "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz", "estraverse@^5.1.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.2.0.tgz", "estraverse@^5.2.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.2.0.tgz", "esutils@^2.0.2": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz", "etag@~1.8.1": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "events@^3.2.0": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "execa@^4.0.2": "https://registry.npmjs.org/execa/-/execa-4.1.0.tgz", "execa@^5.0.0": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz", "exit@^0.1.2": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz", "expand-template@^2.0.3": "https://registry.npmjs.org/expand-template/-/expand-template-2.0.3.tgz", "expect@^28.0.0": "https://registry.npmjs.org/expect/-/expect-28.1.3.tgz", "expect@^28.1.3": "https://registry.npmjs.org/expect/-/expect-28.1.3.tgz", "express@4.18.1": "https://registry.npmjs.org/express/-/express-4.18.1.tgz", "external-editor@^3.0.3": "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz", "fast-deep-equal@^3.1.1": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-deep-equal@^3.1.3": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-diff@^1.1.2": "https://registry.npmjs.org/fast-diff/-/fast-diff-1.2.0.tgz", "fast-glob@^3.2.9": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.11.tgz", "fast-json-stable-stringify@2.x": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-json-stable-stringify@^2.0.0": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-levenshtein@^2.0.6": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "fast-safe-stringify@2.1.1": "https://registry.npmjs.org/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz", "fast-safe-stringify@^2.1.1": "https://registry.npmjs.org/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz", "fastq@^1.6.0": "https://registry.npmjs.org/fastq/-/fastq-1.12.0.tgz", "fb-watchman@^2.0.0": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.1.tgz", "figures@^3.0.0": "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz", "file-entry-cache@^6.0.1": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "file-uri-to-path@1.0.0": "https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz", "fill-range@^7.0.1": "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz", "finalhandler@1.2.0": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz", "find-up@^4.0.0": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "find-up@^4.1.0": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "find-up@^5.0.0": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz", "flat-cache@^3.0.4": "https://registry.npmjs.org/flat-cache/-/flat-cache-3.0.4.tgz", "flatted@^3.1.0": "https://registry.npmjs.org/flatted/-/flatted-3.2.2.tgz", "follow-redirects@^1.15.0": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.2.tgz", "foreground-child@^3.1.0": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz", "fork-ts-checker-webpack-plugin@7.2.11": "https://registry.npmjs.org/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-7.2.11.tgz", "form-data@^4.0.0": "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz", "formidable@^2.0.1": "https://registry.npmjs.org/formidable/-/formidable-2.0.1.tgz", "forwarded@0.2.0": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "fresh@0.5.2": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "fs-constants@^1.0.0": "https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz", "fs-extra@10.1.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "fs-extra@^10.0.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "fs-extra@^9.1.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "fs-minipass@^2.0.0": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz", "fs-monkey@^1.0.3": "https://registry.npmjs.org/fs-monkey/-/fs-monkey-1.0.3.tgz", "fs.realpath@^1.0.0": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@^2.3.2": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "fsevents@~2.3.2": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "function-bind@^1.1.1": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "function-bind@^1.1.2": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "functional-red-black-tree@^1.0.1": "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "fuse.js@^6.6.2": "https://registry.npmjs.org/fuse.js/-/fuse.js-6.6.2.tgz", "gauge@^4.0.3": "https://registry.npmjs.org/gauge/-/gauge-4.0.4.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "get-caller-file@^2.0.5": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "get-intrinsic@^1.0.2": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.1.tgz", "get-package-type@^0.1.0": "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz", "get-stream@^5.0.0": "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz", "get-stream@^6.0.0": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz", "github-from-package@0.0.0": "https://registry.npmjs.org/github-from-package/-/github-from-package-0.0.0.tgz", "glob-parent@^5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@^6.0.1": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz", "glob-parent@~5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob-to-regexp@^0.4.1": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz", "glob@7.2.0": "https://registry.npmjs.org/glob/-/glob-7.2.0.tgz", "glob@^10.4.2": "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz", "glob@^7.0.0": "https://registry.npmjs.org/glob/-/glob-7.1.7.tgz", "glob@^7.1.3": "https://registry.npmjs.org/glob/-/glob-7.1.7.tgz", "glob@^7.1.4": "https://registry.npmjs.org/glob/-/glob-7.1.7.tgz", "globals@^11.1.0": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "globals@^13.15.0": "https://registry.npmjs.org/globals/-/globals-13.17.0.tgz", "globby@^11.1.0": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz", "graceful-fs@^4.1.2": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.8.tgz", "graceful-fs@^4.1.6": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.8.tgz", "graceful-fs@^4.2.0": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.10.tgz", "graceful-fs@^4.2.4": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.8.tgz", "graceful-fs@^4.2.6": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.10.tgz", "graceful-fs@^4.2.9": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.10.tgz", "grapheme-splitter@^1.0.4": "https://registry.npmjs.org/grapheme-splitter/-/grapheme-splitter-1.0.4.tgz", "has-flag@^3.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "has-flag@^4.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "has-symbols@^1.0.1": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.2.tgz", "has-unicode@^2.0.1": "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz", "has@^1.0.3": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "hasown@^2.0.2": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "hexoid@1.0.0": "https://registry.npmjs.org/hexoid/-/hexoid-1.0.0.tgz", "html-escaper@^2.0.0": "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz", "http-cache-semantics@^4.1.0": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.2.0.tgz", "http-errors@2.0.0": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "http-proxy-agent@^4.0.1": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz", "https-proxy-agent@^5.0.0": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "human-signals@^1.1.1": "https://registry.npmjs.org/human-signals/-/human-signals-1.1.1.tgz", "human-signals@^2.1.0": "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz", "humanize-ms@^1.2.1": "https://registry.npmjs.org/humanize-ms/-/humanize-ms-1.2.1.tgz", "iconv-lite@0.4.24": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "iconv-lite@^0.4.24": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "iconv-lite@^0.6.2": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "ieee754@^1.1.13": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "ignore@^5.2.0": "https://registry.npmjs.org/ignore/-/ignore-5.2.0.tgz", "import-fresh@^3.0.0": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz", "import-fresh@^3.2.1": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz", "import-local@^3.0.2": "https://registry.npmjs.org/import-local/-/import-local-3.0.2.tgz", "imurmurhash@^0.1.4": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "indent-string@^4.0.0": "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz", "infer-owner@^1.0.4": "https://registry.npmjs.org/infer-owner/-/infer-owner-1.0.4.tgz", "inflection@^1.13.4": "https://registry.npmjs.org/inflection/-/inflection-1.13.4.tgz", "inflight@^1.0.4": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@2.0.4": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.4": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@~2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "ini@^1.3.4": "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz", "ini@~1.3.0": "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz", "inquirer@7.3.3": "https://registry.npmjs.org/inquirer/-/inquirer-7.3.3.tgz", "inquirer@8.2.4": "https://registry.npmjs.org/inquirer/-/inquirer-8.2.4.tgz", "interpret@^1.0.0": "https://registry.npmjs.org/interpret/-/interpret-1.4.0.tgz", "ip-address@^9.0.5": "https://registry.npmjs.org/ip-address/-/ip-address-9.0.5.tgz", "ipaddr.js@1.9.1": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "is-arrayish@^0.2.1": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "is-binary-path@~2.1.0": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "is-core-module@^2.16.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "is-extglob@^2.1.1": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "is-generator-fn@^2.0.0": "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "is-glob@^4.0.0": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.1.tgz", "is-glob@^4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.1.tgz", "is-glob@^4.0.3": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@~4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.1.tgz", "is-interactive@^1.0.0": "https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz", "is-lambda@^1.0.1": "https://registry.npmjs.org/is-lambda/-/is-lambda-1.0.1.tgz", "is-number@^7.0.0": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "is-stream@^2.0.0": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz", "is-unicode-supported@^0.1.0": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "isarray@~1.0.0": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "isexe@^2.0.0": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "istanbul-lib-coverage@^3.0.0": "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.0.0.tgz", "istanbul-lib-coverage@^3.2.0": "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.0.tgz", "istanbul-lib-instrument@^5.0.4": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.0.tgz", "istanbul-lib-instrument@^5.1.0": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.0.tgz", "istanbul-lib-report@^3.0.0": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz", "istanbul-lib-source-maps@^4.0.0": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.0.tgz", "istanbul-reports@^3.1.3": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.5.tgz", "iterare@1.2.1": "https://registry.npmjs.org/iterare/-/iterare-1.2.1.tgz", "jackspeak@^3.1.2": "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz", "jest-changed-files@^28.1.3": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-28.1.3.tgz", "jest-circus@^28.1.3": "https://registry.npmjs.org/jest-circus/-/jest-circus-28.1.3.tgz", "jest-cli@^28.1.3": "https://registry.npmjs.org/jest-cli/-/jest-cli-28.1.3.tgz", "jest-config@^28.1.3": "https://registry.npmjs.org/jest-config/-/jest-config-28.1.3.tgz", "jest-diff@^28.1.3": "https://registry.npmjs.org/jest-diff/-/jest-diff-28.1.3.tgz", "jest-docblock@^28.1.1": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-28.1.1.tgz", "jest-each@^28.1.3": "https://registry.npmjs.org/jest-each/-/jest-each-28.1.3.tgz", "jest-environment-node@^28.1.3": "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-28.1.3.tgz", "jest-get-type@^28.0.2": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-28.0.2.tgz", "jest-haste-map@^28.1.3": "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-28.1.3.tgz", "jest-leak-detector@^28.1.3": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.1.3.tgz", "jest-matcher-utils@^28.1.3": "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-28.1.3.tgz", "jest-message-util@^28.1.3": "https://registry.npmjs.org/jest-message-util/-/jest-message-util-28.1.3.tgz", "jest-mock@^28.1.3": "https://registry.npmjs.org/jest-mock/-/jest-mock-28.1.3.tgz", "jest-pnp-resolver@^1.2.2": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.2.tgz", "jest-regex-util@^28.0.2": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-28.0.2.tgz", "jest-resolve-dependencies@^28.1.3": "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-28.1.3.tgz", "jest-resolve@^28.1.3": "https://registry.npmjs.org/jest-resolve/-/jest-resolve-28.1.3.tgz", "jest-runner@^28.1.3": "https://registry.npmjs.org/jest-runner/-/jest-runner-28.1.3.tgz", "jest-runtime@^28.1.3": "https://registry.npmjs.org/jest-runtime/-/jest-runtime-28.1.3.tgz", "jest-snapshot@^28.1.3": "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-28.1.3.tgz", "jest-util@^28.0.0": "https://registry.npmjs.org/jest-util/-/jest-util-28.1.3.tgz", "jest-util@^28.1.3": "https://registry.npmjs.org/jest-util/-/jest-util-28.1.3.tgz", "jest-validate@^28.1.3": "https://registry.npmjs.org/jest-validate/-/jest-validate-28.1.3.tgz", "jest-watcher@^28.1.3": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.1.3.tgz", "jest-worker@^27.0.6": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.1.0.tgz", "jest-worker@^28.1.3": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.1.3.tgz", "jest@28.1.3": "https://registry.npmjs.org/jest/-/jest-28.1.3.tgz", "js-beautify@1.15.4": "https://registry.npmjs.org/js-beautify/-/js-beautify-1.15.4.tgz", "js-cookie@^3.0.5": "https://registry.npmjs.org/js-cookie/-/js-cookie-3.0.5.tgz", "js-tokens@^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-yaml@4.1.0": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "js-yaml@^3.13.1": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz", "js-yaml@^4.1.0": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "jsbn@1.1.0": "https://registry.npmjs.org/jsbn/-/jsbn-1.1.0.tgz", "jsesc@^2.5.1": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz", "json-parse-even-better-errors@^2.3.0": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "json-parse-even-better-errors@^2.3.1": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "json-schema-traverse@^0.4.1": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "json-schema-traverse@^1.0.0": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "json-stable-stringify-without-jsonify@^1.0.1": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "json5@^1.0.1": "https://registry.npmjs.org/json5/-/json5-1.0.1.tgz", "json5@^2.2.1": "https://registry.npmjs.org/json5/-/json5-2.2.1.tgz", "jsonc-parser@3.0.0": "https://registry.npmjs.org/jsonc-parser/-/jsonc-parser-3.0.0.tgz", "jsonfile@^6.0.1": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "kleur@^3.0.3": "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz", "leven@^3.1.0": "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz", "levn@^0.4.1": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz", "libphonenumber-js@^1.9.43": "https://registry.npmjs.org/libphonenumber-js/-/libphonenumber-js-1.10.12.tgz", "lines-and-columns@^1.1.6": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.1.6.tgz", "loader-runner@^4.2.0": "https://registry.npmjs.org/loader-runner/-/loader-runner-4.2.0.tgz", "locate-path@^5.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "locate-path@^6.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz", "lodash.memoize@4.x": "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "lodash.merge@^4.6.2": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "lodash@4.17.21": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.19": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.21": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "log-symbols@^4.1.0": "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz", "lru-cache@^10.2.0": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz", "lru-cache@^6.0.0": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "macos-release@^2.5.0": "https://registry.npmjs.org/macos-release/-/macos-release-2.5.0.tgz", "magic-string@0.26.1": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.1.tgz", "make-dir@^3.0.0": "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz", "make-error@1.x": "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz", "make-error@^1.1.1": "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz", "make-fetch-happen@^9.1.0": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-9.1.0.tgz", "makeerror@1.0.12": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz", "media-typer@0.3.0": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "memfs@^3.4.1": "https://registry.npmjs.org/memfs/-/memfs-3.4.7.tgz", "merge-descriptors@1.0.1": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "merge-stream@^2.0.0": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz", "merge2@^1.3.0": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "merge2@^1.4.1": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "methods@^1.1.2": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "methods@~1.1.2": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "micromatch@^4.0.0": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.4.tgz", "micromatch@^4.0.4": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.4.tgz", "mime-db@1.49.0": "https://registry.npmjs.org/mime-db/-/mime-db-1.49.0.tgz", "mime-db@1.52.0": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "mime-types@^2.1.12": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.32.tgz", "mime-types@^2.1.27": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.32.tgz", "mime-types@~2.1.24": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.32.tgz", "mime-types@~2.1.34": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime@1.6.0": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "mime@2.6.0": "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz", "mimic-fn@^2.1.0": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz", "mimic-response@^3.1.0": "https://registry.npmjs.org/mimic-response/-/mimic-response-3.1.0.tgz", "minimatch@9.0.1": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.1.tgz", "minimatch@^3.0.4": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "minimatch@^3.1.2": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^9.0.4": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz", "minimist@^1.2.0": "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz", "minimist@^1.2.3": "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz", "minimist@^1.2.5": "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz", "minimist@^1.2.6": "https://registry.npmjs.org/minimist/-/minimist-1.2.6.tgz", "minipass-collect@^1.0.2": "https://registry.npmjs.org/minipass-collect/-/minipass-collect-1.0.2.tgz", "minipass-fetch@^1.3.2": "https://registry.npmjs.org/minipass-fetch/-/minipass-fetch-1.4.1.tgz", "minipass-flush@^1.0.5": "https://registry.npmjs.org/minipass-flush/-/minipass-flush-1.0.5.tgz", "minipass-pipeline@^1.2.2": "https://registry.npmjs.org/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz", "minipass-pipeline@^1.2.4": "https://registry.npmjs.org/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz", "minipass-sized@^1.0.3": "https://registry.npmjs.org/minipass-sized/-/minipass-sized-1.0.3.tgz", "minipass@^3.0.0": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "minipass@^3.1.0": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "minipass@^3.1.1": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "minipass@^3.1.3": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "minipass@^5.0.0": "https://registry.npmjs.org/minipass/-/minipass-5.0.0.tgz", "minipass@^5.0.0 || ^6.0.2 || ^7.0.0": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "minipass@^7.1.2": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "minizlib@^2.0.0": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz", "minizlib@^2.1.1": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz", "mkdirp-classic@^0.5.2": "https://registry.npmjs.org/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz", "mkdirp-classic@^0.5.3": "https://registry.npmjs.org/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz", "mkdirp@^0.5.4": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.5.tgz", "mkdirp@^1.0.3": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "mkdirp@^1.0.4": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "moment-timezone@^0.5.43": "https://registry.npmjs.org/moment-timezone/-/moment-timezone-0.5.48.tgz", "moment@^2.29.4": "https://registry.npmjs.org/moment/-/moment-2.30.1.tgz", "ms@2.0.0": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "ms@2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.0.0": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "multer@1.4.4-lts.1": "https://registry.npmjs.org/multer/-/multer-1.4.4-lts.1.tgz", "mute-stream@0.0.8": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz", "napi-build-utils@^2.0.0": "https://registry.npmjs.org/napi-build-utils/-/napi-build-utils-2.0.0.tgz", "natural-compare@^1.4.0": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "negotiator@0.6.3": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "negotiator@^0.6.2": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "neo-async@^2.6.2": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz", "nestjs-stripe@^1.0.0": "https://registry.npmjs.org/nestjs-stripe/-/nestjs-stripe-1.0.0.tgz", "node-abi@^3.3.0": "https://registry.npmjs.org/node-abi/-/node-abi-3.75.0.tgz", "node-addon-api@^7.0.0": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-7.1.1.tgz", "node-emoji@1.11.0": "https://registry.npmjs.org/node-emoji/-/node-emoji-1.11.0.tgz", "node-fetch@^2.6.1": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.1.tgz", "node-gyp@8.x": "https://registry.npmjs.org/node-gyp/-/node-gyp-8.4.1.tgz", "node-int64@^0.4.0": "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz", "node-releases@^1.1.75": "https://registry.npmjs.org/node-releases/-/node-releases-1.1.75.tgz", "node-releases@^2.0.6": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.6.tgz", "nopt@^5.0.0": "https://registry.npmjs.org/nopt/-/nopt-5.0.0.tgz", "nopt@^7.2.1": "https://registry.npmjs.org/nopt/-/nopt-7.2.1.tgz", "normalize-path@^3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-path@~3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "npm-run-path@^4.0.0": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz", "npm-run-path@^4.0.1": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz", "npmlog@^6.0.0": "https://registry.npmjs.org/npmlog/-/npmlog-6.0.2.tgz", "object-assign@^4": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "object-assign@^4.1.1": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "object-hash@3.0.0": "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz", "object-inspect@^1.9.0": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.11.0.tgz", "on-finished@2.4.1": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "once@1.4.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "once@^1.3.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "once@^1.3.1": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "once@^1.4.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "onetime@^5.1.0": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz", "onetime@^5.1.2": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz", "optionator@^0.9.1": "https://registry.npmjs.org/optionator/-/optionator-0.9.1.tgz", "ora@5.4.1": "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz", "ora@^5.4.1": "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz", "os-name@4.0.1": "https://registry.npmjs.org/os-name/-/os-name-4.0.1.tgz", "os-tmpdir@~1.0.2": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "p-limit@^2.2.0": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "p-limit@^3.0.2": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "p-limit@^3.1.0": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "p-locate@^4.1.0": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "p-locate@^5.0.0": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz", "p-map@^4.0.0": "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz", "p-try@^2.0.0": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "package-json-from-dist@^1.0.0": "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "parent-module@^1.0.0": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz", "parse-json@^5.0.0": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "parse-json@^5.2.0": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "parseurl@~1.3.3": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "path-exists@^4.0.0": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "path-is-absolute@^1.0.0": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "path-key@^3.0.0": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "path-key@^3.1.0": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "path-parse@^1.0.7": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "path-scurry@^1.11.1": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz", "path-to-regexp@0.1.7": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "path-to-regexp@3.2.0": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-3.2.0.tgz", "path-type@^4.0.0": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "paypal-rest-sdk@^1.8.1": "https://registry.npmjs.org/paypal-rest-sdk/-/paypal-rest-sdk-1.8.1.tgz", "pg-cloudflare@^1.2.5": "https://registry.npmjs.org/pg-cloudflare/-/pg-cloudflare-1.2.5.tgz", "pg-connection-string@^2.6.1": "https://registry.npmjs.org/pg-connection-string/-/pg-connection-string-2.9.0.tgz", "pg-connection-string@^2.9.0": "https://registry.npmjs.org/pg-connection-string/-/pg-connection-string-2.9.0.tgz", "pg-hstore@^2.3.4": "https://registry.npmjs.org/pg-hstore/-/pg-hstore-2.3.4.tgz", "pg-int8@1.0.1": "https://registry.npmjs.org/pg-int8/-/pg-int8-1.0.1.tgz", "pg-pool@^3.10.0": "https://registry.npmjs.org/pg-pool/-/pg-pool-3.10.0.tgz", "pg-protocol@^1.10.0": "https://registry.npmjs.org/pg-protocol/-/pg-protocol-1.10.0.tgz", "pg-types@2.2.0": "https://registry.npmjs.org/pg-types/-/pg-types-2.2.0.tgz", "pg@^8.16.0": "https://registry.npmjs.org/pg/-/pg-8.16.0.tgz", "pgpass@1.0.5": "https://registry.npmjs.org/pgpass/-/pgpass-1.0.5.tgz", "picocolors@^1.0.0": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "picocolors@^1.1.1": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "picomatch@^2.0.4": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.0.tgz", "picomatch@^2.2.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.0.tgz", "picomatch@^2.2.3": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.0.tgz", "pirates@^4.0.4": "https://registry.npmjs.org/pirates/-/pirates-4.0.5.tgz", "pkg-dir@^4.2.0": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz", "pluralize@8.0.0": "https://registry.npmjs.org/pluralize/-/pluralize-8.0.0.tgz", "postgres-array@~2.0.0": "https://registry.npmjs.org/postgres-array/-/postgres-array-2.0.0.tgz", "postgres-bytea@~1.0.0": "https://registry.npmjs.org/postgres-bytea/-/postgres-bytea-1.0.0.tgz", "postgres-date@~1.0.4": "https://registry.npmjs.org/postgres-date/-/postgres-date-1.0.7.tgz", "postgres-interval@^1.1.0": "https://registry.npmjs.org/postgres-interval/-/postgres-interval-1.2.0.tgz", "prebuild-install@^7.1.1": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-7.1.3.tgz", "prelude-ls@^1.2.1": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz", "prettier-linter-helpers@^1.0.0": "https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz", "prettier@^2.7.1": "https://registry.npmjs.org/prettier/-/prettier-2.7.1.tgz", "pretty-format@^28.0.0": "https://registry.npmjs.org/pretty-format/-/pretty-format-28.1.3.tgz", "pretty-format@^28.1.3": "https://registry.npmjs.org/pretty-format/-/pretty-format-28.1.3.tgz", "process-nextick-args@~2.0.0": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "promise-inflight@^1.0.1": "https://registry.npmjs.org/promise-inflight/-/promise-inflight-1.0.1.tgz", "promise-retry@^2.0.1": "https://registry.npmjs.org/promise-retry/-/promise-retry-2.0.1.tgz", "prompts@^2.0.1": "https://registry.npmjs.org/prompts/-/prompts-2.4.1.tgz", "proto-list@~1.2.1": "https://registry.npmjs.org/proto-list/-/proto-list-1.2.4.tgz", "proxy-addr@~2.0.7": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "proxy-from-env@^1.1.0": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "pump@^3.0.0": "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz", "punycode@^2.1.0": "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz", "qs@6.10.3": "https://registry.npmjs.org/qs/-/qs-6.10.3.tgz", "qs@6.9.3": "https://registry.npmjs.org/qs/-/qs-6.9.3.tgz", "qs@^6.10.3": "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz", "qs@^6.11.0": "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz", "queue-microtask@^1.2.2": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "randombytes@^2.1.0": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "range-parser@~1.2.1": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "raw-body@2.5.1": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.1.tgz", "rc@^1.2.7": "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz", "react-is@^18.0.0": "https://registry.npmjs.org/react-is/-/react-is-18.2.0.tgz", "readable-stream@^2.2.2": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz", "readable-stream@^3.1.1": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz", "readable-stream@^3.4.0": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz", "readable-stream@^3.6.0": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz", "readdirp@~3.6.0": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "rechoir@^0.6.2": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz", "reflect-metadata@^0.1.13": "https://registry.npmjs.org/reflect-metadata/-/reflect-metadata-0.1.13.tgz", "regexpp@^3.2.0": "https://registry.npmjs.org/regexpp/-/regexpp-3.2.0.tgz", "require-directory@^2.1.1": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "require-from-string@^2.0.2": "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz", "resolve-cwd@^3.0.0": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "resolve-from@^4.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "resolve-from@^5.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "resolve.exports@^1.1.0": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-1.1.0.tgz", "resolve@^1.1.6": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^1.20.0": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^1.22.1": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "restore-cursor@^3.1.0": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz", "retry-as-promised@^7.0.4": "https://registry.npmjs.org/retry-as-promised/-/retry-as-promised-7.1.1.tgz", "retry@^0.12.0": "https://registry.npmjs.org/retry/-/retry-0.12.0.tgz", "reusify@^1.0.4": "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz", "rimraf@3.0.2": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "rimraf@^3.0.0": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "rimraf@^3.0.2": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "run-async@^2.4.0": "https://registry.npmjs.org/run-async/-/run-async-2.4.1.tgz", "run-parallel@^1.1.9": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "rxjs@6.6.7": "https://registry.npmjs.org/rxjs/-/rxjs-6.6.7.tgz", "rxjs@^6.6.0": "https://registry.npmjs.org/rxjs/-/rxjs-6.6.7.tgz", "rxjs@^7.5.5": "https://registry.npmjs.org/rxjs/-/rxjs-7.5.6.tgz", "rxjs@^7.5.6": "https://registry.npmjs.org/rxjs/-/rxjs-7.5.6.tgz", "safe-buffer@5.2.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.0.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@~5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@~5.1.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@~5.2.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safer-buffer@>= 2.1.2 < 3": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "safer-buffer@>= 2.1.2 < 3.0.0": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "schema-utils@^3.1.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.1.tgz", "schema-utils@^3.1.1": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.1.tgz", "semver@7.x": "https://registry.npmjs.org/semver/-/semver-7.3.5.tgz", "semver@^5.0.3": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "semver@^6.0.0": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "semver@^6.3.0": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "semver@^7.3.4": "https://registry.npmjs.org/semver/-/semver-7.3.5.tgz", "semver@^7.3.5": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "semver@^7.3.7": "https://registry.npmjs.org/semver/-/semver-7.3.7.tgz", "semver@^7.5.3": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "semver@^7.5.4": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "send@0.18.0": "https://registry.npmjs.org/send/-/send-0.18.0.tgz", "sequelize-cli@^6.6.3": "https://registry.npmjs.org/sequelize-cli/-/sequelize-cli-6.6.3.tgz", "sequelize-pool@^7.1.0": "https://registry.npmjs.org/sequelize-pool/-/sequelize-pool-7.1.0.tgz", "sequelize-typescript@^2.1.6": "https://registry.npmjs.org/sequelize-typescript/-/sequelize-typescript-2.1.6.tgz", "sequelize@^6.37.7": "https://registry.npmjs.org/sequelize/-/sequelize-6.37.7.tgz", "serialize-javascript@^6.0.0": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.0.tgz", "serve-static@1.15.0": "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz", "set-blocking@^2.0.0": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "setprototypeof@1.2.0": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "shebang-command@^2.0.0": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "shelljs@0.8.5": "https://registry.npmjs.org/shelljs/-/shelljs-0.8.5.tgz", "side-channel@^1.0.4": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz", "signal-exit@^3.0.2": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.3": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.7": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^4.0.1": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz", "simple-concat@^1.0.0": "https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.1.tgz", "simple-get@^4.0.0": "https://registry.npmjs.org/simple-get/-/simple-get-4.0.1.tgz", "sisteransi@^1.0.5": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz", "slash@^3.0.0": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "smart-buffer@^4.2.0": "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz", "socks-proxy-agent@^6.0.0": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-6.2.1.tgz", "socks@^2.6.2": "https://registry.npmjs.org/socks/-/socks-2.8.5.tgz", "source-map-support@0.5.13": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz", "source-map-support@0.5.21": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "source-map-support@~0.5.19": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.19.tgz", "source-map@0.7.3": "https://registry.npmjs.org/source-map/-/source-map-0.7.3.tgz", "source-map@^0.5.0": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "source-map@^0.6.0": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@^0.6.1": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@~0.7.2": "https://registry.npmjs.org/source-map/-/source-map-0.7.3.tgz", "sourcemap-codec@^1.4.8": "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz", "split2@^4.1.0": "https://registry.npmjs.org/split2/-/split2-4.2.0.tgz", "sprintf-js@^1.1.3": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.3.tgz", "sprintf-js@~1.0.2": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "sqlite3@^5.1.7": "https://registry.npmjs.org/sqlite3/-/sqlite3-5.1.7.tgz", "ssri@^8.0.0": "https://registry.npmjs.org/ssri/-/ssri-8.0.1.tgz", "ssri@^8.0.1": "https://registry.npmjs.org/ssri/-/ssri-8.0.1.tgz", "stack-utils@^2.0.3": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.3.tgz", "statuses@2.0.1": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "streamsearch@^1.1.0": "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz", "string-length@^4.0.1": "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz", "string-width-cjs@npm:string-width@^4.2.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^1.0.2 || 2 || 3 || 4": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.1.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.3": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^5.0.1": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "string-width@^5.1.2": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "string_decoder@^1.1.1": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "string_decoder@~1.1.1": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "strip-ansi-cjs@npm:strip-ansi@^6.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^7.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz", "strip-bom@^3.0.0": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz", "strip-bom@^4.0.0": "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz", "strip-final-newline@^2.0.0": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "strip-json-comments@^3.1.0": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "strip-json-comments@^3.1.1": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "strip-json-comments@~2.0.1": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "stripe@^11.2.0": "https://registry.npmjs.org/stripe/-/stripe-11.2.0.tgz", "superagent@^8.0.0": "https://registry.npmjs.org/superagent/-/superagent-8.0.0.tgz", "supertest@^6.2.4": "https://registry.npmjs.org/supertest/-/supertest-6.2.4.tgz", "supports-color@^5.3.0": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "supports-color@^7.0.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^7.1.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^8.0.0": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "supports-hyperlinks@^2.0.0": "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.2.0.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "swagger-ui-dist@4.13.2": "https://registry.npmjs.org/swagger-ui-dist/-/swagger-ui-dist-4.13.2.tgz", "symbol-observable@4.0.0": "https://registry.npmjs.org/symbol-observable/-/symbol-observable-4.0.0.tgz", "tapable@^2.1.1": "https://registry.npmjs.org/tapable/-/tapable-2.2.0.tgz", "tapable@^2.2.0": "https://registry.npmjs.org/tapable/-/tapable-2.2.0.tgz", "tapable@^2.2.1": "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz", "tar-fs@^2.0.0": "https://registry.npmjs.org/tar-fs/-/tar-fs-2.1.3.tgz", "tar-stream@^2.1.4": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.2.0.tgz", "tar@^6.0.2": "https://registry.npmjs.org/tar/-/tar-6.2.1.tgz", "tar@^6.1.11": "https://registry.npmjs.org/tar/-/tar-6.2.1.tgz", "tar@^6.1.2": "https://registry.npmjs.org/tar/-/tar-6.2.1.tgz", "terminal-link@^2.0.0": "https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz", "terser-webpack-plugin@^5.1.3": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.2.0.tgz", "terser@^5.7.2": "https://registry.npmjs.org/terser/-/terser-5.7.2.tgz", "test-exclude@^6.0.0": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz", "text-table@^0.2.0": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "through@^2.3.6": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "tmp@^0.0.33": "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz", "tmpl@1.0.5": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz", "to-fast-properties@^2.0.0": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "to-regex-range@^5.0.1": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "toidentifier@1.0.1": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "toposort-class@^1.0.1": "https://registry.npmjs.org/toposort-class/-/toposort-class-1.0.1.tgz", "tree-kill@1.2.2": "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz", "ts-jest@^28.0.8": "https://registry.npmjs.org/ts-jest/-/ts-jest-28.0.8.tgz", "ts-loader@^9.3.1": "https://registry.npmjs.org/ts-loader/-/ts-loader-9.3.1.tgz", "ts-node@^10.9.1": "https://registry.npmjs.org/ts-node/-/ts-node-10.9.1.tgz", "tsconfig-paths-webpack-plugin@3.5.2": "https://registry.npmjs.org/tsconfig-paths-webpack-plugin/-/tsconfig-paths-webpack-plugin-3.5.2.tgz", "tsconfig-paths@3.14.1": "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.14.1.tgz", "tsconfig-paths@^3.9.0": "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.11.0.tgz", "tsconfig-paths@^4.1.0": "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-4.1.0.tgz", "tslib@2.4.0": "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz", "tslib@^1.8.1": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "tslib@^1.9.0": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "tslib@^2.1.0": "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz", "tsutils@^3.21.0": "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz", "tunnel-agent@^0.6.0": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "type-check@^0.4.0": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "type-check@~0.4.0": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "type-detect@4.0.8": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz", "type-fest@^0.20.2": "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz", "type-fest@^0.21.3": "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz", "type-is@^1.6.4": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "type-is@~1.6.18": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "typedarray@^0.0.6": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "typescript@4.7.4": "https://registry.npmjs.org/typescript/-/typescript-4.7.4.tgz", "typescript@^4.7.4": "https://registry.npmjs.org/typescript/-/typescript-4.7.4.tgz", "umzug@^2.3.0": "https://registry.npmjs.org/umzug/-/umzug-2.3.0.tgz", "underscore@^1.13.1": "https://registry.npmjs.org/underscore/-/underscore-1.13.7.tgz", "unique-filename@^1.1.1": "https://registry.npmjs.org/unique-filename/-/unique-filename-1.1.1.tgz", "unique-slug@^2.0.0": "https://registry.npmjs.org/unique-slug/-/unique-slug-2.0.2.tgz", "universalify@^2.0.0": "https://registry.npmjs.org/universalify/-/universalify-2.0.0.tgz", "unpipe@1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "unpipe@~1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "update-browserslist-db@^1.0.5": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.5.tgz", "uri-js@^4.2.2": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "util-deprecate@^1.0.1": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "util-deprecate@~1.0.1": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "utils-merge@1.0.1": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "uuid@8.3.2": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "uuid@^8.3.2": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "v8-compile-cache-lib@^3.0.1": "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz", "v8-compile-cache@^2.0.3": "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz", "v8-to-istanbul@^9.0.1": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.0.1.tgz", "validator@^13.7.0": "https://registry.npmjs.org/validator/-/validator-13.7.0.tgz", "validator@^13.9.0": "https://registry.npmjs.org/validator/-/validator-13.15.15.tgz", "vary@^1": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "vary@~1.1.2": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "walker@^1.0.8": "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz", "watchpack@^2.3.1": "https://registry.npmjs.org/watchpack/-/watchpack-2.4.0.tgz", "wcwidth@^1.0.1": "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz", "webpack-node-externals@3.0.0": "https://registry.npmjs.org/webpack-node-externals/-/webpack-node-externals-3.0.0.tgz", "webpack-sources@^3.2.3": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz", "webpack@5.73.0": "https://registry.npmjs.org/webpack/-/webpack-5.73.0.tgz", "which@^2.0.1": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "which@^2.0.2": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "wide-align@^1.1.5": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz", "windows-release@^4.0.0": "https://registry.npmjs.org/windows-release/-/windows-release-4.0.0.tgz", "wkx@^0.5.0": "https://registry.npmjs.org/wkx/-/wkx-0.5.0.tgz", "word-wrap@^1.2.3": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz", "wrap-ansi-cjs@npm:wrap-ansi@^7.0.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^7.0.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^8.1.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "wrappy@1": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "write-file-atomic@^4.0.1": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "xtend@^4.0.0": "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz", "y18n@^5.0.5": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "yallist@^4.0.0": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "yaml@^1.10.0": "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz", "yargs-parser@21.0.1": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.0.1.tgz", "yargs-parser@^20.2.2": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz", "yargs-parser@^21.0.0": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs-parser@^21.0.1": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs@^16.2.0": "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz", "yargs@^17.3.1": "https://registry.npmjs.org/yargs/-/yargs-17.5.1.tgz", "yn@3.1.1": "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz", "yocto-queue@^0.1.0": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"}, "files": [], "artifacts": {"sqlite3@5.1.7": ["build", "build\\Release", "build\\Release\\node_sqlite3.node"]}}