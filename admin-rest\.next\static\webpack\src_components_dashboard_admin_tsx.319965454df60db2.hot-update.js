"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src_components_dashboard_admin_tsx",{

/***/ "./src/components/widgets/sticker-card.tsx":
/*!*************************************************!*\
  !*** ./src/components/widgets/sticker-card.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_icons_ios_arrow_down__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons/ios-arrow-down */ \"./src/components/icons/ios-arrow-down.tsx\");\n/* harmony import */ var _components_icons_ios_arrow_up__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/icons/ios-arrow-up */ \"./src/components/icons/ios-arrow-up.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tailwind-merge */ \"./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst StickerCard = (param)=>{\n    let { titleTransKey, icon, color, price, indicator, indicatorText, note, link, linkText, iconClassName } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(\"widgets\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full w-full flex-col rounded-lg border border-b-4 border-secondary-text bg-secondary-bg p-5 md:p-6\",\n        style: {\n            borderBottomColor: color\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-auto flex w-full items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_6__.twMerge)(classnames__WEBPACK_IMPORTED_MODULE_5___default()(\"flex h-14 w-14 shrink-0 items-center justify-center rounded bg-gray-100/80 me-3\", iconClassName)),\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex w-full flex-col text-end\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mb-1 text-base font-normal text-body\",\n                                children: t(titleTransKey)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mb-2 text-2xl font-semibold text-heading\",\n                                children: price\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            indicator === \"up\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mb-12 inline-block text-sm font-semibold text-body\",\n                style: {\n                    color: \"#03D3B5\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_ios_arrow_up__WEBPACK_IMPORTED_MODULE_3__.IosArrowUp, {\n                        width: \"9px\",\n                        height: \"11px\",\n                        className: \"inline-block\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined),\n                    \" \",\n                    indicatorText,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-normal text-body\",\n                        children: [\n                            \" \",\n                            note\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, undefined),\n            indicator === \"down\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mb-12 inline-block text-sm font-semibold text-body\",\n                style: {\n                    color: \"#FC6687\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_ios_arrow_down__WEBPACK_IMPORTED_MODULE_2__.IosArrowDown, {\n                        width: \"9px\",\n                        height: \"11px\",\n                        className: \"inline-block\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, undefined),\n                    \" \",\n                    indicatorText,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-normal text-body\",\n                        children: [\n                            \" \",\n                            note\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, undefined),\n            link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"text-xs font-semibold text-purple-700 no-underline\",\n                href: link,\n                target: \"_blank\",\n                rel: \"noreferrer\",\n                children: linkText\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\widgets\\\\sticker-card.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StickerCard, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = StickerCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StickerCard);\nvar _c;\n$RefreshReg$(_c, \"StickerCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/widgets/sticker-card.tsx\n"));

/***/ })

});