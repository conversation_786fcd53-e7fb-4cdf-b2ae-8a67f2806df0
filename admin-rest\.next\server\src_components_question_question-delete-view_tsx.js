"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_question_question-delete-view_tsx";
exports.ids = ["src_components_question_question-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/question/question-delete-view.tsx":
/*!**********************************************************!*\
  !*** ./src/components/question/question-delete-view.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_question__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/question */ \"./src/data/question.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_question__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_question__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst QuestionDeleteView = ()=>{\n    const { mutate: deleteQuestion, isLoading: loading } = (0,_data_question__WEBPACK_IMPORTED_MODULE_3__.useDeleteQuestionMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteQuestion({\n            id: data?.id\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\question\\\\question-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuestionDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/question/question-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/question.ts":
/*!*************************************!*\
  !*** ./src/data/client/question.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   questionClient: () => (/* binding */ questionClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst questionClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.QUESTIONS),\n    get ({ id }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.QUESTIONS}/${id}`);\n    },\n    paginated: ({ type, shop_id, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.QUESTIONS, {\n            searchJoin: \"and\",\n            with: \"product;user\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                shop_id\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZGF0YS9jbGllbnQvcXVlc3Rpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQU1nRDtBQUNIO0FBQ0Y7QUFHcEMsTUFBTUcsaUJBQWlCO0lBQzVCLEdBQUdGLDBEQUFXQSxDQUNaRCx5REFBYUEsQ0FBQ0ksU0FBUyxDQUN4QjtJQUNEQyxLQUFJLEVBQUVDLEVBQUUsRUFBa0I7UUFDeEIsT0FBT0osb0RBQVVBLENBQUNHLEdBQUcsQ0FBVyxDQUFDLEVBQUVMLHlEQUFhQSxDQUFDSSxTQUFTLENBQUMsQ0FBQyxFQUFFRSxHQUFHLENBQUM7SUFDcEU7SUFDQUMsV0FBVyxDQUFDLEVBQUVDLElBQUksRUFBRUMsT0FBTyxFQUFFLEdBQUdDLFFBQXVDO1FBQ3JFLE9BQU9SLG9EQUFVQSxDQUFDRyxHQUFHLENBQW9CTCx5REFBYUEsQ0FBQ0ksU0FBUyxFQUFFO1lBQ2hFTyxZQUFZO1lBQ1pDLE1BQU07WUFDTixHQUFHRixNQUFNO1lBQ1RHLFFBQVFYLG9EQUFVQSxDQUFDWSxrQkFBa0IsQ0FBQztnQkFBRU47Z0JBQU1DO1lBQVE7UUFDeEQ7SUFDRjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab25la2FydC9hZG1pbi1yZXN0Ly4vc3JjL2RhdGEvY2xpZW50L3F1ZXN0aW9uLnRzP2JiNzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUge1xyXG4gIFF1ZXJ5T3B0aW9ucyxcclxuICBRdWVzdGlvbixcclxuICBSZXBseVF1ZXN0aW9uLFxyXG4gIFF1ZXN0aW9uUGFnaW5hdG9yLFxyXG59IGZyb20gJ0AvdHlwZXMnO1xyXG5pbXBvcnQgeyBBUElfRU5EUE9JTlRTIH0gZnJvbSAnLi9hcGktZW5kcG9pbnRzJztcclxuaW1wb3J0IHsgY3J1ZEZhY3RvcnkgfSBmcm9tICcuL2N1cmQtZmFjdG9yeSc7XHJcbmltcG9ydCB7IEh0dHBDbGllbnQgfSBmcm9tICcuL2h0dHAtY2xpZW50JztcclxuaW1wb3J0IHsgUXVlc3Rpb25RdWVyeU9wdGlvbnMgfSBmcm9tICdAL3R5cGVzJztcclxuXHJcbmV4cG9ydCBjb25zdCBxdWVzdGlvbkNsaWVudCA9IHtcclxuICAuLi5jcnVkRmFjdG9yeTxRdWVzdGlvbiwgUXVlcnlPcHRpb25zLCBSZXBseVF1ZXN0aW9uPihcclxuICAgIEFQSV9FTkRQT0lOVFMuUVVFU1RJT05TXHJcbiAgKSxcclxuICBnZXQoeyBpZCB9OiB7IGlkOiBzdHJpbmcgfSkge1xyXG4gICAgcmV0dXJuIEh0dHBDbGllbnQuZ2V0PFF1ZXN0aW9uPihgJHtBUElfRU5EUE9JTlRTLlFVRVNUSU9OU30vJHtpZH1gKTtcclxuICB9LFxyXG4gIHBhZ2luYXRlZDogKHsgdHlwZSwgc2hvcF9pZCwgLi4ucGFyYW1zIH06IFBhcnRpYWw8UXVlc3Rpb25RdWVyeU9wdGlvbnM+KSA9PiB7XHJcbiAgICByZXR1cm4gSHR0cENsaWVudC5nZXQ8UXVlc3Rpb25QYWdpbmF0b3I+KEFQSV9FTkRQT0lOVFMuUVVFU1RJT05TLCB7XHJcbiAgICAgIHNlYXJjaEpvaW46ICdhbmQnLFxyXG4gICAgICB3aXRoOiAncHJvZHVjdDt1c2VyJyxcclxuICAgICAgLi4ucGFyYW1zLFxyXG4gICAgICBzZWFyY2g6IEh0dHBDbGllbnQuZm9ybWF0U2VhcmNoUGFyYW1zKHsgdHlwZSwgc2hvcF9pZCB9KSxcclxuICAgIH0pO1xyXG4gIH0sXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJBUElfRU5EUE9JTlRTIiwiY3J1ZEZhY3RvcnkiLCJIdHRwQ2xpZW50IiwicXVlc3Rpb25DbGllbnQiLCJRVUVTVElPTlMiLCJnZXQiLCJpZCIsInBhZ2luYXRlZCIsInR5cGUiLCJzaG9wX2lkIiwicGFyYW1zIiwic2VhcmNoSm9pbiIsIndpdGgiLCJzZWFyY2giLCJmb3JtYXRTZWFyY2hQYXJhbXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/data/client/question.ts\n");

/***/ }),

/***/ "./src/data/question.ts":
/*!******************************!*\
  !*** ./src/data/question.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDeleteQuestionMutation: () => (/* binding */ useDeleteQuestionMutation),\n/* harmony export */   useQuestionsQuery: () => (/* binding */ useQuestionsQuery),\n/* harmony export */   useReplyQuestionMutation: () => (/* binding */ useReplyQuestionMutation)\n/* harmony export */ });\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _data_client_question__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/data/client/question */ \"./src/data/client/question.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__, react_toastify__WEBPACK_IMPORTED_MODULE_3__, _data_client_question__WEBPACK_IMPORTED_MODULE_5__]);\n([_utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__, react_toastify__WEBPACK_IMPORTED_MODULE_3__, _data_client_question__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst useQuestionsQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.QUESTIONS,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_question__WEBPACK_IMPORTED_MODULE_5__.questionClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        questions: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\nconst useReplyQuestionMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_question__WEBPACK_IMPORTED_MODULE_5__.questionClient.update, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.QUESTIONS);\n        }\n    });\n};\nconst useDeleteQuestionMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_question__WEBPACK_IMPORTED_MODULE_5__.questionClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.QUESTIONS);\n        }\n    });\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/question.ts\n");

/***/ })

};
;