import {
  Column,
  Model,
  Table,
  DataType,
  BelongsTo,
  ForeignKey,
  HasMany,
} from 'sequelize-typescript';
import { Shop } from 'src/shops/entities/shop.entity';
import { AttributeValue } from './attribute-value.entity';

@Table({
  tableName: 'attributes',
  timestamps: true,
})
export class Attribute extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @ForeignKey(() => Shop)
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  shop_id: string;

  @BelongsTo(() => Shop)
  shop: Shop;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  slug: string;

  @HasMany(() => AttributeValue)
  values: AttributeValue[];

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  language: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  translated_languages: string[];
}
