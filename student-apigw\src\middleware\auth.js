// Simple authentication middleware (for demonstration)
const authMiddleware = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  // Skip auth for GET requests in development
  if (process.env.NODE_ENV === 'development' && req.method === 'GET') {
    return next();
  }
  
  // Check for API key or Bearer token
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Access denied. No authorization header provided.',
      gateway: 'student-apigw'
    });
  }
  
  // Simple API key validation (in production, use proper JWT validation)
  const token = authHeader.split(' ')[1];
  const validTokens = ['demo-api-key', 'student-api-key', 'admin-token'];
  
  if (!validTokens.includes(token)) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Invalid token.',
      gateway: 'student-apigw'
    });
  }
  
  // Add user info to request (in production, decode JWT)
  req.user = {
    id: 'demo-user',
    role: 'admin',
    permissions: ['read:students', 'write:students']
  };
  
  next();
};

module.exports = {
  authMiddleware
};
