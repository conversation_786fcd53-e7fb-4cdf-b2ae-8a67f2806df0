"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_shipping_shipping-delete-view_tsx";
exports.ids = ["src_components_shipping_shipping-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/shipping/shipping-delete-view.tsx":
/*!**********************************************************!*\
  !*** ./src/components/shipping/shipping-delete-view.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_shipping__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/shipping */ \"./src/data/shipping.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_shipping__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_shipping__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst ShippingDeleteView = ()=>{\n    const { mutate: deleteShippingClass, isLoading: loading } = (0,_data_shipping__WEBPACK_IMPORTED_MODULE_3__.useDeleteShippingClassMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteShippingClass({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\shipping\\\\shipping-delete-view.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ShippingDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/shipping/shipping-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/shipping.ts":
/*!*************************************!*\
  !*** ./src/data/client/shipping.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shippingClient: () => (/* binding */ shippingClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst shippingClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS),\n    get ({ id }) {\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS}/${id}`);\n    },\n    paginated: ({ name, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    },\n    all: ({ name, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/shipping.ts\n");

/***/ }),

/***/ "./src/data/shipping.ts":
/*!******************************!*\
  !*** ./src/data/shipping.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateShippingMutation: () => (/* binding */ useCreateShippingMutation),\n/* harmony export */   useDeleteShippingClassMutation: () => (/* binding */ useDeleteShippingClassMutation),\n/* harmony export */   useShippingClassesQuery: () => (/* binding */ useShippingClassesQuery),\n/* harmony export */   useShippingQuery: () => (/* binding */ useShippingQuery),\n/* harmony export */   useUpdateShippingMutation: () => (/* binding */ useUpdateShippingMutation)\n/* harmony export */ });\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var _client_shipping__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/shipping */ \"./src/data/client/shipping.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_5__, _client_shipping__WEBPACK_IMPORTED_MODULE_6__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_5__, _client_shipping__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst useCreateShippingMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.create, {\n        onSuccess: ()=>{\n            router.push(_config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shipping.list);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS);\n        }\n    });\n};\nconst useDeleteShippingClassMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS);\n        }\n    });\n};\nconst useUpdateShippingMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.update, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS);\n        }\n    });\n};\nconst useShippingQuery = (id)=>{\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS,\n        id\n    ], ()=>_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.get({\n            id\n        }));\n};\nconst useShippingClassesQuery = (options = {})=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS,\n        options\n    ], ({ queryKey, pageParam })=>_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.all(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        shippingClasses: data ?? [],\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/shipping.ts\n");

/***/ })

};
;