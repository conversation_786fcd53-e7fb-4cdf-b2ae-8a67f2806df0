"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OwnershipTransferController = void 0;
const openapi = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const ownership_transfer_service_1 = require("./ownership-transfer.service");
const create_ownership_transfer_dto_1 = require("./dto/create-ownership-transfer.dto");
const get_ownership_transfer_dto_1 = require("./dto/get-ownership-transfer.dto");
const update_ownership_transfer_dto_1 = require("./dto/update-ownership-transfer.dto");
let OwnershipTransferController = class OwnershipTransferController {
    constructor(ownershipTransferService) {
        this.ownershipTransferService = ownershipTransferService;
    }
    createOwnershipTransfer(createOwnershipTransferDto) {
        return this.ownershipTransferService.create(createOwnershipTransferDto);
    }
    findAll(query) {
        return this.ownershipTransferService.findAll(query);
    }
    getOwnershipTransfer(param, language) {
        return this.ownershipTransferService.getOwnershipTransfer(param, language);
    }
    update(id, language, updateRefundDto) {
        return this.ownershipTransferService.update(+id, updateRefundDto);
    }
    deleteOwnershipTransfer(id) {
        return this.ownershipTransferService.remove(+id);
    }
};
__decorate([
    (0, common_1.Post)(),
    openapi.ApiResponse({ status: 201, type: require("./entities/ownership-transfer.entity").OwnershipTransfer }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_ownership_transfer_dto_1.CreateOwnershipTransferDto]),
    __metadata("design:returntype", void 0)
], OwnershipTransferController.prototype, "createOwnershipTransfer", null);
__decorate([
    (0, common_1.Get)(),
    openapi.ApiResponse({ status: 200 }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_ownership_transfer_dto_1.GetOwnershipTransferDto]),
    __metadata("design:returntype", void 0)
], OwnershipTransferController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':param'),
    openapi.ApiResponse({ status: 200, type: require("./entities/ownership-transfer.entity").OwnershipTransfer }),
    __param(0, (0, common_1.Param)('param')),
    __param(1, (0, common_1.Query)('language')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], OwnershipTransferController.prototype, "getOwnershipTransfer", null);
__decorate([
    (0, common_1.Put)(':id'),
    openapi.ApiResponse({ status: 200, type: require("./entities/ownership-transfer.entity").OwnershipTransfer }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('language')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, update_ownership_transfer_dto_1.UpdateOwnershipTransferDto]),
    __metadata("design:returntype", void 0)
], OwnershipTransferController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    openapi.ApiResponse({ status: 200, type: String }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OwnershipTransferController.prototype, "deleteOwnershipTransfer", null);
OwnershipTransferController = __decorate([
    (0, common_1.Controller)('ownership-transfer'),
    __metadata("design:paramtypes", [ownership_transfer_service_1.OwnershipTransferService])
], OwnershipTransferController);
exports.OwnershipTransferController = OwnershipTransferController;
//# sourceMappingURL=ownership-transfer.controller.js.map