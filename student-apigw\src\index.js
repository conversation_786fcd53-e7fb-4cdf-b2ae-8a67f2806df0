const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const rateLimit = require("express-rate-limit");
const { createProxyMiddleware } = require("http-proxy-middleware");
require("dotenv").config();

const { errorHandler, notFound } = require("./middleware/errorHandler");
const { authMiddleware } = require("./middleware/auth");
const { loggingMiddleware } = require("./middleware/logging");

const app = express();
const PORT = process.env.PORT || 3000;

// Service URLs
const STUDENT_SERVICE_URL =
  process.env.STUDENT_SERVICE_URL || "http://localhost:3001";

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: "Too many requests from this IP, please try again later.",
  },
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan("combined"));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(limiter);
app.use(loggingMiddleware);

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    service: "student-apigw",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    services: {
      studentService: STUDENT_SERVICE_URL,
    },
  });
});

// API Gateway routes
app.get("/api/gateway/info", (req, res) => {
  res.status(200).json({
    success: true,
    message: "Student API Gateway",
    version: "1.0.0",
    endpoints: {
      students: "/api/students/*",
      health: "/health",
      info: "/api/gateway/info",
    },
    services: {
      studentService: {
        url: STUDENT_SERVICE_URL,
        endpoints: [
          "GET /api/students - List students",
          "POST /api/students - Create student",
          "GET /api/students/:id - Get student by ID",
          "PUT /api/students/:id - Update student",
          "DELETE /api/students/:id - Delete student",
          "GET /api/students/stats/overview - Get statistics",
        ],
      },
    },
  });
});

// Student service proxy
app.use(
  "/api/students",
  // Optional authentication middleware (commented out for testing)
  // authMiddleware,
  createProxyMiddleware({
    target: STUDENT_SERVICE_URL,
    changeOrigin: true,
    pathRewrite: {
      "^/api/students": "/api/students",
    },
    timeout: 10000, // 10 second timeout
    proxyTimeout: 10000,
    onError: (err, req, res) => {
      console.error("Proxy Error:", err.message);
      if (!res.headersSent) {
        res.status(503).json({
          success: false,
          message: "Student service unavailable",
          error: "Service temporarily unavailable",
        });
      }
    },
    onProxyReq: (proxyReq, req, res) => {
      console.log(
        `🔄 Proxying ${req.method} ${req.path} to ${STUDENT_SERVICE_URL}`
      );

      // Fix for POST/PUT requests - ensure body is properly forwarded
      if (req.body && (req.method === "POST" || req.method === "PUT")) {
        const bodyData = JSON.stringify(req.body);
        proxyReq.setHeader("Content-Type", "application/json");
        proxyReq.setHeader("Content-Length", Buffer.byteLength(bodyData));
        proxyReq.write(bodyData);
      }
    },
    onProxyRes: (proxyRes, req, res) => {
      console.log(`✅ Response ${proxyRes.statusCode} from student service`);
    },
  })
);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`🌐 Student API Gateway running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`📋 Gateway info: http://localhost:${PORT}/api/gateway/info`);
  console.log(`👨‍🎓 Students API: http://localhost:${PORT}/api/students`);
  console.log(`🔗 Proxying to Student Service: ${STUDENT_SERVICE_URL}`);
});

module.exports = app;
