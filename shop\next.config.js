/** @type {import('next').NextConfig} */
// Temporarily simplify config for debugging
// const { i18n } = require('./next-i18next.config');

module.exports = {
  reactStrictMode: false, // Disable strict mode for debugging
  // i18n, // Temporarily disable i18n
  images: {
    domains: [
      'pickbazarlaravel.s3.ap-southeast-1.amazonaws.com',
      'pixarlaravel.s3.ap-southeast-1.amazonaws.com',
      'lh3.googleusercontent.com',
      'localhost',
      '127.0.0.1',
      'i.pravatar.cc',
    ],
  },
  // Temporarily disable complex webpack config
  // ...(process.env.FRAMEWORK_PROVIDER === 'graphql' && {
  //   webpack(config, options) {
  //     config.module.rules.push({
  //       test: /\.graphql$/,
  //       exclude: /node_modules/,
  //       use: [options.defaultLoaders.babel, { loader: 'graphql-let/loader' }],
  //     });

  //     config.module.rules.push({
  //       test: /\.ya?ml$/,
  //       type: 'json',
  //       use: 'yaml-loader',
  //     });

  //     return config;
  //   },
  // }),
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
};
