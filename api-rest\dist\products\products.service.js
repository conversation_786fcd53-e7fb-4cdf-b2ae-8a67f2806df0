"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const product_entity_1 = require("./entities/product.entity");
const paginate_1 = require("../common/pagination/paginate");
const sequelize_2 = require("sequelize");
const category_entity_1 = require("../categories/entities/category.entity");
const tag_entity_1 = require("../tags/entities/tag.entity");
const type_entity_1 = require("../types/entities/type.entity");
const shop_entity_1 = require("../shops/entities/shop.entity");
let ProductsService = class ProductsService {
    constructor(productModel) {
        this.productModel = productModel;
    }
    async create(createProductDto) {
        return this.productModel.create(Object.assign({}, createProductDto));
    }
    async getProducts({ limit, page, search, }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const whereClause = {};
        if (search) {
            const parseSearchParams = search.split(';');
            for (const searchParam of parseSearchParams) {
                const [key, value] = searchParam.split(':');
                if (key === 'name') {
                    whereClause.name = {
                        [sequelize_2.Op.iLike]: `%${value}%`,
                    };
                }
                else if (key === 'shop_id') {
                    whereClause.shop_id = parseInt(value, 10);
                }
                else if (key in this.productModel.rawAttributes) {
                    whereClause[key] = value;
                }
            }
        }
        const { count, rows } = await this.productModel.findAndCountAll({
            where: whereClause,
            include: [
                { model: category_entity_1.Category, as: 'categories' },
                { model: tag_entity_1.Tag, as: 'tags' },
                { model: type_entity_1.Type, as: 'type' },
                { model: shop_entity_1.Shop, as: 'shop' },
            ],
            limit,
            offset,
            order: [['created_at', 'DESC']],
        });
        const url = `/products?search=${search}&limit=${limit}`;
        const totalPages = Math.ceil(count / limit);
        return {
            data: rows,
            count,
            current_page: page,
            firstItem: offset + 1,
            lastItem: Math.min(offset + limit, count),
            last_page: totalPages,
            per_page: limit,
            total: count,
            first_page_url: `${url}&page=1`,
            last_page_url: `${url}&page=${totalPages}`,
            next_page_url: page < totalPages ? `${url}&page=${page + 1}` : null,
            prev_page_url: page > 1 ? `${url}&page=${page - 1}` : null,
        };
    }
    async getProductBySlug(slug) {
        const product = await this.productModel.findOne({
            where: { slug },
            include: [
                { model: category_entity_1.Category, as: 'categories' },
                { model: tag_entity_1.Tag, as: 'tags' },
                { model: type_entity_1.Type, as: 'type' },
                { model: shop_entity_1.Shop, as: 'shop' },
            ],
        });
        if (product) {
            const related_products = await this.productModel.findAll({
                where: { type_id: product.type_id },
                limit: 20,
                include: [{ model: type_entity_1.Type, as: 'type' }],
            });
            product.related_products = related_products;
        }
        return product;
    }
    async getPopularProducts({ limit, type_slug, }) {
        const whereClause = {};
        if (type_slug) {
            const typeRecord = await this.productModel.sequelize.models.Type.findOne({
                where: { slug: type_slug },
            });
            if (typeRecord) {
                whereClause.type_id = typeRecord.id;
            }
        }
        return this.productModel.findAll({
            where: whereClause,
            include: [
                { model: type_entity_1.Type, as: 'type' },
                { model: shop_entity_1.Shop, as: 'shop' },
            ],
            limit,
            order: [['ratings', 'DESC']],
        });
    }
    async getBestSellingProducts({ limit, type_slug, }) {
        const whereClause = {};
        if (type_slug) {
            const typeRecord = await this.productModel.sequelize.models.Type.findOne({
                where: { slug: type_slug },
            });
            if (typeRecord) {
                whereClause.type_id = typeRecord.id;
            }
        }
        return this.productModel.findAll({
            where: whereClause,
            include: [
                { model: type_entity_1.Type, as: 'type' },
                { model: shop_entity_1.Shop, as: 'shop' },
            ],
            limit,
            order: [['quantity', 'ASC']],
        });
    }
    async getProductsStock({ limit, page, search, }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const whereClause = {
            quantity: { [sequelize_2.Op.lte]: 9 },
        };
        if (search) {
            whereClause[sequelize_2.Op.or] = [
                { name: { [sequelize_2.Op.iLike]: `%${search}%` } },
                { description: { [sequelize_2.Op.iLike]: `%${search}%` } },
            ];
        }
        const { count, rows: data } = await this.productModel.findAndCountAll({
            where: whereClause,
            limit,
            offset,
            order: [['quantity', 'ASC']],
        });
        const url = `/products-stock?search=${search}&limit=${limit}`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
    async getDraftProducts({ limit, page, search, }) {
        if (!page)
            page = 1;
        if (!limit)
            limit = 30;
        const offset = (page - 1) * limit;
        const whereClause = {
            status: 'draft',
        };
        if (search) {
            whereClause[sequelize_2.Op.or] = [
                { name: { [sequelize_2.Op.iLike]: `%${search}%` } },
                { description: { [sequelize_2.Op.iLike]: `%${search}%` } },
            ];
        }
        const { count, rows: data } = await this.productModel.findAndCountAll({
            where: whereClause,
            limit,
            offset,
            order: [['updated_at', 'DESC']],
        });
        const url = `/draft-products?search=${search}&limit=${limit}`;
        return Object.assign({ data }, (0, paginate_1.paginate)(count, page, limit, data.length, url));
    }
    async update(id, updateProductDto) {
        const [affectedCount, updatedProducts] = await this.productModel.update(Object.assign({}, updateProductDto), { where: { id }, returning: true });
        return [affectedCount, updatedProducts];
    }
    async remove(id) {
        return this.productModel.destroy({ where: { id } });
    }
};
ProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(product_entity_1.Product)),
    __metadata("design:paramtypes", [Object])
], ProductsService);
exports.ProductsService = ProductsService;
//# sourceMappingURL=products.service.js.map