"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_products_cards_radon_tsx";
exports.ids = ["src_components_products_cards_radon_tsx"];
exports.modules = {

/***/ "./src/components/icons/external-icon.tsx":
/*!************************************************!*\
  !*** ./src/components/icons/external-icon.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExternalIcon: () => (/* binding */ ExternalIcon),\n/* harmony export */   ExternalIconNew: () => (/* binding */ ExternalIconNew)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ExternalIcon = ({ ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\nconst ExternalIconNew = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"1em\",\n        height: \"1em\",\n        viewBox: \"0 0 20 20\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                opacity: 0.4,\n                fillRule: \"evenodd\",\n                clipRule: \"evenodd\",\n                d: \"M1.668 6.667a2.5 2.5 0 012.5-2.5h5a.833.833 0 110 1.666h-5a.833.833 0 00-.833.834v9.166c0 .46.373.834.833.834h9.167c.46 0 .833-.373.833-.834v-5a.833.833 0 111.667 0v5a2.5 2.5 0 01-2.5 2.5H4.168a2.5 2.5 0 01-2.5-2.5V6.667z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                clipRule: \"evenodd\",\n                d: \"M18.34 7.5a.833.833 0 01-1.667 0V4.512L9.5 11.684a.833.833 0 11-1.179-1.178l7.172-7.173h-2.99a.833.833 0 110-1.666h5.002c.46 0 .833.373.833.833v5z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/external-icon.tsx\n");

/***/ }),

/***/ "./src/components/products/cards/radon.tsx":
/*!*************************************************!*\
  !*** ./src/components/products/cards/radon.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/link */ \"./src/components/ui/link.tsx\");\n/* harmony import */ var _components_ui_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/image */ \"./src/components/ui/image.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _lib_placeholders__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/placeholders */ \"./src/lib/placeholders.tsx\");\n/* harmony import */ var _lib_use_price__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/use-price */ \"./src/lib/use-price.tsx\");\n/* harmony import */ var _components_icons_external_icon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/icons/external-icon */ \"./src/components/icons/external-icon.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_use_price__WEBPACK_IMPORTED_MODULE_7__]);\n_lib_use_price__WEBPACK_IMPORTED_MODULE_7__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\nconst Radon = ({ product, className })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(\"common\");\n    const { name, slug, image, author, min_price, max_price, product_type, is_external, external_product_url, external_product_button_text } = product ?? {};\n    const { price, basePrice, discount } = (0,_lib_use_price__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({\n        amount: product.sale_price ? product.sale_price : product.price,\n        baseAmount: product.price\n    });\n    const { price: minPrice } = (0,_lib_use_price__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({\n        amount: min_price\n    });\n    const { price: maxPrice } = (0,_lib_use_price__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({\n        amount: max_price\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(\"product-card cart-type-radon flex h-full flex-col overflow-hidden duration-200\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                href: _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.product(slug),\n                className: `cursor-pointer relative rounded-lg flex bg-white w-full justify-center items-center overflow-hidden ${product?.type?.settings?.layoutType === \"compact\" ? \"aspect-[2/3]\" : \"aspect-square\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image__WEBPACK_IMPORTED_MODULE_2__.Image, {\n                    src: image?.original ?? _lib_placeholders__WEBPACK_IMPORTED_MODULE_6__.productPlaceholder,\n                    alt: name,\n                    fill: true,\n                    quality: 100,\n                    sizes: \"(max-width: 768px) 100vw\",\n                    className: \"object-contain my-auto rounded-lg product-image\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between gap-3 pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col w-full space-y-2 overflow-hidden shrink-0\",\n                        children: [\n                            name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.product(slug),\n                                className: \"w-full text-sm font-semibold truncate transition-colors text-heading hover:text-orange-500 md:text-base\",\n                                title: name,\n                                children: name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, undefined),\n                            author && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-400 md:text-sm\",\n                                children: [\n                                    t(\"text-by\"),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.author(author?.slug),\n                                        className: \"transition-colors text-body hover:text-orange-500 ltr:ml-1 rtl:mr-1\",\n                                        children: author?.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center shrink-0\",\n                                children: product_type.toLowerCase() === \"variable\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-semibold text-orange-500 md:text-base\",\n                                    children: [\n                                        minPrice,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-heading\",\n                                            children: \" - \"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        maxPrice\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2.5 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-base font-semibold text-orange-500\",\n                                            children: price\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        basePrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"del\", {\n                                            className: \"text-xs font-semibold text-gray-400 ltr:mr-2 rtl:ml-2\",\n                                            children: basePrice\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-accent\",\n                                            children: [\n                                                \"(\",\n                                                t(\"text-save\"),\n                                                \" \",\n                                                discount,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, undefined),\n                    is_external ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: external_product_url,\n                        className: \"transition-all hover:text-orange-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_external_icon__WEBPACK_IMPORTED_MODULE_8__.ExternalIcon, {\n                            className: \"w-5 h-5 stroke-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined) : null\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Projects\\\\BB\\\\Projects\\\\e-commerce\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\radon.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Radon);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/cards/radon.tsx\n");

/***/ }),

/***/ "./src/lib/use-price.tsx":
/*!*******************************!*\
  !*** ./src/lib/use-price.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ usePrice),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatVariantPrice: () => (/* binding */ formatVariantPrice)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_framework_settings__WEBPACK_IMPORTED_MODULE_2__]);\n_framework_settings__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction formatPrice({ amount, currencyCode, locale, fractions }) {\n    const formatCurrency = new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency: currencyCode,\n        maximumFractionDigits: fractions\n    });\n    return formatCurrency.format(amount);\n}\nfunction formatVariantPrice({ amount, baseAmount, currencyCode, locale, fractions = 2 }) {\n    const hasDiscount = baseAmount > amount;\n    const formatDiscount = new Intl.NumberFormat(locale, {\n        style: \"percent\"\n    });\n    const discount = hasDiscount ? formatDiscount.format((baseAmount - amount) / baseAmount) : null;\n    const price = formatPrice({\n        amount,\n        currencyCode,\n        locale,\n        fractions\n    });\n    const basePrice = hasDiscount ? formatPrice({\n        amount: baseAmount,\n        currencyCode,\n        locale,\n        fractions\n    }) : null;\n    return {\n        price,\n        basePrice,\n        discount\n    };\n}\nfunction usePrice(data) {\n    const { settings } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_2__.useSettings)();\n    const currency = settings?.currency;\n    const currencyOptions = settings?.currencyOptions;\n    const { amount, baseAmount, currencyCode, currencyOptionsFormat } = {\n        ...data,\n        currencyCode: currency ?? \"USD\",\n        currencyOptionsFormat: currencyOptions ?? {\n            formation: \"en-US\",\n            fractions: 2\n        }\n    };\n    const { formation = \"en-US\", fractions = 2 } = currencyOptionsFormat;\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (typeof amount !== \"number\" || !currencyCode) return \"\";\n        const fractionalDigit = fractions ? fractions : 2;\n        let currentLocale = formation ? formation : \"en\";\n        // if (process.env.NEXT_PUBLIC_ENABLE_MULTI_LANG) {\n        //   currentLocale = locale ? locale : 'en';\n        // }\n        return baseAmount ? formatVariantPrice({\n            amount,\n            baseAmount,\n            currencyCode,\n            locale: currentLocale,\n            fractions: fractionalDigit\n        }) : formatPrice({\n            amount,\n            currencyCode,\n            locale: currentLocale,\n            fractions: fractionalDigit\n        });\n    }, [\n        amount,\n        baseAmount,\n        currencyCode,\n        locale\n    ]);\n    return typeof value === \"string\" ? {\n        price: value,\n        basePrice: null,\n        discount: null\n    } : value;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/use-price.tsx\n");

/***/ })

};
;