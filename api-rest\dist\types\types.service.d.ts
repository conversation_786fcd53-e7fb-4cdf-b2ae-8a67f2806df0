import { CreateTypeDto } from './dto/create-type.dto';
import { UpdateTypeDto } from './dto/update-type.dto';
import { Type } from './entities/type.entity';
import { GetTypesDto } from './dto/get-types.dto';
export declare class TypesService {
    private typeModel;
    constructor(typeModel: typeof Type);
    getTypes({ text, search }: GetTypesDto): Promise<Type[]>;
    getTypeBySlug(slug: string): Promise<Type | null>;
    create(createTypeDto: CreateTypeDto): Promise<Type>;
    findAll(): string;
    findOne(id: number): string;
    update(id: number, updateTypeDto: UpdateTypeDto): Promise<Type | null>;
    remove(id: number): string;
}
