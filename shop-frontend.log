  ▲ Next.js 13.5.6
  - Local:        http://localhost:3005
  - Environments: .env
  - Experiments (use at your own risk):
     · cpus

 ✓ Ready in 1875ms
Browserslist: browsers data (caniuse-lite) is 9 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
 ○ Compiling /[[...pages]] ...
 ✓ Compiled /[[...pages]] in 8.9s (2393 modules)
Generating static paths for locales: [ 'en' ]
react-i18next:: You will need to pass in an i18next instance by using initReactI18next
Warning: <PERSON>act does not recognize the `fetchPriority` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `fetchpriority` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
    at img
    at eval (webpack-internal:///./node_modules/next/dist/client/image-component.js:131:11)
    at eval (webpack-internal:///./node_modules/next/dist/client/image-component.js:238:47)
    at span
    at a
    at LinkComponent (webpack-internal:///./node_modules/next/dist/client/link.js:105:19)
    at Link (webpack-internal:///./src/components/ui/link.tsx:14:17)
    at Logo (webpack-internal:///./src/components/ui/logo.tsx:22:17)
    at div
    at div
    at div
    at header
    at Header (webpack-internal:///./src/components/layouts/header.tsx:111:19)
    at div
    at HomeLayout (webpack-internal:///./src/components/layouts/_home.tsx:39:23)
    at NotificationProvider (webpack-internal:///./src/context/notify-content.tsx:22:33)
    at main
    at Maintenance (webpack-internal:///./src/components/maintenance/layout.tsx:54:24)
    at CartProvider (webpack-internal:///./src/store/quick-cart/cart.context.tsx:41:76)
    at ModalProvider (webpack-internal:///./src/components/ui/modal/modal.context.tsx:42:26)
    at SearchProvider (webpack-internal:///./src/components/ui/search/search.context.tsx:22:77)
    at Hydrate (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\shop\node_modules\react-query\lib\react\Hydrate.js:33:23)
    at QueryClientProvider (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\shop\node_modules\react-query\lib\react\QueryClientProvider.js:45:21)
    at QueryProvider (webpack-internal:///./src/framework/rest/client/query-provider.tsx:20:26)
    at SessionProvider (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\shop\node_modules\next-auth\react\index.js:364:24)
    at div
    at CustomApp (webpack-internal:///./src/pages/_app.tsx:61:22)
    at I18nextProvider (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\shop\node_modules\react-i18next\dist\commonjs\I18nextProvider.js:11:5)
    at AppWithTranslation (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\shop\node_modules\next-i18next\dist\commonjs\appWithTranslation.js:73:22)
    at StyleRegistry (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\shop\node_modules\styled-jsx\dist\index\index.js:449:36)
    at ek (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\shop\node_modules\next\dist\compiled\next-server\pages.runtime.dev.js:30:13126)
    at eY (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\shop\node_modules\next\dist\compiled\next-server\pages.runtime.dev.js:39:1766)
    at eV (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\shop\node_modules\next\dist\compiled\next-server\pages.runtime.dev.js:39:3110)
    at div
    at e1 (E:\Projects\BB\Projects\e-commerce\logorithm-e-site\shop\node_modules\next\dist\compiled\next-server\pages.runtime.dev.js:48:761)
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 ○ Compiling /404 ...
 ✓ Compiled /404 in 3.5s (2397 modules)
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
Generating static paths for locales: [ 'en' ]
Generating static paths for locales: [ 'en' ]
Generating static paths for locales: [ 'en' ]
Generating static paths for locales: [ 'en' ]
Generating static paths for locales: [ 'en' ]
Generating static paths for locales: [ 'en' ]
Generating static paths for locales: [ 'en' ]
Generating static paths for locales: [ 'en' ]
Generating static paths for locales: [ 'en' ]
Generating static paths for locales: [ 'en' ]
Generating static paths for locales: [ 'en' ]
