{"c": ["webpack"], "r": ["pages/products", "pages/my-shops", "pages/authors", "pages/manufacturers", "pages/attributes", "pages/tags", "pages/categories", "pages/categories/create", "src_components_dashboard_shops_shops_tsx", "src_components_dashboard_shops_message_tsx", "src_components_dashboard_shops_store-notices_tsx", "src_components_dashboard_widgets_box_widget-order-by-status_tsx", "src_components_dashboard_widgets_table_widget-product-count-by-category_tsx", "src_components_dashboard_widgets_box_widget-top-rate-product_tsx", "node_modules_react-apexcharts_dist_react-apexcharts_min_js"], "m": ["./node_modules/@headlessui/react/dist/components/disclosure/disclosure.js", "./node_modules/@headlessui/react/dist/components/popover/popover.js", "./node_modules/@headlessui/react/dist/utils/start-transition.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CProjects%5CBB%5CProjects%5Ce-commerce%5Clogorithm-e-site%5Cadmin-rest%5Csrc%5Cpages%5Cproducts%5Cindex.tsx&page=%2Fproducts!", "./src/components/filters/category-type-filter.tsx", "./src/components/icons/arrow-down.tsx", "./src/components/icons/arrow-up.tsx", "./src/components/icons/chevronDownIcon.tsx", "./src/components/icons/toggle-icon.tsx", "./src/components/product/product-list.tsx", "./src/components/ui/lang-action/action.tsx", "./src/components/ui/lang-action/lang-list-box.tsx", "./src/components/ui/lang-action/language-switcher.tsx", "./src/components/ui/popover.tsx", "./src/pages/products/index.tsx", "__barrel_optimize__?names=Disclosure!=!./node_modules/@headlessui/react/dist/headlessui.esm.js", "__barrel_optimize__?names=Popover!=!./node_modules/@headlessui/react/dist/headlessui.esm.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CProjects%5CBB%5CProjects%5Ce-commerce%5Clogorithm-e-site%5Cadmin-rest%5Csrc%5Cpages%5Cmy-shops.tsx&page=%2Fmy-shops!", "./src/components/dashboard/owner.tsx", "./src/components/icons/ios-arrow-down.tsx", "./src/components/icons/ios-arrow-up.tsx", "./src/components/icons/summary/basket.tsx", "./src/components/icons/summary/checklist.tsx", "./src/components/icons/summary/earning.tsx", "./src/components/icons/summary/shopping.tsx", "./src/components/ui/chart.tsx", "./src/components/widgets/column-chart.tsx", "./src/components/widgets/sticker-card.tsx", "./src/data/client/dashboard.ts", "./src/data/dashboard.ts", "./src/pages/my-shops.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CProjects%5CBB%5CProjects%5Ce-commerce%5Clogorithm-e-site%5Cadmin-rest%5Csrc%5Cpages%5Cauthors%5Cindex.tsx&page=%2Fauthors!", "./src/components/author/author-list.tsx", "./src/pages/authors/index.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CProjects%5CBB%5CProjects%5Ce-commerce%5Clogorithm-e-site%5Cadmin-rest%5Csrc%5Cpages%5Cmanufacturers%5Cindex.tsx&page=%2Fmanufacturers!", "./src/components/manufacturer/manufacturer-list.tsx", "./src/pages/manufacturers/index.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CProjects%5CBB%5CProjects%5Ce-commerce%5Clogorithm-e-site%5Cadmin-rest%5Csrc%5Cpages%5Cattributes%5Cindex.tsx&page=%2Fattributes!", "./src/components/attribute/attribute-list.tsx", "./src/pages/attributes/index.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CProjects%5CBB%5CProjects%5Ce-commerce%5Clogorithm-e-site%5Cadmin-rest%5Csrc%5Cpages%5Ctags%5Cindex.tsx&page=%2Ftags!", "./src/components/category/type-filter.tsx", "./src/components/tag/tag-list.tsx", "./src/pages/tags/index.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CProjects%5CBB%5CProjects%5Ce-commerce%5Clogorithm-e-site%5Cadmin-rest%5Csrc%5Cpages%5Ccategories%5Cindex.tsx&page=%2Fcategories!", "./src/components/category/category-list.tsx", "./src/components/icons/category/accessories.tsx", "./src/components/icons/category/baby-care.tsx", "./src/components/icons/category/bath-oil.tsx", "./src/components/icons/category/beauty-care.tsx", "./src/components/icons/category/beauty-health.tsx", "./src/components/icons/category/bed.tsx", "./src/components/icons/category/beverage.tsx", "./src/components/icons/category/blender.tsx", "./src/components/icons/category/book-shelf.tsx", "./src/components/icons/category/breakfast.tsx", "./src/components/icons/category/camera.tsx", "./src/components/icons/category/center-table.tsx", "./src/components/icons/category/chair.tsx", "./src/components/icons/category/console.tsx", "./src/components/icons/category/contraceptive.tsx", "./src/components/icons/category/cooking.tsx", "./src/components/icons/category/dairy.tsx", "./src/components/icons/category/deodorant.tsx", "./src/components/icons/category/diapers.tsx", "./src/components/icons/category/dressing-table.tsx", "./src/components/icons/category/eyes.tsx", "./src/components/icons/category/face-skin-care.tsx", "./src/components/icons/category/face.tsx", "./src/components/icons/category/facial-care.tsx", "./src/components/icons/category/feeders.tsx", "./src/components/icons/category/feminine-hygiene.tsx", "./src/components/icons/category/first-aid-kit.tsx", "./src/components/icons/category/fruits-vegetable.tsx", "./src/components/icons/category/gadget-accessories.tsx", "./src/components/icons/category/hand-bag.tsx", "./src/components/icons/category/headphone.tsx", "./src/components/icons/category/health-protein.tsx", "./src/components/icons/category/health-wellness.tsx", "./src/components/icons/category/herb.tsx", "./src/components/icons/category/home-cleaning.tsx", "./src/components/icons/category/index.tsx", "./src/components/icons/category/indoor-plants.tsx", "./src/components/icons/category/laptop-bag.tsx", "./src/components/icons/category/laptop.tsx", "./src/components/icons/category/lips.tsx", "./src/components/icons/category/meat-fish.tsx", "./src/components/icons/category/microwave.tsx", "./src/components/icons/category/mobile.tsx", "./src/components/icons/category/monitor.tsx", "./src/components/icons/category/oral-care.tsx", "./src/components/icons/category/oral.tsx", "./src/components/icons/category/orchid.tsx", "./src/components/icons/category/outer-wear.tsx", "./src/components/icons/category/pants.tsx", "./src/components/icons/category/pet-care.tsx", "./src/components/icons/category/pregnancy.tsx", "./src/components/icons/category/purse.tsx", "./src/components/icons/category/reading-table.tsx", "./src/components/icons/category/relax-chair.tsx", "./src/components/icons/category/router.tsx", "./src/components/icons/category/seeds.tsx", "./src/components/icons/category/sexual-wellbeing.tsx", "./src/components/icons/category/shaving-needs.tsx", "./src/components/icons/category/shirts.tsx", "./src/components/icons/category/shoulder-bag.tsx", "./src/components/icons/category/skirts.tsx", "./src/components/icons/category/smart-watch.tsx", "./src/components/icons/category/snacks.tsx", "./src/components/icons/category/sofa.tsx", "./src/components/icons/category/sound-box.tsx", "./src/components/icons/category/storage.tsx", "./src/components/icons/category/succulent.tsx", "./src/components/icons/category/table.tsx", "./src/components/icons/category/tiny-veg.tsx", "./src/components/icons/category/tools.tsx", "./src/components/icons/category/tops.tsx", "./src/components/icons/category/wallet.tsx", "./src/components/icons/category/washing-machine.tsx", "./src/components/icons/category/women-dress.tsx", "./src/pages/categories/index.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=E%3A%5CProjects%5CBB%5CProjects%5Ce-commerce%5Clogorithm-e-site%5Cadmin-rest%5Csrc%5Cpages%5Ccategories%5Ccreate.tsx&page=%2Fcategories%2Fcreate!", "./src/components/category/category-ai-prompt.ts", "./src/components/category/category-form.tsx", "./src/components/category/category-icons.ts", "./src/components/category/category-validation-schema.ts", "./src/pages/categories/create.tsx", "./node_modules/lodash/isNumber.js", "./src/components/dashboard/shops/shops.tsx", "./src/components/shop/shop-avatar.tsx", "./src/components/shop/shop-card.tsx", "./src/components/ui/not-found.tsx", "./src/utils/format-address.tsx", "./node_modules/react-content-loader/dist/react-content-loader.es.js", "./src/components/dashboard/shops/message.tsx", "./src/components/icons/back-icon.tsx", "./src/components/icons/empty-inbox.tsx", "./src/components/icons/select-conversation.tsx", "./src/components/icons/send-message.tsx", "./src/components/message/content-loader.tsx", "./src/components/message/index.tsx", "./src/components/message/user-box-header.tsx", "./src/components/message/user-list-index.tsx", "./src/components/message/user-list.tsx", "./src/components/message/user-message-index.tsx", "./src/components/message/views/conversation-not-found.tsx", "./src/components/message/views/form-view.tsx", "./src/components/message/views/header-view.tsx", "./src/components/message/views/list-view.tsx", "./src/components/message/views/message-view.tsx", "./src/components/message/views/no-message-found.tsx", "./src/components/message/views/responsive-vew.tsx", "./src/components/message/views/select-conversation.tsx", "./src/components/dashboard/shops/store-notices.tsx", "./src/components/icons/no-shop.tsx", "./src/components/store-notice/store-notice-card.tsx", "./src/components/dashboard/widgets/box/widget-order-by-status.tsx", "./src/components/icons/summary/customers.tsx", "./src/components/icons/summary/order-processed.tsx", "./src/components/dashboard/widgets/table/widget-product-count-by-category.tsx", "./src/components/dashboard/widgets/box/widget-top-rate-product.tsx", "./src/components/icons/star-icon.tsx", "./node_modules/apexcharts/dist/apexcharts.common.js", "./node_modules/react-apexcharts/dist/react-apexcharts.min.js"]}