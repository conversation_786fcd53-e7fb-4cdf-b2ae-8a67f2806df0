import { CreateCategoryDto } from './dto/create-category.dto';
import { GetCategoriesDto } from './dto/get-categories.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { Category } from './entities/category.entity';
export declare class CategoriesService {
    private categoryModel;
    constructor(categoryModel: typeof Category);
    create(createCategoryDto: CreateCategoryDto): Promise<Category>;
    getCategories({ limit, page, search, parent }: GetCategoriesDto): Promise<{
        data: Category[];
        count: number;
        current_page: number;
        firstItem: number;
        lastItem: number;
        last_page: number;
        per_page: number;
        total: number;
        first_page_url: string;
        last_page_url: string;
        next_page_url: string;
        prev_page_url: string;
    }>;
    getCategory(param: string, language: string): Promise<Category>;
    update(id: number, updateCategoryDto: UpdateCategoryDto): Promise<[number, Category[]]>;
    remove(id: number): Promise<number>;
}
